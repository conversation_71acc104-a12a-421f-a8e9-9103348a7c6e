import React, { useEffect, useState } from 'react'
import { Route, Routes } from 'react-router-dom'
import './App.css'
import { BookmarkGrid } from './components/BookmarkGrid'
import { DragDropZone } from './components/DragDropZone'
import { Header } from './components/Header'
import { Sidebar } from './components/Sidebar'
import { PanelType, TabbedRightPanel } from './components/TabbedRightPanel'
import { BookmarkProvider, useBookmarks } from './contexts/BookmarkContext'
import { LocalizationProvider } from './contexts/LocalizationContext'
import { ModernThemeProvider } from './contexts/ModernThemeContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { ThemeIntegration } from './contexts/ThemeIntegration'
import UIShowcasePage from './pages/UIShowcase'
import './styles/memory-optimization.css'
import './styles/modernThemeToggle.css'
import './styles/multimedia-integration.css'
import './themes/modernTheme.css'
// Import playlist demo for development testing
import './utils/playlistDemo'
// Import protection systems to prevent console spam and memory issues
import './utils/consoleProtection'
import './utils/intervalManager'
import './utils/memoryProtection'
import './utils/silentEmergencyCleanup'
import './utils/userFriendlyConsole'
// Import favicon service to initialize error suppression
import './services/faviconService'
// Smart favicon system provides memory-efficient favicon loading
console.log('🎯 Smart favicon system initialized - Memory-efficient favicon loading enabled');

// Generate favicon URL from domain
const generateFaviconUrl = (url: string): string => {
  try {
    const domain = new URL(url).hostname
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
  } catch {
    return `https://www.google.com/s2/favicons?domain=example.com&sz=32`
  }
}



function AppContent() {
  const {
    addBookmarkFromDrop,
    hasUnsavedChanges,
    saveBookmarks,
    filteredBookmarks,
    // selectedCollection,
    // filterType,
    // searchQuery,
    // selectedTags,
    // selectedPlaylist,
    isLoading
  } = useBookmarks()
  const [prevUnsavedState, setPrevUnsavedState] = React.useState(false)

  // Toast functionality - moved here to avoid initialization order issues
  const [toasts, setToasts] = useState<Array<{
    id: string
    message: string
    type?: 'success' | 'error' | 'info' | 'warning'
    duration?: number
  }>>([])

  const toastTimeoutsRef = React.useRef<Map<string, NodeJS.Timeout>>(new Map())
  const maxToasts = 5 // Limit concurrent toasts to prevent memory accumulation

  const removeToast = React.useCallback((id: string) => {
    // Clear timeout if it exists
    const timeout = toastTimeoutsRef.current.get(id)
    if (timeout) {
      clearTimeout(timeout)
      toastTimeoutsRef.current.delete(id)
    }

    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const addToast = React.useCallback((message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info', duration = 3000) => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    setToasts(prev => {
      // Remove oldest toasts if we exceed the limit
      const newToasts = prev.length >= maxToasts ? prev.slice(-(maxToasts - 1)) : prev
      return [...newToasts, { id, message, type, duration }]
    })

    // Auto-remove after duration with proper cleanup
    const timeout = setTimeout(() => {
      removeToast(id)
    }, duration)

    toastTimeoutsRef.current.set(id, timeout)
  }, [removeToast])

  // Toast cleanup effect
  React.useEffect(() => {
    const toastTimeouts = toastTimeoutsRef.current
    return () => {
      toastTimeouts.forEach(timeout => clearTimeout(timeout))
      toastTimeouts.clear()
    }
  }, [])

  // Refs needed by handlePaste - moved here to avoid initialization order issues
  const dragSuccessTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)

  // Handle paste functionality - moved here to avoid initialization order issues
  const handlePaste = React.useCallback(async () => {
    try {
      const clipboardText = await navigator.clipboard.readText()
      const url = clipboardText.trim()

      // Check if it's a valid URL
      if (url && (url.startsWith('http://') || url.startsWith('https://') || url.includes('.'))) {
        let finalUrl = url

        // Add https:// if no protocol
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          finalUrl = `https://${url}`
        }

        // Validate URL
        try {
          new URL(finalUrl)
        } catch {
          addToast('Invalid URL in clipboard', 'error')
          return
        }

        // Extract title from URL
        let title = ''
        try {
          const urlObj = new URL(finalUrl)
          title = urlObj.hostname.replace('www.', '')
        } catch {
          title = 'Pasted Bookmark'
        }

        // Create the bookmark
        const newBookmark = {
          title,
          url: finalUrl,
          favicon: generateFaviconUrl(finalUrl),
          description: '',
          tags: [],
          collection: 'Quick Add',
          dateAdded: new Date().toISOString(),
          isFavorite: false,
          visits: 0,
          isRecentlyAdded: true, // Show "NEW" indicators for drag & drop
          addedTimestamp: new Date().toISOString(), // Track as recently added
          canRevert: true // Allow revert for drag & drop
        }

        await addBookmarkFromDrop(newBookmark)
        // setLastAddedTitle(title)
        addToast(`Bookmark "${title}" added from clipboard!`, 'success')

        // Clear success message after 3 seconds
        if (dragSuccessTimeoutRef.current) {
          clearTimeout(dragSuccessTimeoutRef.current)
        }
        dragSuccessTimeoutRef.current = setTimeout(() => {
          // setLastAddedTitle(null)
        }, 3000)
      } else {
        addToast('No valid URL found in clipboard', 'warning')
      }
    } catch (error) {
      console.error('Error reading clipboard:', error)
      addToast('Unable to read clipboard. Try using Ctrl+V again.', 'error')
    }
  }, [addToast, addBookmarkFromDrop])

  // Debug: Log what the debug panel is receiving
  React.useEffect(() => {
    console.warn('🐛 DEBUG PANEL:',
      // 'bookmarkData=' + (bookmarkData ? 'EXISTS' : 'NULL'),
      // 'bookmarkDataBookmarks=' + (bookmarkData?.bookmarks?.length || 0),
      'filteredBookmarks=' + (filteredBookmarks?.length || 0),
      'isLoading=' + isLoading
    )
  }, [filteredBookmarks, isLoading])



  // Modal manager will be implemented later

  // Detect when save is completed (unsaved changes go from true to false)
  React.useEffect(() => {
    if (prevUnsavedState && !hasUnsavedChanges) {
      // Save was completed
      addToast('Bookmarks saved and exported to HTML file!', 'success')
    }
    setPrevUnsavedState(hasUnsavedChanges)
  }, [hasUnsavedChanges, prevUnsavedState, addToast])

  // Keyboard shortcuts for saving (Ctrl+S / Cmd+S) and paste (Ctrl+V / Cmd+V)
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (hasUnsavedChanges) {
          saveBookmarks()
        }
      }

      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        // Only handle paste if not in an input field
        const target = e.target as HTMLElement
        if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA' && !target.isContentEditable) {
          e.preventDefault()
          handlePaste()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [hasUnsavedChanges, saveBookmarks, handlePaste])



  // Update document title to show unsaved changes
  React.useEffect(() => {
    const baseTitle = 'Bookmark Studio'
    document.title = hasUnsavedChanges ? `${baseTitle} • Unsaved Changes` : baseTitle
  }, [hasUnsavedChanges])

  // Remove conflicting beforeunload listener - handled in BookmarkContext
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // Tabbed panel state
  const [activePanels, setActivePanels] = useState<PanelType[]>([])
  const [activeTab, setActiveTab] = useState<PanelType | null>(null)

  // Panel management functions
  const openPanel = (panelType: PanelType) => {
    setActivePanels(prev => {
      if (!prev.includes(panelType)) {
        return [...prev, panelType]
      }
      return prev
    })
    setActiveTab(panelType)
  }

  const closePanel = (panelType: PanelType) => {
    setActivePanels(prev => {
      const newPanels = prev.filter(p => p !== panelType)
      if (activeTab === panelType) {
        setActiveTab(newPanels.length > 0 ? newPanels[newPanels.length - 1] : null)
      }
      return newPanels
    })
  }

  const togglePanel = (panelType: PanelType) => {
    if (activePanels.includes(panelType)) {
      closePanel(panelType)
    } else {
      openPanel(panelType)
    }
  }

  const closeAllPanels = () => {
    setActivePanels([])
    setActiveTab(null)
  }

  const [isDragActive, setIsDragActive] = useState(false)
  const isDragActiveRef = React.useRef(isDragActive)
  // const [lastAddedTitle, setLastAddedTitle] = useState<string | null>(null)

  // Keep ref in sync with state
  React.useEffect(() => {
    isDragActiveRef.current = isDragActive
  }, [isDragActive])
  // const [contextMenu, setContextMenu] = useState<{x: number, y: number, visible: boolean}>({x: 0, y: 0, visible: false})

  // Drag success timeout cleanup
  React.useEffect(() => {
    const dragSuccessTimeout = dragSuccessTimeoutRef.current
    return () => {
      if (dragSuccessTimeout) {
        clearTimeout(dragSuccessTimeout)
      }
    }
  }, [])

  // Global drag event listeners for drag-to-add functionality
  useEffect(() => {
    let dragCounter = 0

    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()

      dragCounter++
      console.log('🌍 DRAG ENTER - types:', e.dataTransfer?.types, 'counter:', dragCounter)
      console.log('🌍 DRAG ENTER - target:', e.target, 'currentTarget:', e.currentTarget)

      // Check if the drag contains URL or file data
      const hasUrl = e.dataTransfer?.types.includes('text/uri-list') ||
        e.dataTransfer?.types.includes('text/plain') ||
        e.dataTransfer?.types.includes('URL')

      const hasFiles = e.dataTransfer?.types.includes('Files')

      console.log('🌍 DRAG ENTER - hasUrl:', hasUrl, 'hasFiles:', hasFiles, 'isDragActive:', isDragActiveRef.current)

      if ((hasUrl || hasFiles) && !isDragActiveRef.current) {
        console.log('✅ SETTING DRAG ACTIVE - TRUE')
        setIsDragActive(true)
      }
    }

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()

      // Add periodic logging to see if dragover is working
      if (Math.random() < 0.1) { // Log only 10% of dragover events to avoid spam
        console.log('🌍 DRAG OVER - types:', e.dataTransfer?.types, 'isDragActive:', isDragActiveRef.current)
      }
    }

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault()
      dragCounter--
      console.log('🌍 Drag leave, counter:', dragCounter)

      // Only hide if we've left all elements (counter reaches 0 or below)
      if (dragCounter <= 0) {
        dragCounter = 0
        console.log('🚪 Drag left the window')
        setIsDragActive(false)
      }
    }

    const handleDrop = (e: DragEvent) => {
      // Don't prevent default here - let DragDropZone handle the actual drop
      dragCounter = 0
      console.log('🎯 GLOBAL DROP DETECTED - delegating to DragDropZone')
      console.log('🎯 DROP - types:', e.dataTransfer?.types)
      console.log('🎯 DROP - target:', e.target, 'currentTarget:', e.currentTarget)

      // Only reset drag state after a delay to allow DragDropZone to process
      setTimeout(() => {
        setIsDragActive(false)
        console.log('🎯 DRAG STATE RESET - isDragActive set to false')
      }, 100)
    }

    // Add event listeners to document
    document.addEventListener('dragenter', handleDragEnter)
    document.addEventListener('dragover', handleDragOver)
    document.addEventListener('dragleave', handleDragLeave)
    document.addEventListener('drop', handleDrop)

    return () => {
      document.removeEventListener('dragenter', handleDragEnter)
      document.removeEventListener('dragover', handleDragOver)
      document.removeEventListener('dragleave', handleDragLeave)
      document.removeEventListener('drop', handleDrop)
    }
  }, [])

  // DISABLED: Context menu functionality was causing memory issues

  const BookmarkManagerApp = () => (
    <div className="bookmark-manager">
      <Sidebar
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
        onToggleHealthCheck={() => togglePanel('health-check')}
        onToggleSummaries={() => togglePanel('summaries')}
        onToggleHybrid={() => togglePanel('hybrid')}
        onToggleSmartAI={() => togglePanel('smart-ai')}
        onToggleDomain={() => togglePanel('domain')}
        onToggleContent={() => togglePanel('content')}
        onToggleMultimedia={() => togglePanel('multimedia')}
        // Active state props
        healthCheckPanelOpen={activePanels.includes('health-check')}
        summariesPanelOpen={activePanels.includes('summaries')}
        hybridPanelOpen={activePanels.includes('hybrid')}
        smartAIPanelOpen={activePanels.includes('smart-ai')}
        domainPanelOpen={activePanels.includes('domain')}
        contentPanelOpen={activePanels.includes('content')}
        multimediaPanelOpen={activePanels.includes('multimedia')}
      />

      <div className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Header
          onToggleImport={() => togglePanel('import')}
          onToggleExport={() => togglePanel('export')}
          onToggleSplit={() => togglePanel('split')}
          onTogglePlaylist={() => togglePanel('playlist')}
          importPanelOpen={activePanels.includes('import')}
          exportPanelOpen={activePanels.includes('export')}
          splitPanelOpen={activePanels.includes('split')}
          playlistPanelOpen={activePanels.includes('playlist')}
        />

        <div className="content-wrapper">
          <BookmarkGrid onOpenPanel={openPanel} />
          {/* Add Bookmark Modal - will be implemented later */}
        </div>

        {/* Tabbed Right Panel */}
        <TabbedRightPanel
          activePanels={activePanels}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onCloseTab={closePanel}
          onCloseAll={closeAllPanels}
        />
      </div>

      {/* Drag & Drop Zone - Always render to preserve modal state */}
      <DragDropZone
        isActive={isDragActive}
        onDragStateChange={setIsDragActive}
      />

      {/* Toast Notifications */}
      {toasts.length > 0 && (
        <div className="toast-container">
          {toasts.map(toast => (
            <div key={toast.id} className={`toast toast-${toast.type} toast-visible`}>
              <div className="toast-icon">
                {toast.type === 'success' && '✅'}
                {toast.type === 'error' && '❌'}
                {toast.type === 'warning' && '⚠️'}
                {toast.type === 'info' && 'ℹ️'}
              </div>
              <div className="toast-message">{toast.message}</div>
              <button
                onClick={() => removeToast(toast.id)}
                className="toast-close"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Context Menu DISABLED for memory optimization */}

      {/* Debug panel temporarily removed - testing core functionality */}
    </div>
  )

  return (
    <Routes>
      <Route path="/" element={<BookmarkManagerApp />} />
      <Route path="/ui-showcase" element={<UIShowcasePage />} />
    </Routes>
  )
}

function App() {
  return (
    <LocalizationProvider>
      <ModernThemeProvider>
        <ThemeProvider>
          <ThemeIntegration>
            <BookmarkProvider>
              <AppContent />
            </BookmarkProvider>
          </ThemeIntegration>
        </ThemeProvider>
      </ModernThemeProvider>
    </LocalizationProvider>
  )
}

export default App
