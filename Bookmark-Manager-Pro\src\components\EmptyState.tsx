import { BookOpen, Filter, Search } from 'lucide-react'
import React from 'react'

interface EmptyStateProps {
  searchQuery: string
  selectedCollection: string
  onImportClick?: () => void
  onClearFilters?: () => void
  onViewAllClick?: () => void
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  searchQuery,
  selectedCollection,
  onImportClick,
  onClearFilters,
  onViewAllClick
}) => {
  const isSearching = searchQuery.length > 0
  const isFiltered = selectedCollection !== 'all'

  if (isSearching) {
    return (
      <div className="empty-state">
        <div className="empty-icon">
          <Search size={48} />
        </div>
        <h3 className="empty-title">No bookmarks found</h3>
        <p className="empty-description">
          No bookmarks match your search for "{searchQuery}".
          <br />
          Try searching for something else or check your spelling.
        </p>
        <div className="empty-actions">
          <button onClick={onClearFilters} className="btn-secondary">
            <Filter size={16} />
            Clear filters
          </button>
          <div className="drag-paste-hint">
            <Search size={16} />
            Drag or paste a URL to bookmark
          </div>
        </div>
      </div>
    )
  }

  if (isFiltered) {
    return (
      <div className="empty-state">
        <div className="empty-icon">
          <BookOpen size={48} />
        </div>
        <h3 className="empty-title">No bookmarks in this collection</h3>
        <p className="empty-description">
          The "{selectedCollection}" collection is empty.
          <br />
          Start adding bookmarks to organize your links.
        </p>
        <div className="empty-actions">
          <button onClick={onViewAllClick} className="btn-secondary">
            <BookOpen size={16} />
            View all bookmarks
          </button>
          <div className="drag-paste-hint">
            <Search size={16} />
            Drag or paste a URL to bookmark
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="empty-state">
      <div className="empty-icon">
        <BookOpen size={48} />
      </div>
      <h3 className="empty-title">Welcome to Bookmark Studio</h3>
      <p className="empty-description">
        You don't have any bookmarks yet.
        <br />
        Start building your collection by adding your first bookmark.
      </p>
      <div className="empty-actions">
        <button onClick={onImportClick} className="btn-secondary">
          <BookOpen size={16} />
          Import bookmarks
        </button>
        <div className="drag-paste-hint primary">
          <Search size={16} />
          Drag or paste a URL to bookmark
        </div>
      </div>
    </div>
  )
}
