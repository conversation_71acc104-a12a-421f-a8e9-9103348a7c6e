#!/usr/bin/env node

/**
 * Mobile Friendliness Validation Script
 * Automated testing for mobile responsiveness and touch optimization
 * Target: Achieve 9/10 mobile friendliness score
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Configuration
const CONFIG = {
  breakpoints: {
    mobile: { width: 375, height: 667 },
    mobileLarge: { width: 414, height: 896 },
    tablet: { width: 768, height: 1024 },
    tabletLarge: { width: 1024, height: 768 },
    desktop: { width: 1280, height: 720 }
  },
  touchTargets: {
    minimum: 44,
    comfortable: 48,
    large: 56
  },
  performance: {
    targetLighthouseScore: 90,
    maxLoadTime: 3000,
    maxFirstContentfulPaint: 1500
  }
};

// Test Results Storage
let testResults = {
  timestamp: new Date().toISOString(),
  overall: { passed: 0, failed: 0, score: 0 },
  categories: {
    responsive: { tests: [], passed: 0, failed: 0 },
    touchTargets: { tests: [], passed: 0, failed: 0 },
    performance: { tests: [], passed: 0, failed: 0 },
    accessibility: { tests: [], passed: 0, failed: 0 },
    usability: { tests: [], passed: 0, failed: 0 }
  }
};

// Utility Functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[type]}${message}${colors.reset}`);
}

function addTestResult(category, testName, passed, details = '') {
  const result = { testName, passed, details, timestamp: new Date().toISOString() };
  testResults.categories[category].tests.push(result);
  
  if (passed) {
    testResults.categories[category].passed++;
    testResults.overall.passed++;
  } else {
    testResults.categories[category].failed++;
    testResults.overall.failed++;
  }
}

function calculateScore() {
  const total = testResults.overall.passed + testResults.overall.failed;
  if (total === 0) return 0;
  
  const percentage = (testResults.overall.passed / total) * 100;
  testResults.overall.score = Math.round(percentage / 10); // Convert to 1-10 scale
  return testResults.overall.score;
}

// File System Validation
function validateFileStructure() {
  log('\n🔍 Validating File Structure...', 'info');
  
  const requiredFiles = [
    'src/styles/responsive-foundation.css',
    'src/components/ResponsiveHeader.tsx',
    'src/components/ResponsiveHeader.css',
    'src/components/EnhancedSearchInput.tsx',
    'src/components/EnhancedSearchInput.css'
  ];
  
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    const exists = fs.existsSync(filePath);
    addTestResult('responsive', `File exists: ${file}`, exists, 
      exists ? 'File found' : 'File missing - required for mobile improvements');
    
    if (exists) {
      log(`  ✅ ${file}`, 'success');
    } else {
      log(`  ❌ ${file} - MISSING`, 'error');
    }
  });
}

// CSS Validation
function validateResponsiveCSS() {
  log('\n📱 Validating Responsive CSS...', 'info');
  
  try {
    const foundationCSS = fs.readFileSync(
      path.join(process.cwd(), 'src/styles/responsive-foundation.css'), 
      'utf8'
    );
    
    // Check for required breakpoints
    const requiredBreakpoints = [
      '--breakpoint-xs: 320px',
      '--breakpoint-sm: 480px',
      '--breakpoint-md: 640px',
      '--breakpoint-lg: 768px',
      '--breakpoint-xl: 1024px'
    ];
    
    requiredBreakpoints.forEach(breakpoint => {
      const hasBreakpoint = foundationCSS.includes(breakpoint);
      addTestResult('responsive', `Breakpoint defined: ${breakpoint}`, hasBreakpoint,
        hasBreakpoint ? 'Breakpoint found' : 'Missing critical breakpoint');
    });
    
    // Check for touch target variables
    const touchTargetVars = [
      '--touch-target-min: 44px',
      '--touch-target-comfortable: 48px',
      '--touch-target-large: 56px'
    ];
    
    touchTargetVars.forEach(variable => {
      const hasVariable = foundationCSS.includes(variable);
      addTestResult('touchTargets', `Touch target variable: ${variable}`, hasVariable,
        hasVariable ? 'Variable defined' : 'Missing touch target standard');
    });
    
    // Check for mobile-first media queries
    const mobileFirstQueries = [
      '@media (min-width: 480px)',
      '@media (min-width: 640px)',
      '@media (min-width: 768px)'
    ];
    
    mobileFirstQueries.forEach(query => {
      const hasQuery = foundationCSS.includes(query);
      addTestResult('responsive', `Mobile-first query: ${query}`, hasQuery,
        hasQuery ? 'Mobile-first approach confirmed' : 'Missing mobile-first media query');
    });
    
    log('  ✅ Responsive foundation CSS validated', 'success');
    
  } catch (error) {
    log(`  ❌ Error reading responsive foundation CSS: ${error.message}`, 'error');
    addTestResult('responsive', 'CSS file readable', false, error.message);
  }
}

// Component Validation
function validateComponents() {
  log('\n⚛️ Validating React Components...', 'info');
  
  try {
    // Validate ResponsiveHeader
    const headerComponent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/ResponsiveHeader.tsx'),
      'utf8'
    );
    
    const headerRequirements = [
      'interface ResponsiveHeaderProps',
      'useState',
      'useEffect',
      'onSearch',
      'searchQuery',
      'isSearching'
    ];
    
    headerRequirements.forEach(requirement => {
      const hasRequirement = headerComponent.includes(requirement);
      addTestResult('responsive', `Header component: ${requirement}`, hasRequirement,
        hasRequirement ? 'Component feature found' : 'Missing required component feature');
    });
    
    // Validate EnhancedSearchInput
    const searchComponent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/EnhancedSearchInput.tsx'),
      'utf8'
    );
    
    const searchRequirements = [
      'interface EnhancedSearchInputProps',
      'recentSearches',
      'suggestions',
      'onSubmit',
      'isLoading',
      'size?:'
    ];
    
    searchRequirements.forEach(requirement => {
      const hasRequirement = searchComponent.includes(requirement);
      addTestResult('touchTargets', `Search component: ${requirement}`, hasRequirement,
        hasRequirement ? 'Search feature found' : 'Missing search component feature');
    });
    
    log('  ✅ React components validated', 'success');
    
  } catch (error) {
    log(`  ❌ Error validating components: ${error.message}`, 'error');
    addTestResult('responsive', 'Component files readable', false, error.message);
  }
}

// Accessibility Validation
function validateAccessibility() {
  log('\n♿ Validating Accessibility Features...', 'info');
  
  try {
    const searchCSS = fs.readFileSync(
      path.join(process.cwd(), 'src/components/EnhancedSearchInput.css'),
      'utf8'
    );
    
    const a11yFeatures = [
      '@media (prefers-reduced-motion: reduce)',
      '@media (prefers-contrast: high)',
      'outline: 2px solid',
      'outline-offset',
      ':focus'
    ];
    
    a11yFeatures.forEach(feature => {
      const hasFeature = searchCSS.includes(feature);
      addTestResult('accessibility', `A11y feature: ${feature}`, hasFeature,
        hasFeature ? 'Accessibility feature implemented' : 'Missing accessibility feature');
    });
    
    // Check for ARIA attributes in components
    const headerComponent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/ResponsiveHeader.tsx'),
      'utf8'
    );
    
    const ariaFeatures = [
      'aria-label',
      'aria-expanded',
      'role=',
      'tabIndex'
    ];
    
    ariaFeatures.forEach(feature => {
      const hasFeature = headerComponent.includes(feature);
      addTestResult('accessibility', `ARIA feature: ${feature}`, hasFeature,
        hasFeature ? 'ARIA attribute found' : 'Missing ARIA attribute');
    });
    
    log('  ✅ Accessibility features validated', 'success');
    
  } catch (error) {
    log(`  ❌ Error validating accessibility: ${error.message}`, 'error');
    addTestResult('accessibility', 'Accessibility validation', false, error.message);
  }
}

// Performance Validation
function validatePerformance() {
  log('\n⚡ Validating Performance Optimizations...', 'info');
  
  try {
    // Check for CSS optimization patterns
    const foundationCSS = fs.readFileSync(
      path.join(process.cwd(), 'src/styles/responsive-foundation.css'),
      'utf8'
    );
    
    const performanceFeatures = [
      'will-change',
      'transform: translateZ(0)',
      'backface-visibility: hidden',
      '@media print'
    ];
    
    performanceFeatures.forEach(feature => {
      const hasFeature = foundationCSS.includes(feature) || 
                        fs.readFileSync(path.join(process.cwd(), 'src/components/ResponsiveHeader.css'), 'utf8').includes(feature);
      addTestResult('performance', `Performance optimization: ${feature}`, hasFeature,
        hasFeature ? 'Performance optimization found' : 'Performance optimization missing');
    });
    
    // Check for lazy loading patterns
    const searchComponent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/EnhancedSearchInput.tsx'),
      'utf8'
    );
    
    const lazyFeatures = [
      'useCallback',
      'useMemo',
      'debounce',
      'useEffect'
    ];
    
    lazyFeatures.forEach(feature => {
      const hasFeature = searchComponent.includes(feature);
      addTestResult('performance', `React optimization: ${feature}`, hasFeature,
        hasFeature ? 'React optimization found' : 'React optimization missing');
    });
    
    log('  ✅ Performance optimizations validated', 'success');
    
  } catch (error) {
    log(`  ❌ Error validating performance: ${error.message}`, 'error');
    addTestResult('performance', 'Performance validation', false, error.message);
  }
}

// Usability Validation
function validateUsability() {
  log('\n👆 Validating Mobile Usability...', 'info');
  
  try {
    const headerCSS = fs.readFileSync(
      path.join(process.cwd(), 'src/components/ResponsiveHeader.css'),
      'utf8'
    );
    
    const searchCSS = fs.readFileSync(
      path.join(process.cwd(), 'src/components/EnhancedSearchInput.css'),
      'utf8'
    );
    
    // Check for mobile-friendly interactions
    const usabilityFeatures = [
      'touch-action',
      'user-select',
      '-webkit-tap-highlight-color',
      'cursor: pointer'
    ];
    
    usabilityFeatures.forEach(feature => {
      const hasFeature = headerCSS.includes(feature) || searchCSS.includes(feature);
      addTestResult('usability', `Mobile interaction: ${feature}`, hasFeature,
        hasFeature ? 'Mobile interaction optimized' : 'Mobile interaction needs optimization');
    });
    
    // Check for responsive typography
    const typographyFeatures = [
      'font-size: var(--text-',
      'line-height',
      'letter-spacing',
      '@media (min-width:'
    ];
    
    typographyFeatures.forEach(feature => {
      const hasFeature = headerCSS.includes(feature) || searchCSS.includes(feature);
      addTestResult('usability', `Responsive typography: ${feature}`, hasFeature,
        hasFeature ? 'Responsive typography implemented' : 'Typography needs responsive scaling');
    });
    
    log('  ✅ Mobile usability validated', 'success');
    
  } catch (error) {
    log(`  ❌ Error validating usability: ${error.message}`, 'error');
    addTestResult('usability', 'Usability validation', false, error.message);
  }
}

// Generate Report
function generateReport() {
  const score = calculateScore();
  
  log('\n📊 MOBILE FRIENDLINESS VALIDATION REPORT', 'info');
  log('=' .repeat(50), 'info');
  
  // Overall Score
  const scoreColor = score >= 9 ? 'success' : score >= 7 ? 'warning' : 'error';
  log(`\n🎯 OVERALL SCORE: ${score}/10`, scoreColor);
  log(`📈 Tests Passed: ${testResults.overall.passed}`, 'success');
  log(`📉 Tests Failed: ${testResults.overall.failed}`, testResults.overall.failed > 0 ? 'error' : 'info');
  
  // Category Breakdown
  log('\n📋 CATEGORY BREAKDOWN:', 'info');
  Object.entries(testResults.categories).forEach(([category, results]) => {
    const categoryScore = results.tests.length > 0 ? 
      Math.round((results.passed / results.tests.length) * 100) : 0;
    const categoryColor = categoryScore >= 90 ? 'success' : categoryScore >= 70 ? 'warning' : 'error';
    
    log(`\n  ${category.toUpperCase()}:`, 'info');
    log(`    Score: ${categoryScore}% (${results.passed}/${results.tests.length})`, categoryColor);
    
    // Show failed tests
    const failedTests = results.tests.filter(test => !test.passed);
    if (failedTests.length > 0) {
      log(`    Failed Tests:`, 'error');
      failedTests.forEach(test => {
        log(`      ❌ ${test.testName}: ${test.details}`, 'error');
      });
    }
  });
  
  // Recommendations
  log('\n💡 RECOMMENDATIONS:', 'info');
  if (score >= 9) {
    log('  🎉 Excellent! Mobile friendliness target achieved!', 'success');
    log('  ✅ Ready for production deployment', 'success');
  } else if (score >= 7) {
    log('  ⚠️  Good progress, but improvements needed for 9/10 target', 'warning');
    log('  🔧 Focus on failed test categories above', 'warning');
  } else {
    log('  🚨 Significant improvements required', 'error');
    log('  📚 Review implementation guide and fix critical issues', 'error');
  }
  
  // Next Steps
  log('\n🚀 NEXT STEPS:', 'info');
  if (testResults.overall.failed > 0) {
    log('  1. Fix failed tests listed above', 'info');
    log('  2. Re-run validation script', 'info');
    log('  3. Test on real devices', 'info');
    log('  4. Run Lighthouse mobile audit', 'info');
  } else {
    log('  1. Deploy to staging environment', 'info');
    log('  2. Conduct user testing', 'info');
    log('  3. Monitor mobile analytics', 'info');
    log('  4. Plan for continuous improvements', 'info');
  }
  
  // Save detailed report
  const reportPath = path.join(process.cwd(), 'mobile-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'info');
  
  return score;
}

// Main Execution
function main() {
  log('🚀 Starting Mobile Friendliness Validation...', 'info');
  log('Target: 9/10 Mobile Friendliness Score\n', 'info');
  
  try {
    validateFileStructure();
    validateResponsiveCSS();
    validateComponents();
    validateAccessibility();
    validatePerformance();
    validateUsability();
    
    const finalScore = generateReport();
    
    // Exit with appropriate code
    process.exit(finalScore >= 9 ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 Validation failed with error: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run the main function directly
main();

export {
  validateFileStructure,
  validateResponsiveCSS,
  validateComponents,
  validateAccessibility,
  validatePerformance,
  validateUsability,
  generateReport,
  CONFIG
};