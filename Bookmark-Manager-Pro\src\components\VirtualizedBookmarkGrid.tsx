import React, { useEffect, useMemo, useRef, useState } from 'react'
import { FixedSizeGrid as Grid } from 'react-window'
import { useBookmarks } from '../contexts/BookmarkContext'
import { logMemoryUsage } from '../utils/memoryOptimization'
import { BookmarkCardFlip } from './BookmarkCardFlip'

interface VirtualizedBookmarkGridProps {
  searchQuery: string
  selectedCollection: string
  onOpenPanel?: (panelType: string) => void
}

interface GridItemData {
  bookmarks: any[]
  itemsPerRow: number
  itemWidth: number
  itemHeight: number
  onToggleFavorite: (id: string) => void
  onOpenPanel?: (panelType: string) => void
}

interface GridItemProps {
  columnIndex: number
  rowIndex: number
  style: React.CSSProperties
  data: GridItemData
}

// Memoized grid item to prevent unnecessary re-renders
const GridItem: React.FC<GridItemProps> = React.memo(({ columnIndex, rowIndex, style, data }) => {
  const { bookmarks, itemsPerRow, onToggleFavorite, onOpenPanel } = data
  const index = rowIndex * itemsPerRow + columnIndex

  if (index >= bookmarks.length) {
    return <div style={style} />
  }

  const bookmark = bookmarks[index]

  return (
    <div style={style}>
      <div style={{ padding: '8px' }}>
        <BookmarkCardFlip
          bookmark={bookmark}
          onToggleFavorite={onToggleFavorite}
          onOpenPanel={onOpenPanel}
        />
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  // Only re-render if the bookmark data or position has changed
  const prevIndex = prevProps.rowIndex * prevProps.data.itemsPerRow + prevProps.columnIndex
  const nextIndex = nextProps.rowIndex * nextProps.data.itemsPerRow + nextProps.columnIndex

  if (prevIndex !== nextIndex) return false
  if (prevIndex >= prevProps.data.bookmarks.length && nextIndex >= nextProps.data.bookmarks.length) return true
  if (prevIndex >= prevProps.data.bookmarks.length || nextIndex >= nextProps.data.bookmarks.length) return false

  const prevBookmark = prevProps.data.bookmarks[prevIndex]
  const nextBookmark = nextProps.data.bookmarks[nextIndex]

  // Compare essential bookmark properties
  return (
    prevBookmark?.id === nextBookmark?.id &&
    prevBookmark?.title === nextBookmark?.title &&
    prevBookmark?.isFavorite === nextBookmark?.isFavorite &&
    prevProps.data.onToggleFavorite === nextProps.data.onToggleFavorite &&
    prevProps.data.onOpenPanel === nextProps.data.onOpenPanel
  )
})

export const VirtualizedBookmarkGrid: React.FC<VirtualizedBookmarkGridProps> = ({
  searchQuery,
  selectedCollection,
  onOpenPanel
}) => {
  const {
    filteredBookmarks,
    toggleBookmarkFavorite,
    isLoading
  } = useBookmarks()

  // Stable callback functions to prevent unnecessary re-renders
  const stableToggleFavorite = React.useCallback((id: string) => {
    toggleBookmarkFavorite(id)
  }, [toggleBookmarkFavorite])

  const stableOnOpenPanel = React.useCallback((panelType: string) => {
    onOpenPanel?.(panelType)
  }, [onOpenPanel])

  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  // Memory optimization: Only use virtual scrolling for large datasets
  const shouldUseVirtualScrolling = filteredBookmarks.length > 50

  // Monitor DOM node count for debugging
  useEffect(() => {
    const nodeCount = document.querySelectorAll('*').length
    if (nodeCount > 50000) {
      console.warn(`⚠️ High DOM node count: ${nodeCount.toLocaleString()}`)
      console.log(`📊 Bookmarks: ${filteredBookmarks.length}, Virtual scrolling: ${shouldUseVirtualScrolling ? 'ENABLED' : 'DISABLED'}`)
    }
  }, [filteredBookmarks.length, shouldUseVirtualScrolling])

  // Calculate grid dimensions for optimal space utilization
  const itemWidth = 320
  const itemHeight = 280
  const gap = 16

  // Calculate optimal items per row to maximize screen utilization
  const itemsPerRow = Math.max(1, Math.floor((containerSize.width + gap) / (itemWidth + gap)))
  const rowCount = Math.ceil(filteredBookmarks.length / itemsPerRow)

  // Calculate how many rows can fit in the visible area
  const visibleRows = Math.floor(containerSize.height / (itemHeight + gap))
  const totalVisibleItems = itemsPerRow * visibleRows

  // Memory monitoring and grid optimization logging
  useEffect(() => {
    if (filteredBookmarks.length > 1000) {
      logMemoryUsage(`VirtualizedGrid-${filteredBookmarks.length}-bookmarks`)
    }

    // Log grid optimization info for debugging
    if (containerSize.width > 0 && containerSize.height > 0) {
      console.log(`🎯 Grid Optimization: ${itemsPerRow} items/row, ${visibleRows} visible rows, ${totalVisibleItems} total visible items`)
      console.log(`📐 Container: ${containerSize.width}x${containerSize.height}px`)
    }
  }, [filteredBookmarks.length, containerSize, itemsPerRow, visibleRows, totalVisibleItems])

  // Container resize observer
  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver(entries => {
      const entry = entries[0]
      if (entry) {
        setContainerSize({
          width: entry.contentRect.width,
          height: entry.contentRect.height
        })
      }
    })

    resizeObserver.observe(containerRef.current)
    return () => resizeObserver.disconnect()
  }, [])

  // Grid data for react-window with stable callbacks
  const gridData = useMemo<GridItemData>(() => ({
    bookmarks: filteredBookmarks,
    itemsPerRow,
    itemWidth,
    itemHeight,
    onToggleFavorite: stableToggleFavorite,
    onOpenPanel: stableOnOpenPanel
  }), [filteredBookmarks, itemsPerRow, itemWidth, itemHeight, stableToggleFavorite, stableOnOpenPanel])

  if (isLoading) {
    return (
      <div className="virtualized-grid-loading">
        <div className="loading-spinner">Loading bookmarks...</div>
      </div>
    )
  }

  if (filteredBookmarks.length === 0) {
    return (
      <div className="virtualized-grid-empty">
        <p>No bookmarks found</p>
      </div>
    )
  }

  // For small datasets, use regular rendering
  if (!shouldUseVirtualScrolling) {
    return (
      <div className="bookmark-grid-small" ref={containerRef}>
        {filteredBookmarks.map((bookmark) => (
          <BookmarkCardFlip
            key={bookmark.id}
            bookmark={bookmark}
            onToggleFavorite={toggleBookmarkFavorite}
            onOpenPanel={onOpenPanel}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="virtualized-grid-container" ref={containerRef}>
      {containerSize.width > 0 && containerSize.height > 0 && (
        <Grid
          columnCount={itemsPerRow}
          columnWidth={itemWidth + gap}
          height={containerSize.height} // Use full available height for optimal space utilization
          rowCount={rowCount}
          rowHeight={itemHeight + gap}
          width={containerSize.width}
          itemData={gridData}
          overscanRowCount={2} // Render 2 extra rows for smooth scrolling
        >
          {GridItem}
        </Grid>
      )}

      {/* Memory usage indicator for large datasets */}
      {filteredBookmarks.length > 2000 && (
        <div className="memory-optimization-indicator">
          ⚡ Virtual scrolling enabled for performance
        </div>
      )}
    </div>
  )
}
