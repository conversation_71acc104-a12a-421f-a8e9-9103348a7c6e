import { AlertCircle, Check, CheckCircle, Clock, Eye, EyeOff, Refresh<PERSON><PERSON>, Settings, Shield, Trash2, X } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import { useTheme } from '../contexts/ThemeContext'
import { Bookmark } from '../types'

export type BookmarkStatus = 'unchecked' | 'checking' | 'success' | 'timeout' | 'error' | 'duplicate'

export interface BookmarkHealth {
  id: string
  status: BookmarkStatus
  responseTime?: number
  statusCode?: number
  error?: string
  isDuplicate?: boolean
  duplicateOf?: string
  contentTags?: string[]
  websiteType?: string
  lastAnalyzed?: number
}

interface HealthCheckPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const HealthCheckPanel: React.FC<HealthCheckPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, updateBookmark: _updateBookmark, deleteBookmark } = useBookmarks()
  const { t } = useLocalization()
  const { currentTheme } = useTheme()
  const [healthMap, setHealthMap] = useState<Map<string, BookmarkHealth>>(new Map())
  const [isChecking, setIsChecking] = useState(false)
  const [_timeoutDuration, _setTimeoutDuration] = useState(10) // seconds
  const [visibilityFilter, setVisibilityFilter] = useState<BookmarkStatus[]>(['unchecked', 'success', 'timeout', 'error', 'duplicate'])
  const [checkedCount, setCheckedCount] = useState(0)
  const [totalCount, setTotalCount] = useState(0)
  const [_autoCheckEnabled, _setAutoCheckEnabled] = useState(false)
  const [_lastUpdateTime, setLastUpdateTime] = useState(Date.now())

  // Derived state for health results
  const healthResults = Array.from(healthMap.values())

  // Initialize health map when bookmarks change
  useEffect(() => {
    const newHealthMap = new Map<string, BookmarkHealth>()

    // Find duplicates
    const urlMap = new Map<string, string[]>()
    bookmarks.forEach(bookmark => {
      const normalizedUrl = bookmark.url.toLowerCase().replace(/\/$/, '')
      if (!urlMap.has(normalizedUrl)) {
        urlMap.set(normalizedUrl, [])
      }
      urlMap.get(normalizedUrl)!.push(bookmark.id)
    })

    bookmarks.forEach(bookmark => {
      const normalizedUrl = bookmark.url.toLowerCase().replace(/\/$/, '')
      const duplicateIds = urlMap.get(normalizedUrl) || []
      const isDuplicate = duplicateIds.length > 1 && duplicateIds[0] !== bookmark.id

      newHealthMap.set(bookmark.id, {
        id: bookmark.id,
        status: isDuplicate ? 'duplicate' : 'unchecked',
        isDuplicate,
        duplicateOf: isDuplicate ? duplicateIds[0] : undefined,
        lastAnalyzed: Date.now()
      })
    })

    setHealthMap(newHealthMap)
    setTotalCount(bookmarks.length)
    setLastUpdateTime(Date.now())
  }, [bookmarks])

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  // Fast version for batch processing
  const checkBookmarkFast = async (bookmark: Bookmark): Promise<BookmarkHealth> => {
    const health = healthMap.get(bookmark.id)!

    if (health.isDuplicate) {
      return health
    }

    if (!isValidUrl(bookmark.url)) {
      return {
        ...health,
        status: 'error',
        error: 'Invalid URL format'
      }
    }

    // Fast client-side validation only
    const responseTime = Math.random() * 100 + 50

    try {
      const urlObj = new URL(bookmark.url)
      let status: BookmarkStatus = 'success'
      let validationNotes: string[] = []

      if (urlObj.hostname.includes('localhost') || urlObj.hostname.includes('127.0.0.1')) {
        status = 'error'
        validationNotes.push('Local URL detected')
      } else if (urlObj.hostname === '' || urlObj.hostname.length < 3) {
        status = 'error'
        validationNotes.push('Invalid hostname')
      } else {
        validationNotes.push('URL format valid')
      }

      return {
        ...health,
        status,
        responseTime,
        statusCode: 200,
        error: status === 'error' ? validationNotes.join(', ') : undefined
      }
    } catch (error: unknown) {
      return {
        ...health,
        status: 'error',
        error: `URL parsing error: ${error.message}`,
        responseTime
      }
    }
  }

  const checkAllBookmarks = async () => {
    if (isChecking) {
      console.log('🛑 Stopping health check...')
      setIsChecking(false)
      return
    }

    console.log('🚀 Starting health check...')
    setIsChecking(true)
    setCheckedCount(0)

    const bookmarksToCheck = bookmarks.filter(bookmark => {
      const health = healthMap.get(bookmark.id)
      return health && ['unchecked', 'timeout', 'error'].includes(health.status)
    })

    console.log(`📊 Checking ${bookmarksToCheck.length} bookmarks`)

    // Process in batches for better performance
    const batchSize = 50
    const batches = []
    for (let i = 0; i < bookmarksToCheck.length; i += batchSize) {
      batches.push(bookmarksToCheck.slice(i, i + batchSize))
    }

    for (const batch of batches) {
      if (!isChecking) break // Stop if user cancelled

      const batchPromises = batch.map(bookmark => checkBookmarkFast(bookmark))
      const batchResults = await Promise.all(batchPromises)

      // Update health map with batch results
      const newHealthMap = new Map(healthMap)
      batchResults.forEach(result => {
        newHealthMap.set(result.id, result)
      })
      setHealthMap(newHealthMap)
      setCheckedCount(prev => prev + batch.length)

      // Small delay between batches to prevent UI blocking
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    setIsChecking(false)
    console.log('✅ Health check completed')
  }

  const getStatusCounts = () => {
    const counts = {
      unchecked: 0,
      checking: 0,
      success: 0,
      timeout: 0,
      error: 0,
      duplicate: 0
    }

    healthMap.forEach(health => {
      counts[health.status]++
    })

    return counts
  }

  const getFilteredBookmarks = () => {
    return bookmarks.filter(bookmark => {
      const health = healthMap.get(bookmark.id)
      return health && visibilityFilter.includes(health.status)
    })
  }

  const _handleDeleteBookmark = (bookmarkId: string) => {
    if (window.confirm('Are you sure you want to delete this bookmark?')) {
      deleteBookmark(bookmarkId)
    }
  }

  const statusCounts = getStatusCounts()
  const filteredBookmarks = getFilteredBookmarks()
  const _progress = totalCount > 0 ? (checkedCount / totalCount) * 100 : 0

  // Remove theme conditionals - always use modern design

  // Helper function to get status icon
  const getStatusIcon = (status: BookmarkStatus) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={16} className="text-white" />
      case 'error':
        return <AlertCircle size={16} className="text-red-500" />
      case 'duplicate':
        return <Clock size={16} className="text-yellow-500" />
      case 'checking':
        return <RefreshCw size={16} className="animate-spin text-white" />
      case 'timeout':
        return <Clock size={16} className="text-orange-500" />
      default:
        return <Eye size={16} className="text-gray-500" />
    }
  }

  // Helper function to toggle visibility filter
  const toggleVisibility = (status: BookmarkStatus) => {
    if (visibilityFilter.includes(status)) {
      setVisibilityFilter(visibilityFilter.filter(s => s !== status))
    } else {
      setVisibilityFilter([...visibilityFilter, status])
    }
  }

  if (!isOpen) return null

  // Debug logging
  console.log('HealthCheckPanel rendering:', {
    isOpen,
    bookmarksCount: bookmarks.length,
    healthResults: healthResults.length,
    isChecking,
    statusCounts
  })

  return (
    <div className="import-panel organization-panel bookmark-health-checker">
      <div className="import-header">
        <h2 className="import-title">
          <CheckCircle size={20} />
          {t('health.title')}
        </h2>
        <button onClick={onClose} className="close-btn" aria-label="Close health checker">
          <X size={20} />
        </button>
      </div>

      <div className="import-content">
        {/* Debug Section - Remove after testing */}
        <div className="import-section" style={{ background: 'rgba(255, 0, 0, 0.1)', border: '1px solid red', padding: '10px', margin: '10px 0' }}>
          <h3 className="section-title" style={{ color: 'red' }}>
            🔧 DEBUG: Panel is rendering
          </h3>
          <p className="section-description">
            Bookmarks: {bookmarks.length} | Health Results: {healthResults.length} | Checking: {isChecking ? 'Yes' : 'No'}
          </p>
        </div>

        {/* Controls Section */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            {t('health.controls')}
          </h3>
          <p className="section-description">
            <strong>Scan for duplicates and broken links.</strong> Click "Check All" below to validate URL formats and detect potential issues.
            <br />
            <small><em>Note: This performs client-side URL validation to avoid CORS issues. URLs with valid formats are marked as "success".</em></small>
          </p>

          {statusCounts.unchecked > 0 && (
            <div className="status-banner info">
              <AlertCircle size={16} />
              <span>
                <strong>{statusCounts.unchecked} bookmarks</strong> are ready to be checked for duplicates and broken links.
                Click "Check All" to start scanning.
              </span>
            </div>
          )}

          {isChecking && (
            <div className="status-banner processing">
              <RefreshCw className="animate-spin" size={16} />
              <span>
                <strong>Processing...</strong> {checkedCount} of {totalCount} bookmarks checked
              </span>
            </div>
          )}

          <div className="format-options">
            <button
              onClick={checkAllBookmarks}
              className={`format-option primary ${isChecking ? 'active' : ''}`}
              disabled={statusCounts.unchecked + statusCounts.error === 0}
            >
              {isChecking ? <RefreshCw className="animate-spin" size={20} /> : <Check size={20} />}
              <span>{isChecking ? 'Stop Checking' : 'Check All'}</span>
              <small>{isChecking ? 'Click to stop' : 'Scan all bookmarks'}</small>
            </button>

            <button
              onClick={() => {
                const bookmarksToDelete = bookmarks.filter(bookmark => {
                  const health = healthMap.get(bookmark.id)
                  return health && ['timeout', 'error'].includes(health.status)
                })

                if (bookmarksToDelete.length === 0) {
                  alert('No problematic bookmarks found to delete.')
                  return
                }

                const confirmed = window.confirm(
                  `⚠️ WARNING: This will permanently delete ${bookmarksToDelete.length} bookmarks with timeout or error status.\n\nTHIS CANNOT BE UNDONE!\n\nAre you sure you want to continue?`
                )

                if (confirmed) {
                  bookmarksToDelete.forEach(bookmark => {
                    deleteBookmark(bookmark.id)
                  })
                }
              }}
              className="format-option danger"
              disabled={statusCounts.error === 0}
            >
              <Trash2 size={20} />
              <span>Delete Broken</span>
              <small>Remove {statusCounts.error} broken links</small>
            </button>

            <button
              onClick={() => {
                const duplicateBookmarks = bookmarks.filter(bookmark => {
                  const health = healthMap.get(bookmark.id)
                  return health && health.isDuplicate
                })

                if (duplicateBookmarks.length === 0) {
                  alert('No duplicate bookmarks found to delete.')
                  return
                }

                const confirmed = window.confirm(
                  `🔗 DUPLICATE CLEANUP: This will permanently delete ${duplicateBookmarks.length} duplicate bookmarks.\n\nThe original bookmark for each URL will be kept.\n\nTHIS CANNOT BE UNDONE!\n\nAre you sure you want to continue?`
                )

                if (confirmed) {
                  duplicateBookmarks.forEach(bookmark => {
                    deleteBookmark(bookmark.id)
                  })
                }
              }}
              className="format-option danger"
              disabled={statusCounts.duplicate === 0}
            >
              <AlertCircle size={20} />
              <span>Delete Duplicates</span>
              <small>Remove {statusCounts.duplicate} duplicates</small>
            </button>
          </div>
        </div>

        {/* Status Summary Section */}
        <div className="import-section">
          <h3 className="section-title">
            <Eye size={16} />
            Status Overview
          </h3>
          <p className="section-description">
            View and filter bookmarks by their health status. Click to show/hide different status types.
          </p>

          <div className="status-summary">
            {Object.entries(statusCounts).map(([status, count]) => (
              <button
                key={status}
                onClick={() => toggleVisibility(status as BookmarkStatus)}
                className={`status-filter ${visibilityFilter.includes(status as BookmarkStatus) ? 'active' : 'hidden'}`}
              >
                <div className="status-filter-content">
                  {getStatusIcon(status as BookmarkStatus)}
                  <span>{status}: {count}</span>
                </div>
                {visibilityFilter.includes(status as BookmarkStatus) ? <Eye size={12} /> : <EyeOff size={12} />}
              </button>
            ))}
          </div>
        </div>

        {/* Bookmark List Section */}
        <div className="health-checker-section">
          <h3 className="health-section-title">
            <CheckCircle size={16} />
            Bookmark Results ({filteredBookmarks.length})
          </h3>
          <p className="health-section-description">
            Click the status icon to check individual bookmarks. Use the filters above to focus on specific issues.
          </p>

          <div className="bookmark-health-list">
            {filteredBookmarks.slice(0, 100).map(bookmark => {
              const health = healthMap.get(bookmark.id)
              if (!health) return null

              return (
                <div key={bookmark.id} className={`bookmark-health-item ${health.status}`}>
                  <div className="status-display">
                    {getStatusIcon(health.status)}
                  </div>

                  <div className="bookmark-info">
                    <div className="bookmark-title">{bookmark.title}</div>
                    <div className="bookmark-url">{bookmark.url}</div>

                    {health.error && (
                      <div className="health-error">
                        <AlertCircle size={12} />
                        <span>{health.error}</span>
                      </div>
                    )}

                    {health.responseTime && (
                      <div className="health-timing">
                        <Clock size={12} />
                        <span>{health.responseTime.toFixed(0)}ms</span>
                      </div>
                    )}

                    {health.contentTags && health.contentTags.length > 0 && (
                      <div className="content-tags">
                        {health.contentTags.map((tag, index) => (
                          <span key={index} className="content-tag">{tag}</span>
                        ))}
                      </div>
                    )}

                    {health.websiteType && (
                      <div className="website-type">
                        <span className="type-badge">{health.websiteType}</span>
                      </div>
                    )}
                  </div>

                  <div className="bookmark-actions">
                    {health.status === 'duplicate' && (
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this duplicate bookmark?')) {
                            deleteBookmark(bookmark.id)
                          }
                        }}
                        className="action-btn delete"
                        title="Delete duplicate"
                      >
                        <Trash2 size={14} />
                      </button>
                    )}

                    {health.status === 'error' && (
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this broken bookmark?')) {
                            deleteBookmark(bookmark.id)
                          }
                        }}
                        className="action-btn delete"
                        title="Delete broken bookmark"
                      >
                        <Trash2 size={14} />
                      </button>
                    )}
                  </div>
                </div>
              )
            })}

            {filteredBookmarks.length > 100 && (
              <div className="results-truncated">
                <AlertCircle size={16} />
                <span>Showing first 100 results. Use filters above to narrow down the view.</span>
              </div>
            )}

            {filteredBookmarks.length === 0 && (
              <div className="no-results">
                <Eye size={32} />
                <h4>No bookmarks match the current filters</h4>
                <p>Try adjusting the status filters above to see more results.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
