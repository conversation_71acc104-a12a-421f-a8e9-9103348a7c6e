# Panel Design System Guidelines

## Overview

This document establishes the unified panel design system for Bookmark Studio. All left-side panels now use a consistent modern design regardless of theme mode selection.

## Design Philosophy

**Theme Mode vs. Panel Design Separation:**
- **Theme Mode**: Controls color schemes (light/dark) and overall app theming
- **Panel Design**: Always uses modern glass morphism design system
- **Result**: Consistent panel structure with theme-appropriate colors

## Unified Panel Classes

### Core Panel Container
```css
.import-panel,
.export-panel,
.organization-panel,
.optimized-panel {
  /* Always uses modern glass morphism design */
  background: rgba(0, 0, 0, 0.3);
  border-left: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  /* ... other modern styling */
}
```

### Panel Headers
```css
.import-header,
.export-header,
.organization-header,
.panel-header {
  /* Always uses modern glass design */
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
}
```

### Panel Titles
```css
.import-title,
.export-title,
.organization-title,
.panel-title {
  /* Always uses modern text styling */
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}
```

## Implementation Standards

### ✅ Correct Implementation
```tsx
// Always use unified classes without theme conditionals
export const MyPanel: React.FC<Props> = ({ isOpen, onClose }) => {
  // Remove theme conditionals - always use modern design
  
  return (
    <div className="import-panel organization-panel">
      <div className="import-header">
        <h2 className="import-title">Panel Title</h2>
        <button onClick={onClose} className="close-btn">
          <X size={20} />
        </button>
      </div>
      {/* Panel content */}
    </div>
  )
}
```

### ❌ Incorrect Implementation
```tsx
// DON'T use theme conditionals for panel structure
export const MyPanel: React.FC<Props> = ({ isOpen, onClose }) => {
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'
  
  return (
    <div className={`import-panel ${isModernTheme ? 'modern-enhanced' : ''}`}>
      {/* This creates inconsistent panel designs */}
    </div>
  )
}
```

## Panel Types and Classes

### Import/Export Panels
- **Base Class**: `import-panel` or `export-panel`
- **Header**: `import-header` or `export-header`
- **Title**: `import-title` or `export-title`

### Organization Tool Panels
- **Base Classes**: `import-panel organization-panel`
- **Header**: `import-header organization-header`
- **Title**: `import-title organization-title`

### Multimedia Panels
- **Base Classes**: `import-panel organization-panel multimedia-panel`
- **Header**: `import-header`
- **Title**: `import-title`

## Theme Integration

### Light Theme Adjustments
```css
.light .import-panel,
.light .export-panel,
.light .organization-panel {
  background: rgba(255, 255, 255, 0.95);
  border-left: 2px solid rgba(0, 0, 0, 0.1);
}

.light .import-title,
.light .export-title,
.light .organization-title {
  color: rgba(0, 0, 0, 0.9);
}
```

### Dark Theme (Default)
- Uses the base modern glass morphism design
- Dark backgrounds with light borders and text

## Development Guidelines

### 1. New Panel Creation
- Always use `import-panel` as the base class
- Add specific panel type classes as needed
- Never use theme conditionals for structural styling
- Follow the established header/title/content structure

### 2. Theme Changes
- Only affect color schemes, not panel structure
- Use CSS custom properties for theme-specific colors
- Test in both light and dark modes

### 3. Component Structure
```tsx
<div className="import-panel [specific-panel-type]">
  <div className="import-header">
    <h2 className="import-title">Title</h2>
    <button className="close-btn">Close</button>
  </div>
  <div className="import-content">
    {/* Panel content */}
  </div>
</div>
```

### 4. CSS Organization
- Base panel styles in `optimized-panels.css`
- Theme adjustments in `App.css`
- Component-specific styles in component files

## Migration Checklist

When updating existing panels:

- [ ] Remove theme conditional logic (`isModernTheme` checks)
- [ ] Update className to use unified classes
- [ ] Remove `modern-enhanced` conditional classes
- [ ] Test in both light and dark themes
- [ ] Verify consistent appearance across all panels

## Benefits

1. **Consistency**: All panels have the same modern appearance
2. **Maintainability**: Single design system to maintain
3. **User Experience**: Predictable, professional interface
4. **Development Speed**: No need to implement multiple design variants
5. **Theme Flexibility**: Colors adapt to themes while structure remains consistent

## Future Considerations

- All new panels should follow this unified design system
- Theme changes should only affect colors, not structure
- Consider extending this system to other UI components
- Document any exceptions or special cases
