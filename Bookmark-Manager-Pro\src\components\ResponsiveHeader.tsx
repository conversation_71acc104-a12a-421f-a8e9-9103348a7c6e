import React, { useState, useRef, useEffect } from 'react';
import './ResponsiveHeader.css';

interface ResponsiveHeaderProps {
  onSearch?: (query: string) => void;
  onAdvancedSearch?: () => void;
  onMenuToggle?: () => void;
  searchQuery?: string;
  placeholder?: string;
  showAdvancedSearch?: boolean;
  className?: string;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  onSearch,
  onAdvancedSearch,
  onMenuToggle,
  searchQuery = '',
  placeholder = 'Search bookmarks...',
  showAdvancedSearch = true,
  className = ''
}) => {
  const [query, setQuery] = useState(searchQuery);
  const [isFocused, setIsFocused] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Handle search input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    onSearch?.(newQuery);
  };

  // Handle search form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(query);
    // Blur input on mobile after search
    if (window.innerWidth <= 768) {
      searchInputRef.current?.blur();
    }
  };

  // Handle search focus
  const handleSearchFocus = () => {
    setIsFocused(true);
    setIsSearchExpanded(true);
  };

  // Handle search blur
  const handleSearchBlur = () => {
    setIsFocused(false);
    // Collapse search on mobile if empty
    if (window.innerWidth <= 480 && !query.trim()) {
      setTimeout(() => setIsSearchExpanded(false), 150);
    }
  };

  // Handle search icon click on mobile
  const handleSearchIconClick = () => {
    if (window.innerWidth <= 480) {
      if (!isSearchExpanded) {
        setIsSearchExpanded(true);
        setTimeout(() => searchInputRef.current?.focus(), 100);
      } else {
        searchInputRef.current?.focus();
      }
    }
  };

  // Handle advanced search toggle
  const handleAdvancedSearchClick = () => {
    onAdvancedSearch?.();
  };

  // Handle menu toggle
  const handleMenuClick = () => {
    onMenuToggle?.();
  };

  // Clear search
  const handleClearSearch = () => {
    setQuery('');
    onSearch?.('');
    searchInputRef.current?.focus();
  };

  // Handle escape key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      searchInputRef.current?.blur();
      if (window.innerWidth <= 480) {
        setIsSearchExpanded(false);
      }
    }
  };

  // Update query when prop changes
  useEffect(() => {
    setQuery(searchQuery);
  }, [searchQuery]);

  return (
    <header className={`responsive-header ${className}`}>
      {/* Mobile Menu Button */}
      <button
        className="responsive-header__menu-btn touch-target"
        onClick={handleMenuClick}
        aria-label="Toggle menu"
        type="button"
        role="button"
        tabIndex={0}
      >
        <svg className="responsive-header__menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Logo/Brand */}
      <div className="responsive-header__brand">
        <h1 className="responsive-header__title">
          <span className="responsive-header__title-full">Bookmark Manager Pro</span>
          <span className="responsive-header__title-short">BM Pro</span>
          <span className="responsive-header__title-icon">📚</span>
        </h1>
      </div>

      {/* Search Section */}
      <div 
        className={`responsive-header__search-section ${
          isSearchExpanded ? 'responsive-header__search-section--expanded' : ''
        } ${
          isFocused ? 'responsive-header__search-section--focused' : ''
        }`}
        ref={searchContainerRef}
      >
        {/* Search Icon (Mobile) */}
        <button
          className="responsive-header__search-icon touch-target"
          onClick={handleSearchIconClick}
          aria-label="Search"
          type="button"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.35-4.35" />
          </svg>
        </button>

        {/* Search Form */}
        <form 
          className={`responsive-header__search-form ${
            isSearchExpanded ? 'responsive-header__search-form--expanded' : ''
          }`}
          onSubmit={handleSubmit}
        >
          <div className="responsive-header__search-input-container">
            <input
              ref={searchInputRef}
              type="text"
              value={query}
              onChange={handleInputChange}
              onFocus={handleSearchFocus}
              onBlur={handleSearchBlur}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="responsive-header__search-input"
              aria-label="Search bookmarks"
            />
            
            {/* Clear Button */}
            {query && (
              <button
                type="button"
                className="responsive-header__clear-btn touch-target"
                onClick={handleClearSearch}
                aria-label="Clear search"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            )}
          </div>

          {/* Search Submit Button */}
          <button
            type="submit"
            className="responsive-header__search-submit touch-target"
            aria-label="Submit search"
          >
            <span className="responsive-header__search-submit-text">Search</span>
            <svg className="responsive-header__search-submit-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.35-4.35" />
            </svg>
          </button>
        </form>

        {/* Advanced Search Button */}
        {showAdvancedSearch && (
          <button
            type="button"
            className="responsive-header__advanced-search touch-target"
            onClick={handleAdvancedSearchClick}
            aria-label="Advanced search options"
          >
            <span className="responsive-header__advanced-search-text">Advanced</span>
            <svg className="responsive-header__advanced-search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3" />
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
            </svg>
          </button>
        )}
      </div>

      {/* Action Buttons */}
      <div className="responsive-header__actions">
        {/* Notification Button */}
        <button
          className="responsive-header__action-btn touch-target"
          aria-label="Notifications"
          type="button"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
            <path d="M13.73 21a2 2 0 0 1-3.46 0" />
          </svg>
        </button>

        {/* Settings Button */}
        <button
          className="responsive-header__action-btn touch-target"
          aria-label="Settings"
          type="button"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="3" />
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
          </svg>
        </button>

        {/* Profile Button */}
        <button
          className="responsive-header__profile-btn touch-target"
          aria-label="User profile"
          type="button"
        >
          <div className="responsive-header__avatar">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
          </div>
        </button>
      </div>

      {/* Search Overlay (Mobile) */}
      {isSearchExpanded && window.innerWidth <= 480 && (
        <div 
          className="responsive-header__search-overlay"
          onClick={() => setIsSearchExpanded(false)}
        />
      )}
    </header>
  );
};

export default ResponsiveHeader;
export type { ResponsiveHeaderProps };