<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multimedia Panel Header Test</title>
    <link rel="stylesheet" href="src/styles/multimedia-design-system.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f1f5f9;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        /* Debug styles to identify issues */
        .debug-outline * {
            outline: 1px solid red !important;
        }
        
        .debug-bg {
            background: rgba(255, 0, 0, 0.1) !important;
        }
    </style>
</head>
<body>
    <h1>Multimedia Panel Header Test</h1>
    
    <div class="test-container">
        <!-- Test 1: Your exact HTML structure -->
        <div class="multimedia-panel__header">
            <div class="multimedia-panel__header-content">
                <div class="multimedia-panel__title-section">
                    <div class="multimedia-panel__icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video multimedia-panel__icon-svg" aria-hidden="true">
                            <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                            <rect x="2" y="6" width="14" height="12" rx="2"></rect>
                        </svg>
                    </div>
                    <h2 class="multimedia-panel__title">Multimedia Organization</h2>
                </div>
                <button class="multimedia-panel__close-btn" aria-label="Close multimedia organization panel">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Test content area -->
        <div class="multimedia-content">
            <p>This is the content area below the header.</p>
            <p>If you can see this text and the header above with icon, title, and close button, then the fix is working!</p>
        </div>
    </div>
    
    <br><br>
    
    <!-- Test 2: Debug version with outlines -->
    <div class="test-container debug-outline">
        <div class="multimedia-panel__header">
            <div class="multimedia-panel__header-content">
                <div class="multimedia-panel__title-section">
                    <div class="multimedia-panel__icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video multimedia-panel__icon-svg" aria-hidden="true">
                            <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                            <rect x="2" y="6" width="14" height="12" rx="2"></rect>
                        </svg>
                    </div>
                    <h2 class="multimedia-panel__title">DEBUG: Multimedia Organization</h2>
                </div>
                <button class="multimedia-panel__close-btn" aria-label="Close multimedia organization panel">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <div class="multimedia-content">
            <p><strong>Debug Version:</strong> Red outlines show element boundaries</p>
        </div>
    </div>
    
    <br><br>
    
    <!-- Test 3: Simplified version -->
    <div class="test-container">
        <div class="multimedia-panel__header" style="min-height: 60px;">
            <div style="color: white; font-weight: bold;">📹 Simple Test Header</div>
            <button class="multimedia-panel__close-btn" style="color: white;">✕</button>
        </div>
        
        <div class="multimedia-content">
            <p><strong>Simple Version:</strong> Basic header test</p>
        </div>
    </div>

    <script>
        // Add click handlers for testing
        document.querySelectorAll('.multimedia-panel__close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                alert('Close button clicked!');
            });
        });
        
        // Log CSS variables for debugging
        console.log('CSS Variables:', {
            primary: getComputedStyle(document.documentElement).getPropertyValue('--multimedia-primary'),
            textInverse: getComputedStyle(document.documentElement).getPropertyValue('--multimedia-text-inverse'),
            space6: getComputedStyle(document.documentElement).getPropertyValue('--multimedia-space-6')
        });
    </script>
</body>
</html>