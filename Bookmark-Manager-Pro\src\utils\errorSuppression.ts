/**
 * Enhanced Error Suppression Utility
 * Suppresses external script errors, browser extension errors, and development noise
 * while preserving important application errors
 */

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

/**
 * Enhanced error suppression for external scripts and browser extensions
 */
export const initializeErrorSuppression = () => {
  // Suppress console errors from external sources
  console.error = (...args: unknown[]) => {
    const message = args.join(' ');

    // Suppress browser extension errors
    if (
      message.includes('utils.js') ||
      message.includes('connectBg') ||
      message.includes('wrappedFn') ||
      message.includes('smartSetTimeout') ||
      message.includes('withTimeout') ||
      message.includes('chrome-extension') ||
      message.includes('moz-extension') ||
      message.includes('Extension context invalidated') ||
      (message.includes('timeout') && message.includes('utils.js')) ||
      // Specific timeout error pattern
      (message.trim() === 'timeout' || message.includes('Uncaught (in promise) Error: timeout'))
    ) {
      return; // Suppress these errors
    }

    // Suppress favicon-related errors
    if (
      message.includes('Failed to load resource') &&
      (message.includes('gstatic.com') ||
       message.includes('favicon') ||
       message.includes('faviconV2'))
    ) {
      return; // Suppress favicon errors
    }

    // Suppress WebSocket development errors
    if (
      message.includes('WebSocket') ||
      message.includes('websocket') ||
      message.includes('ws://localhost') ||
      message.includes('[vite]')
    ) {
      return; // Suppress development errors
    }

    // Allow other errors through
    originalConsoleError.apply(console, args);
  };

  // Handle uncaught promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    const message = error?.message || error?.toString() || '';
    const stack = error?.stack || '';

    // Suppress external script promise rejections
    if (
      message.includes('timeout') ||
      message.includes('connectBg') ||
      message.includes('wrappedFn') ||
      message.includes('smartSetTimeout') ||
      message.includes('withTimeout') ||
      stack.includes('utils.js') ||
      stack.includes('extension') ||
      stack.includes('chrome-extension') ||
      stack.includes('moz-extension') ||
      // Specific pattern from the error you encountered
      (message === 'timeout' && stack.includes('utils.js'))
    ) {
      event.preventDefault();
      return;
    }

    // Suppress favicon promise rejections
    if (
      message.includes('favicon') ||
      message.includes('gstatic.com') ||
      message.includes('faviconV2')
    ) {
      event.preventDefault();
      return;
    }

    // Suppress WebSocket promise rejections
    if (
      message.includes('WebSocket') ||
      message.includes('websocket') ||
      message.includes('ws://localhost')
    ) {
      event.preventDefault();
      return;
    }

    // Allow other promise rejections through
  });

  // Handle global errors
  window.addEventListener('error', (event) => {
    const message = event.message || '';
    const filename = event.filename || '';
    const stack = event.error?.stack || '';

    // Suppress browser extension errors
    if (
      filename.includes('utils.js') ||
      filename.includes('extension') ||
      filename.includes('chrome-extension') ||
      filename.includes('moz-extension') ||
      message.includes('connectBg') ||
      message.includes('wrappedFn') ||
      stack.includes('utils.js') ||
      (message.includes('timeout') && filename.includes('utils.js'))
    ) {
      event.preventDefault();
      return false;
    }

    // Allow other errors through
    return true;
  });

  console.log('🛡️ Enhanced error suppression initialized - External script errors will be suppressed');
};

/**
 * Restore original console methods (for debugging)
 */
export const restoreConsole = () => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log('🔧 Console methods restored to original state');
};

/**
 * Check if an error should be suppressed
 */
export const shouldSuppressError = (error: Error | string): boolean => {
  const message = typeof error === 'string' ? error : error.message;
  const stack = typeof error === 'string' ? '' : error.stack || '';

  return (
    // Browser extension errors
    message.includes('utils.js') ||
    message.includes('connectBg') ||
    message.includes('wrappedFn') ||
    message.includes('chrome-extension') ||
    message.includes('moz-extension') ||
    stack.includes('utils.js') ||
    stack.includes('extension') ||
    
    // Favicon errors
    message.includes('favicon') ||
    message.includes('gstatic.com') ||
    message.includes('faviconV2') ||
    
    // WebSocket errors
    message.includes('WebSocket') ||
    message.includes('websocket') ||
    message.includes('ws://localhost')
  );
};

// Auto-initialize when module is imported
initializeErrorSuppression();
