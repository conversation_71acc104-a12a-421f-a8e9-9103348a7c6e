// Multimedia Playlist Creation Flow Tests
// Tests the emotional journey and confidence aspects of creating multimedia playlists

import { expect, test } from '@playwright/test';
import { PerformanceMonitor } from '../utils/performance-monitor.js';
import { VibeMetrics } from '../utils/vibe-metrics.js';

const SELECTORS = {
    multimediaPanel: '.multimedia-panel, [data-testid="multimedia-panel"]',
    createPlaylistBtn: '.create-playlist, [data-testid="create-playlist"], .multimedia-organize',
    videoDetection: '.video-content, .detected-videos, .youtube-videos',
    playlistBuilder: '.playlist-builder, .playlist-creator, .multimedia-builder',
    videoCard: '.video-card, .multimedia-item, .detected-video',
    playButton: '.play-btn, [data-testid="play-button"], .start-playlist',
    progressBar: '.progress-bar, .playlist-progress, [role="progressbar"]',
    playlistControls: '.playlist-controls, .media-controls, .playback-controls',
    errorAlert: '[role="alert"], .error, .error-message'
};

test.describe('Multimedia Playlist Creation Flow - Vibe Testing', () => {

    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('domcontentloaded');

        // Wait for bookmark data to load
        try {
            await page.waitForSelector('[data-testid="bookmark-item"], .bookmark-card', { timeout: 10000 });
            await page.waitForTimeout(1000);
        } catch (error) {
            console.log('No bookmark elements found - may be expected for some test scenarios');
        }
    });

    test('Multimedia detection feels magical, not technical', async ({ page }) => {
        const session = await PerformanceMonitor.startMonitoring(page, 'multimedia-detection-magic');

        // Look for multimedia panel or creation button
        const multimediaButton = page.locator(SELECTORS.createPlaylistBtn).first();
        const multimediaPanel = page.locator(SELECTORS.multimediaPanel).first();

        const hasMultimediaFeature = await multimediaButton.count() > 0 || await multimediaPanel.count() > 0;

        if (!hasMultimediaFeature) {
            console.log('Multimedia feature not found - may not be implemented yet');
            await test.info().attach('vibe-metrics', {
                body: JSON.stringify({
                    testType: 'multimedia-detection-magic',
                    status: 'skipped',
                    reason: 'Multimedia feature not available'
                }),
                contentType: 'application/json'
            });
            await PerformanceMonitor.endMonitoring(session);
            return;
        }

        // Trigger multimedia detection
        const detectionMetrics = await VibeMetrics.measureResponseTime(page, async () => {
            if (await multimediaButton.count() > 0) {
                await multimediaButton.click();
            } else {
                // Try to open multimedia panel if it exists
                const panelToggle = page.locator('.multimedia-toggle, .media-panel-btn').first();
                if (await panelToggle.count() > 0) {
                    await panelToggle.click();
                }
            }
        }, 1000);

        await page.waitForTimeout(1500); // Allow detection to process

        // Check for magical discovery experience
        const magicalElements = await page.evaluate((selectors) => {
            return {
                hasVideoDiscovery: !!document.querySelector(selectors.videoDetection),
                hasCountAnimation: !!document.querySelector('.counting-up, .discovery-animation'),
                hasExcitementLanguage: !!document.querySelector('.found-videos, .discovered-content, .detected-media'),
                showsIntelligence: !!document.querySelector('.smart-detection, .ai-powered, .intelligent-analysis'),
                hasVisualFeedback: !!document.querySelector('.detection-results, .media-preview, .video-thumbnails'),
                noTechnicalJargon: !document.querySelector('.parsing-html, .url-analysis, .dom-extraction'),
                hasDelightfulCopy: !!document.querySelector('.amazing, .awesome, .great-find, .perfect-for')
            };
        }, SELECTORS);

        // Magic requires discovery feeling, not technical processing
        if (magicalElements.hasVideoDiscovery) {
            expect(magicalElements.noTechnicalJargon).toBe(true);
            expect(magicalElements.hasVisualFeedback || magicalElements.hasExcitementLanguage).toBe(true);
        }

        // Check video detection results
        const videoCards = page.locator(SELECTORS.videoCard);
        const videoCount = await videoCards.count();

        if (videoCount > 0) {
            // Should feel like discovery, not just data processing
            const discoveryFeeling = magicalElements.hasCountAnimation ||
                magicalElements.hasExcitementLanguage ||
                magicalElements.hasDelightfulCopy;

            expect(discoveryFeeling).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                responseTime: detectionMetrics,
                magicalElements,
                videoCount,
                testType: 'multimedia-detection-magic'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'multimedia-discovery',
                    duration: 2500,
                    videosFound: videoCount
                },
                emotionalStates: [
                    { emotion: 'curious', timestamp: 0, trigger: 'click-multimedia-button' },
                    { emotion: 'anticipation', timestamp: 500, trigger: 'processing-starts' },
                    { emotion: 'delighted', timestamp: 1500, trigger: 'videos-discovered' }
                ],
                delightMoments: [
                    { category: 'discovery', trigger: 'video-detection', intensity: 8 },
                    { category: 'intelligence', trigger: 'smart-categorization', intensity: 7 },
                    { category: 'visual-feedback', trigger: 'thumbnail-preview', intensity: 6 }
                ]
            }),
            contentType: 'application/json'
        });

        await PerformanceMonitor.endMonitoring(session);
    });

    test('Playlist creation feels creative, not mechanical', async ({ page }) => {
        // Try to access playlist builder
        const createButton = page.locator(SELECTORS.createPlaylistBtn).first();
        const playlistBuilder = page.locator(SELECTORS.playlistBuilder).first();

        if (await createButton.count() === 0 && await playlistBuilder.count() === 0) {
            test.skip('Playlist creation feature not available');
            return;
        }

        // Start playlist creation
        if (await createButton.count() > 0) {
            await createButton.click();
            await page.waitForTimeout(1000);
        }

        // Check for creative elements in playlist builder
        const creativeElements = await page.evaluate(() => {
            return {
                hasPlaylistNaming: !!document.querySelector('.playlist-name, .title-input, .name-your-playlist'),
                hasThemeSelection: !!document.querySelector('.theme-selector, .playlist-theme, .mood-selection'),
                hasCustomization: !!document.querySelector('.customize, .personalize, .make-it-yours'),
                hasCreativeLanguage: !!document.querySelector('.create, .build, .craft, .design'),
                hasInspiration: !!document.querySelector('.inspiration, .suggestions, .ideas'),
                noMechanicalTerms: !document.querySelector('.generate-list, .auto-compile, .batch-process'),
                hasVisualBuilder: !!document.querySelector('.visual-builder, .drag-drop, .playlist-canvas'),
                showsPersonality: !!document.querySelector('.your-style, .personal-touch, .unique-playlist')
            };
        });

        // Creative tools should feel inspiring, not mechanical
        expect(creativeElements.noMechanicalTerms).toBe(true);

        const feelsCreative = creativeElements.hasPlaylistNaming ||
            creativeElements.hasThemeSelection ||
            creativeElements.hasCustomization;

        if (feelsCreative) {
            expect(creativeElements.hasCreativeLanguage || creativeElements.showsPersonality).toBe(true);
        }

        // Test playlist naming if available
        const nameInput = page.locator('.playlist-name, .title-input, input[placeholder*="name"]').first();
        if (await nameInput.count() > 0) {
            const namingExperience = await VibeMetrics.measureResponseTime(page, async () => {
                await nameInput.fill('My Learning Journey');
            }, 100);

            expect(['excellent', 'good', 'acceptable']).toContain(namingExperience.quality);

            // Check for creative feedback
            await page.waitForTimeout(200);
            const namingFeedback = await page.evaluate(() => {
                return {
                    hasPreview: !!document.querySelector('.playlist-preview, .name-preview'),
                    hasEncouragement: !!document.querySelector('.great-name, .perfect, .nice-choice'),
                    showsPersonalization: !!document.querySelector('.your-playlist, .personal-collection'),
                    noGenericResponse: !document.querySelector('.playlist-1, .untitled, .default-name')
                };
            });

            expect(namingFeedback.noGenericResponse).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                creativeElements,
                testType: 'playlist-creation-creativity',
                creativityLevel: feelsCreative ? 'high' : 'low'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'creative-playlist-building',
                    duration: 3000,
                    interactions: 2
                },
                emotionalStates: [
                    { emotion: 'inspired', timestamp: 0, trigger: 'see-creation-options' },
                    { emotion: 'creative', timestamp: 1000, trigger: 'start-customizing' },
                    { emotion: 'proud', timestamp: 2500, trigger: 'see-personal-playlist' }
                ],
                creativityFactors: [
                    { category: 'personalization', trigger: 'naming-playlist', intensity: 8 },
                    { category: 'customization', trigger: 'theme-selection', intensity: 7 },
                    { category: 'ownership', trigger: 'personal-touch', intensity: 9 }
                ]
            }),
            contentType: 'application/json'
        });
    });

    test('Playlist playback feels immersive, not fragmented', async ({ page }) => {
        // Look for existing playlist or create one
        const playButton = page.locator(SELECTORS.playButton).first();
        const playlistControls = page.locator(SELECTORS.playlistControls).first();

        if (await playButton.count() === 0 && await playlistControls.count() === 0) {
            test.skip('Playlist playback feature not available');
            return;
        }

        // Start playback if play button exists
        if (await playButton.count() > 0) {
            const playbackMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await playButton.click();
            }, 500);

            expect(['excellent', 'good', 'acceptable']).toContain(playbackMetrics.quality);
        }

        await page.waitForTimeout(1000);

        // Check for immersive playback experience
        const immersionElements = await page.evaluate((selectors) => {
            return {
                hasSeamlessTransition: !!document.querySelector('.seamless, .smooth-transition, .continuous-play'),
                hasProgressTracking: !!document.querySelector(selectors.progressBar),
                hasContextualInfo: !!document.querySelector('.now-playing, .current-video, .playlist-context'),
                hasImmersiveUI: !!document.querySelector('.fullscreen-option, .theater-mode, .focus-mode'),
                hasFlowState: !!document.querySelector('.uninterrupted, .flow-mode, .distraction-free'),
                noJarringInterruptions: !document.querySelector('.popup-ads, .interruption, '.jarring - transition'),
        hasAmbientControls: !!document.querySelector('.subtle-controls, .ambient-ui, .minimal-interface'),
                    showsContinuity: !!document.querySelector('.next-up, .coming-next, '.playlist - flow')
      };
        }, SELECTORS);

        // Immersion requires smooth, uninterrupted experience
        expect(immersionElements.noJarringInterruptions).toBe(true);

        const feelsImmersive = immersionElements.hasSeamlessTransition ||
            immersionElements.hasFlowState ||
            immersionElements.hasImmersiveUI;

        if (await playlistControls.count() > 0) {
            expect(feelsImmersive || immersionElements.hasProgressTracking).toBe(true);
        }

        // Test control interactions don't break immersion
        const controlButton = page.locator('.pause-btn, .next-btn, .volume-btn').first();
        if (await controlButton.count() > 0) {
            const controlMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await controlButton.hover();
            }, 50);

            expect(['excellent', 'good', 'acceptable']).toContain(controlMetrics.quality);

            // Controls should be subtle, not intrusive
            await page.waitForTimeout(200);
            const controlExperience = await page.evaluate(() => {
                return {
                    hasSubtleAppearance: !!document.querySelector('.subtle-controls, .fade-in-controls'),
                    hasContextualHelp: !!document.querySelector('.control-tooltip, .helpful-hint'),
                    noIntrusiveUI: !document.querySelector('.blocking-overlay, .intrusive-controls'),
                    maintainsImmersion: !!document.querySelector('.immersion-friendly, .non-disruptive')
                };
            });

            expect(controlExperience.noIntrusiveUI).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                immersionElements,
                testType: 'playlist-playback-immersion',
                immersionLevel: feelsImmersive ? 'high' : 'medium'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'immersive-playback',
                    duration: 4000,
                    interactions: 3
                },
                emotionalStates: [
                    { emotion: 'anticipation', timestamp: 0, trigger: 'start-playlist' },
                    { emotion: 'engaged', timestamp: 1000, trigger: 'smooth-playback' },
                    { emotion: 'immersed', timestamp: 2500, trigger: 'flow-state-achieved' },
                    { emotion: 'satisfied', timestamp: 3500, trigger: 'seamless-experience' }
                ],
                immersionFactors: [
                    { category: 'continuity', trigger: 'seamless-transitions', intensity: 9 },
                    { category: 'focus', trigger: 'distraction-free-ui', intensity: 8 },
                    { category: 'control', trigger: 'subtle-controls', intensity: 7 }
                ]
            }),
            contentType: 'application/json'
        });
    });

    test('Playlist organization feels intuitive, not overwhelming', async ({ page }) => {
        // Look for playlist organization features
        const organizationElements = await page.evaluate(() => {
            return {
                hasPlaylistList: !!document.querySelector('.playlist-list, .my-playlists, .playlist-collection'),
                hasCategorization: !!document.querySelector('.playlist-categories, .organize-playlists'),
                hasSearchPlaylists: !!document.querySelector('.search-playlists, .find-playlist'),
                hasSortOptions: !!document.querySelector('.sort-playlists, .playlist-sorting'),
                hasVisualOrganization: !!document.querySelector('.playlist-grid, .visual-playlists'),
                hasTagging: !!document.querySelector('.playlist-tags, .tag-playlists'),
                noComplexHierarchy: !document.querySelector('.deep-nesting, .complex-structure'),
                hasSimpleNavigation: !!document.querySelector('.easy-navigation, .simple-browse')
            };
        });

        if (!organizationElements.hasPlaylistList && !organizationElements.hasVisualOrganization) {
            test.skip('Playlist organization features not available');
            return;
        }

        // Organization should feel manageable, not overwhelming
        expect(organizationElements.noComplexHierarchy).toBe(true);

        const feelsIntuitive = organizationElements.hasVisualOrganization ||
            organizationElements.hasSimpleNavigation ||
            organizationElements.hasSearchPlaylists;

        expect(feelsIntuitive).toBe(true);

        // Test playlist browsing if available
        const playlistItem = page.locator('.playlist-item, .playlist-card').first();
        if (await playlistItem.count() > 0) {
            const browsingMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await playlistItem.hover();
            }, 100);

            expect(['excellent', 'good', 'acceptable']).toContain(browsingMetrics.quality);

            // Check for helpful preview information
            await page.waitForTimeout(200);
            const previewInfo = await page.evaluate(() => {
                return {
                    hasPreview: !!document.querySelector('.playlist-preview, .quick-info'),
                    hasVideoCount: !!document.querySelector('.video-count, .item-count'),
                    hasDuration: !!document.querySelector('.total-duration, .playlist-length'),
                    hasDescription: !!document.querySelector('.playlist-description, .about-playlist'),
                    hasLastPlayed: !!document.querySelector('.last-played, .recent-activity'),
                    noInformationOverload: !document.querySelector('.too-much-info, .overwhelming-details')
                };
            });

            expect(previewInfo.noInformationOverload).toBe(true);

            const hasHelpfulInfo = previewInfo.hasVideoCount ||
                previewInfo.hasDuration ||
                previewInfo.hasDescription;

            expect(hasHelpfulInfo).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                organizationElements,
                testType: 'playlist-organization-intuition',
                intuitionLevel: feelsIntuitive ? 'high' : 'medium'
            }),
            contentType: 'application/json'
        });
    });

    test('Playlist sharing feels social, not technical', async ({ page }) => {
        // Look for sharing features
        const shareButton = page.locator('.share-playlist, .share-btn, [data-testid="share"]').first();

        if (await shareButton.count() === 0) {
            test.skip('Playlist sharing feature not available');
            return;
        }

        const sharingMetrics = await VibeMetrics.measureResponseTime(page, async () => {
            await shareButton.click();
        }, 300);

        expect(['excellent', 'good', 'acceptable']).toContain(sharingMetrics.quality);

        await page.waitForTimeout(500);

        // Check for social sharing experience
        const socialElements = await page.evaluate(() => {
            return {
                hasSocialPlatforms: !!document.querySelector('.social-share, .share-to-twitter, .share-to-facebook'),
                hasPersonalMessage: !!document.querySelector('.add-message, .personal-note, .share-message'),
                hasFriendlyLanguage: !!document.querySelector('.share-with-friends, .let-others-enjoy, .spread-the-love'),
                hasVisualPreview: !!document.querySelector('.share-preview, .link-preview, .playlist-card'),
                hasEasySharing: !!document.querySelector('.one-click-share, '.easy - share', '.quick - share'),
        noTechnicalDetails: !document.querySelector('.api-endpoint, .json-export, .technical-share'),
                    hasCollaborativeOptions: !!document.querySelector('.collaborate, .co-create, .invite-friends'),
                    showsExcitement: !!document.querySelector('.amazing-playlist, .check-this-out, .you-will-love')
      };
        });

        // Social sharing should feel personal and exciting, not technical
        expect(socialElements.noTechnicalDetails).toBe(true);

        const feelsSocial = socialElements.hasSocialPlatforms ||
            socialElements.hasPersonalMessage ||
            socialElements.hasFriendlyLanguage;

        expect(feelsSocial).toBe(true);

        // Test sharing interaction
        const socialShareBtn = page.locator('.twitter-share, .facebook-share, .copy-link').first();
        if (await socialShareBtn.count() > 0) {
            const socialMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await socialShareBtn.click();
            }, 200);

            expect(['excellent', 'good', 'acceptable']).toContain(socialMetrics.quality);

            // Check for social feedback
            await page.waitForTimeout(300);
            const socialFeedback = await page.evaluate(() => {
                return {
                    hasSuccessMessage: !!document.querySelector('.shared-successfully, .link-copied'),
                    hasEncouragement: !!document.querySelector('.great-choice, .friends-will-love'),
                    hasNextSteps: !!document.querySelector('.now-share, .send-to-friends'),
                    noTechnicalConfirmation: !document.querySelector('.url-generated, .api-success')
                };
            });

            expect(socialFeedback.noTechnicalConfirmation).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                responseTime: sharingMetrics,
                socialElements,
                testType: 'playlist-sharing-social'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'social-playlist-sharing',
                    duration: 2000,
                    interactions: 2
                },
                emotionalStates: [
                    { emotion: 'proud', timestamp: 0, trigger: 'want-to-share-playlist' },
                    { emotion: 'excited', timestamp: 500, trigger: 'see-sharing-options' },
                    { emotion: 'connected', timestamp: 1500, trigger: 'share-with-friends' }
                ],
                socialFactors: [
                    { category: 'pride', trigger: 'playlist-accomplishment', intensity: 8 },
                    { category: 'connection', trigger: 'share-with-others', intensity: 9 },
                    { category: 'excitement', trigger: 'spread-discovery', intensity: 7 }
                ]
            }),
            contentType: 'application/json'
        });
    });

});