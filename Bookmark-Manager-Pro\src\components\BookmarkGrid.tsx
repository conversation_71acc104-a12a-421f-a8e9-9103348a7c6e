import React, { useCallback, useEffect, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import '../styles/memory-optimization.css'
import { calculateGridColumns, useKeyboardNavigation } from '../utils/keyboardNavigation'
import { BookmarkCardFlip } from './BookmarkCardFlip'
import { BookmarkSkeleton } from './BookmarkSkeleton'
import { EmptyState } from './EmptyState'
import { ListView } from './ListView'
import { MemoryMonitor } from './MemoryMonitor'
import { MindMapView } from './MindMapView'
import { PanelType } from './TabbedRightPanel'
import { TreeView } from './TreeView'
import { ViewMode, ViewToggle } from './ViewToggle'
import { VirtualizedBookmarkGrid } from './VirtualizedBookmarkGrid'

interface BookmarkGridProps {
  onOpenPanel?: (panelType: PanelType) => void
}

export const BookmarkGrid: React.FC<BookmarkGridProps> = ({ onOpenPanel }) => {
  const {
    filteredBookmarks,
    isLoading,
    searchQuery,
    selectedCollection,
    setSearchQuery,
    setSelectedCollection,
    setFilterType,
    setSelectedPlaylist,
    toggleBookmarkSelection,
    toggleBookmarkFavorite,
    isSelectMode,
    bookmarkData
  } = useBookmarks()

  // Debug: Log what BookmarkGrid is receiving
  React.useEffect(() => {
    console.warn('📊 BOOKMARK GRID:',
      'bookmarkData=' + (bookmarkData ? 'EXISTS' : 'NULL'),
      'bookmarkDataBookmarks=' + (bookmarkData?.bookmarks?.length || 0),
      'filteredBookmarks=' + (filteredBookmarks?.length || 0),
      'isLoading=' + isLoading
    )
  }, [bookmarkData, filteredBookmarks, isLoading])
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [gridColumns, setGridColumns] = useState(4)
  const [_draggedItem, setDraggedItem] = useState<string | null>(null)
  const [useVirtualScrolling, setUseVirtualScrolling] = useState(false)
  const [optimizationSuggestion, setOptimizationSuggestion] = useState<string | null>(null)

  // Drag handlers
  const _createDragStartHandler = useCallback((bookmark: Bookmark) => {
    return (e: React.DragEvent) => {
      if (isSelectMode) {
        e.preventDefault()
        return
      }

      setDraggedItem(bookmark.id)
      e.dataTransfer.setData('text/plain', bookmark.url)
      e.dataTransfer.setData('text/uri-list', bookmark.url)
      e.dataTransfer.setData('application/json', JSON.stringify({
        id: bookmark.id,
        title: bookmark.title,
        url: bookmark.url,
        favicon: bookmark.favicon
      }))
      e.dataTransfer.effectAllowed = 'copy'
    }
  }, [isSelectMode])

  const _handleDragEnd = useCallback(() => {
    setDraggedItem(null)
  }, [])

  // Keyboard navigation for grid view
  const {
    initializeNavigation,
    focusFirst: _focusFirst,
    setContainerRef
  } = useKeyboardNavigation({
    containerSelector: '.bookmark-grid',
    itemSelector: '.bookmark-card',
    gridColumns,
    onEnter: (element, index) => {
      // Simulate click on the bookmark card
      const bookmark = filteredBookmarks[index]
      if (bookmark) {
        if (isSelectMode) {
          toggleBookmarkSelection(bookmark.id)
        } else {
          window.open(bookmark.url, '_blank', 'noopener,noreferrer')
        }
      }
    },
    onSpace: (element, index) => {
      // Toggle selection on space key
      const bookmark = filteredBookmarks[index]
      if (bookmark) {
        toggleBookmarkSelection(bookmark.id)
      }
    },
    enableArrowKeys: true,
    enableHomeEnd: true,
    enablePageUpDown: true,
    wrapNavigation: true
  })

  // Calculate grid columns based on container width
  const updateGridColumns = useCallback(() => {
    const container = document.querySelector('.bookmark-grid') as HTMLElement
    if (container) {
      const containerWidth = container.offsetWidth
      const columns = calculateGridColumns(containerWidth, 300, 16)
      setGridColumns(columns)
    }
  }, [])

  // Initialize keyboard navigation when bookmarks change
  useEffect(() => {
    if (viewMode === 'grid' && filteredBookmarks.length > 0) {
      // Small delay to ensure DOM is updated
      const timer = setTimeout(() => {
        initializeNavigation()
        updateGridColumns()
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [filteredBookmarks, viewMode, initializeNavigation, updateGridColumns])

  // Handle window resize for responsive grid columns
  useEffect(() => {
    if (viewMode === 'grid') {
      const handleResize = () => {
        updateGridColumns()
      }

      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [viewMode, updateGridColumns])



  // CRITICAL: Memory optimization for large datasets without breaking layout
  useEffect(() => {
    const bookmarkCount = filteredBookmarks.length

    // Auto-enable virtual scrolling for large datasets (lowered threshold for better performance)
    if (bookmarkCount > 100 && !useVirtualScrolling) {
      setUseVirtualScrolling(true)
      console.log(`🧠 Auto-enabled virtual scrolling for ${bookmarkCount} bookmarks`)
    } else if (bookmarkCount <= 50 && useVirtualScrolling) {
      setUseVirtualScrolling(false)
      console.log(`🧠 Disabled virtual scrolling for ${bookmarkCount} bookmarks`)
    }

    // Log current state for debugging
    console.log(`📊 Bookmark count: ${bookmarkCount}, Virtual scrolling: ${useVirtualScrolling ? 'ENABLED' : 'DISABLED'}`)

    // Emergency DOM node count check
    const nodeCount = document.querySelectorAll('*').length
    if (nodeCount > 80000 && !useVirtualScrolling) {
      console.error(`🚨 EMERGENCY: DOM node count too high (${nodeCount.toLocaleString()}), forcing virtual scrolling!`)
      setUseVirtualScrolling(true)
    }

    if (bookmarkCount > 2000) {
      const timeoutId = setTimeout(() => {
        console.log(`🧠 Memory optimization active for ${bookmarkCount} bookmarks`)

        // Force garbage collection for large datasets
        if (typeof window !== 'undefined' && (window as Window & { gc?: () => void }).gc) {
          (window as Window & { gc: () => void }).gc()
        }
      }, 1000)
      return () => clearTimeout(timeoutId)
    }
  }, [filteredBookmarks.length, useVirtualScrolling])

  // CRITICAL: Periodic memory cleanup during scroll
  useEffect(() => {
    if (filteredBookmarks.length > 1000) {
      const interval = setInterval(() => {
        if (typeof window !== 'undefined' && (window as Window & { gc?: () => void }).gc) {
          (window as Window & { gc: () => void }).gc()
        }
      }, 30000) // Every 30 seconds for large datasets

      return () => clearInterval(interval)
    }
  }, [filteredBookmarks.length])

  // Drag functionality removed to fix external drag and drop conflicts

  const handleImportClick = () => {
    // Trigger import panel opening - we'll need to pass this up to App component
    const importButton = document.querySelector('.import-btn') as HTMLButtonElement
    if (importButton) {
      importButton.click()
    }
  }



  const handleClearFilters = useCallback(() => {
    setSearchQuery('')
    setSelectedCollection('all')
    setFilterType('all')
    setSelectedPlaylist(null)
  }, [setSearchQuery, setSelectedCollection, setFilterType, setSelectedPlaylist])

  const handleViewAllClick = useCallback(() => {
    setSelectedCollection('all')
    setFilterType('all')
    setSelectedPlaylist(null)
  }, [setSelectedCollection, setFilterType, setSelectedPlaylist])

  if (isLoading) {
    return (
      <main className="bookmark-grid-container">
        <div className="grid-header">
          <div className="grid-info">
            <h2 className="grid-title">Loading...</h2>
          </div>
          <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
        </div>
        {viewMode === 'grid' && (
          <div className="bookmark-grid">
            {Array.from({ length: 8 }).map((_, index) => (
              <BookmarkSkeleton key={index} />
            ))}
          </div>
        )}
        {viewMode === 'list' && <div className="loading-placeholder">Loading list view...</div>}
        {viewMode === 'tree' && <div className="loading-placeholder">Loading tree view...</div>}
        {viewMode === 'mindmap' && <div className="loading-placeholder">Loading mind map...</div>}
      </main>
    )
  }

  if (filteredBookmarks.length === 0) {
    return (
      <main className="bookmark-grid-container">
        <div className="grid-header">
          <div className="grid-info">
            <h2 className="grid-title">No Bookmarks</h2>
          </div>
          <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
        </div>
        <EmptyState
          searchQuery={searchQuery}
          selectedCollection={selectedCollection}
          onImportClick={handleImportClick}
          onClearFilters={handleClearFilters}
          onViewAllClick={handleViewAllClick}
        />
      </main>
    )
  }

  const getTitle = () => {
    if (selectedCollection === 'all') return 'All Bookmarks'
    if (selectedCollection === 'favorites') return 'Favorite Bookmarks'
    if (selectedCollection === 'recent') return 'Recently Added'
    return `${selectedCollection} Collection`
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'list':
        return (
          <ListView
            searchQuery={searchQuery}
            selectedCollection={selectedCollection}
          />
        )
      case 'tree':
        return (
          <TreeView
            searchQuery={searchQuery}
            selectedCollection={selectedCollection}
            onBookmarkMove={(bookmarkId, newPath) => {
              console.log('Bookmark moved:', bookmarkId, 'to:', newPath)
            }}
          />
        )
      case 'mindmap':
        return (
          <MindMapView
            searchQuery={searchQuery}
            selectedCollection={selectedCollection}
          />
        )
      default: { // grid
        // Emergency DOM protection: Force virtual scrolling for large datasets
        const shouldForceVirtualScrolling = filteredBookmarks.length > 50 || useVirtualScrolling

        return shouldForceVirtualScrolling ? (
          <VirtualizedBookmarkGrid
            searchQuery={searchQuery}
            selectedCollection={selectedCollection}
            onOpenPanel={onOpenPanel}
          />
        ) : (
          <div
            className="bookmark-grid"
            ref={setContainerRef}
            role="grid"
            aria-label="Bookmark grid"
          >
            {/* Limit rendering to prevent DOM explosion */}
            {filteredBookmarks.slice(0, 50).map((bookmark, _index) => (
              <BookmarkCardFlip
                key={bookmark.id}
                bookmark={bookmark}
                onToggleFavorite={toggleBookmarkFavorite}
                onOpenPanel={onOpenPanel}
              />
            ))}
            {filteredBookmarks.length > 50 && (
              <div className="load-more-indicator">
                Showing first 50 items. Virtual scrolling will enable automatically for better performance.
              </div>
            )}
          </div>
        )
      }
    }
  }

  return (
    <main className="bookmark-grid-container">
      <div className="grid-header">
        <div className="grid-info">
          <h2 className="grid-title">{getTitle()}</h2>
          <p className="grid-subtitle">
            {searchQuery && `Search results for "${searchQuery}"`}
            {useVirtualScrolling && (
              <span className="memory-optimization-indicator" title="Virtual scrolling enabled for performance">
                ⚡ Virtual Scrolling
              </span>
            )}
            {filteredBookmarks.length > 2000 && (
              <span className="memory-optimization-indicator" title="Memory optimizations enabled for performance">
                🧠 Optimized
              </span>
            )}
          </p>
        </div>
        <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
      </div>

      {/* Multimedia Playlist Quick Actions */}
      {filteredBookmarks.length > 0 && (
        <div className="multimedia-quick-actions" style={{
          display: 'flex',
          gap: '8px',
          padding: '12px 0',
          borderBottom: '1px solid #e5e7eb',
          marginBottom: '16px'
        }}>
          <button
            onClick={() => onOpenPanel?.('multimedia')}
            className="multimedia-action-btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              backgroundColor: 'var(--accent-color)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '12px',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            title="Create multimedia playlist from current bookmarks"
          >
            🎬 Create Playlist ({filteredBookmarks.length})
          </button>

          <button
            onClick={() => {
              // Quick video playlist action
              const videoBookmarks = filteredBookmarks.filter(b =>
                b.url.toLowerCase().includes('youtube.com') ||
                b.url.toLowerCase().includes('vimeo.com')
              )
              if (videoBookmarks.length > 0) {
                onOpenPanel?.('multimedia')
              }
            }}
            className="multimedia-action-btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              backgroundColor: '#EF4444',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '12px',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            title="Quick video playlist from YouTube/Vimeo bookmarks"
          >
            🎥 Videos ({filteredBookmarks.filter(b =>
              b.url.toLowerCase().includes('youtube.com') ||
              b.url.toLowerCase().includes('vimeo.com')
            ).length})
          </button>

          {/* Large Video Collection Helper */}
          {filteredBookmarks.filter(b =>
            b.url.toLowerCase().includes('youtube.com') ||
            b.url.toLowerCase().includes('vimeo.com') ||
            b.url.toLowerCase().includes('video')
          ).length > 100 && (
              <button
                onClick={() => onOpenPanel?.('multimedia')}
                className="multimedia-action-btn"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '6px 12px',
                  backgroundColor: 'var(--accent-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  fontWeight: '600'
                }}
                title="Advanced video selection for large collections"
              >
                🎯 Video Builder ({filteredBookmarks.filter(b =>
                  b.url.toLowerCase().includes('youtube.com') ||
                  b.url.toLowerCase().includes('vimeo.com') ||
                  b.url.toLowerCase().includes('video')
                ).length})
              </button>
            )}

          <button
            onClick={() => onOpenPanel?.('multimedia')}
            className="multimedia-action-btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              backgroundColor: '#10B981',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '12px',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            title="Create hands-free gym mode playlist with TTS"
          >
            🏃‍♂️ Gym Mode
          </button>
        </div>
      )}

      {renderContent()}

      {/* Memory Monitor */}
      <MemoryMonitor
        bookmarkCount={filteredBookmarks.length}
        isVisible={filteredBookmarks.length > 100}
        onOptimizationSuggestion={setOptimizationSuggestion}
      />

      {/* Optimization Suggestion Toast */}
      {optimizationSuggestion && (
        <div className="optimization-suggestion">
          <div className="suggestion-content">
            💡 {optimizationSuggestion}
            <button
              onClick={() => setOptimizationSuggestion(null)}
              className="suggestion-close"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </main>
  )
}
