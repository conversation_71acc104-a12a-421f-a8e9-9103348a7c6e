<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multimedia Panel Test</title>
    <link rel="stylesheet" href="src/styles/multimedia-design-system.css">
    <link rel="stylesheet" href="src/styles/multimedia-integration.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f1f5f9;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .theme-toggle {
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .theme-toggle button {
            margin-right: 10px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .theme-toggle button.active {
            background: #6366f1;
            color: white;
            border-color: #6366f1;
        }
        
        /* Mock icons */
        .icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            background: currentColor;
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor'%3E%3Cpath d='M23 7l-7 5 7 5V7z'/%3E%3Crect x='1' y='5' width='15' height='14' rx='2' ry='2'/%3E%3C/svg%3E") no-repeat center;
            mask-size: contain;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="theme-toggle">
            <h3>Theme Test</h3>
            <button id="classic-theme" class="active">Classic Theme</button>
            <button id="modern-theme">Modern Theme</button>
            <button id="dark-theme">Dark Theme</button>
        </div>
        
        <div id="multimedia-panel-container">
            <div class="multimedia-panel">
                <div class="multimedia-header">
                    <h2 class="multimedia-header-title">
                        <span class="icon"></span>
                        Multimedia Organization
                    </h2>
                    <button class="multimedia-close-button">
                        <span class="icon"></span>
                    </button>
                </div>

                <div class="multimedia-content">
                    <!-- Strategy Description -->
                    <div class="multimedia-section">
                        <div class="multimedia-section-header">
                            <h3 class="multimedia-section-title">
                                <span class="icon"></span>
                                Content Detection
                            </h3>
                        </div>
                        <div class="multimedia-section-content">
                            <p>
                                Organize your bookmarks into multimedia playlists based on content type.
                                Automatically detects videos, audio, and documents to create structured playlists.
                            </p>

                            <div class="multimedia-grid multimedia-grid--2">
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <div>
                                        <h4>Video Content</h4>
                                        <p>YouTube, Vimeo, and other video platforms</p>
                                    </div>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <div>
                                        <h4>Audio Content</h4>
                                        <p>Spotify, SoundCloud, and podcast links</p>
                                    </div>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <div>
                                        <h4>Document Content</h4>
                                        <p>Articles, PDFs, and documentation</p>
                                    </div>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <div>
                                        <h4>AI Enhancement</h4>
                                        <p>Smart organization with content analysis</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Analysis -->
                    <div class="multimedia-section">
                        <div class="multimedia-section-header">
                            <h3 class="multimedia-section-title">
                                <span class="icon"></span>
                                📊 Content Analysis
                            </h3>
                        </div>
                        <div class="multimedia-section-content">
                            <p>Analysis of multimedia content in your target bookmarks.</p>

                            <div class="multimedia-grid">
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <span class="stat-number">12</span>
                                    <span class="stat-label">Videos</span>
                                    <small>60 min</small>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <span class="stat-number">8</span>
                                    <span class="stat-label">Audio</span>
                                    <small>24 min</small>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <span class="stat-number">25</span>
                                    <span class="stat-label">Documents</span>
                                    <small>250 min</small>
                                </div>
                                <div class="multimedia-card">
                                    <span class="icon"></span>
                                    <span class="stat-number">334</span>
                                    <span class="stat-label">Total Minutes</span>
                                    <small>Estimated</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="multimedia-section">
                        <div class="multimedia-section-header">
                            <h3 class="multimedia-section-title">
                                <span class="icon"></span>
                                🚀 Actions
                            </h3>
                        </div>
                        <div class="multimedia-section-content">
                            <div class="multimedia-flex multimedia-gap-4">
                                <button class="multimedia-btn multimedia-btn--primary">
                                    <span class="icon"></span>
                                    Generate Preview
                                </button>
                                <button class="multimedia-btn multimedia-btn--secondary">
                                    <span class="icon"></span>
                                    Create Playlist
                                </button>
                                <button class="multimedia-btn multimedia-btn--ghost">
                                    <span class="icon"></span>
                                    Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme switching functionality
        const container = document.getElementById('multimedia-panel-container');
        const classicBtn = document.getElementById('classic-theme');
        const modernBtn = document.getElementById('modern-theme');
        const darkBtn = document.getElementById('dark-theme');

        function setTheme(theme) {
            // Remove all theme classes
            container.classList.remove('modern-theme', 'dark-theme');
            document.body.classList.remove('modern-theme', 'dark-theme');
            
            // Remove active class from all buttons
            document.querySelectorAll('.theme-toggle button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Apply new theme
            if (theme === 'modern') {
                container.classList.add('modern-theme');
                document.body.classList.add('modern-theme');
                modernBtn.classList.add('active');
                document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            } else if (theme === 'dark') {
                container.classList.add('dark-theme');
                document.body.classList.add('dark-theme');
                darkBtn.classList.add('active');
                document.body.style.background = '#0f172a';
            } else {
                classicBtn.classList.add('active');
                document.body.style.background = '#f1f5f9';
            }
        }

        classicBtn.addEventListener('click', () => setTheme('classic'));
        modernBtn.addEventListener('click', () => setTheme('modern'));
        darkBtn.addEventListener('click', () => setTheme('dark'));

        // Test button interactions
        document.querySelectorAll('.multimedia-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('Button clicked:', e.target.textContent.trim());
                
                // Add visual feedback
                btn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 150);
            });
        });

        // Test close button
        document.querySelector('.multimedia-close-button').addEventListener('click', () => {
            alert('Close button clicked - multimedia panel would close');
        });

        console.log('Multimedia panel test loaded successfully');
    </script>
</body>
</html>