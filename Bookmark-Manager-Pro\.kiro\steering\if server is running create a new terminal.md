# Terminal Management for Trae.ai IDE Execution Rules

This document describes the comprehensive terminal management solution implemented to ensure compliance with **Trae.ai IDE Execution Rules**. The solution provides multiple ways to properly execute tests while adhering to the requirement of opening a new terminal before running Python commands.

## Overview

The **Trae.ai IDE Execution Rules** require:
1. **Always open a new terminal** before executing Python tests
2. **Set the environment path** before running any Python commands
3. **Detect IDE-embedded terminals** and handle them appropriately
4. **Ignore existing command IDs for server processes** - when API or frontend servers are running, do not reuse their terminals; always launch new terminal windows for other commands

## Solution Components

This implementation provides four complementary approaches:

### 1. PowerShell Script (`run_tests_with_terminal.ps1`)

**Primary PowerShell-based solution with comprehensive terminal management.**

```powershell
# Basic usage
.\run_tests_with_terminal.ps1

# Run specific test type with verbose output
.\run_tests_with_terminal.ps1 -TestType "unit" -Verbose

# Run with coverage reporting
.\run_tests_with_terminal.ps1 -Coverage

# Run specific test file
.\run_tests_with_terminal.ps1 -TestPath "tests/test_core.py"
```

**Features:**
- Automatic IDE detection
- New terminal creation with proper environment setup
- Support for test filtering by type (unit, integration, performance, advanced)
- Coverage reporting integration
- Comprehensive error handling and logging

### 2. Batch File Wrapper (`run_tests.bat`)

**Simple batch file interface for easy command-line usage.**

```batch
# Basic usage
run_tests.bat

# Run with specific options
run_tests.bat --type unit --verbose
run_tests.bat --coverage
run_tests.bat --path "tests/test_core.py"

# Show help
run_tests.bat --help
```

**Features:**
- Cross-platform batch execution
- Command-line argument support
- Automatic PowerShell script invocation
- Built-in help documentation

### 3. Python Test Runner (`run_tests_terminal_safe.py`)

**Advanced Python-based solution with intelligent terminal detection.**

```python
# Basic usage
python run_tests_terminal_safe.py

# Run specific test type
python run_tests_terminal_safe.py --type unit --verbose

# Run with coverage
python run_tests_terminal_safe.py --coverage

# Force current terminal (not recommended in IDE)
python run_tests_terminal_safe.py --no-new-terminal
```

**Features:**
- Intelligent IDE detection using environment variables and process analysis
- Cross-platform terminal management (Windows/Unix)
- Automatic script generation for new terminal execution
- Comprehensive command-line interface
- Fallback execution in current terminal when needed

### 4. Compliance Module (`terminal_compliance.py`)

**Programmatic compliance checking and enforcement.**

```python
# Check compliance
python terminal_compliance.py

# Detailed report
python terminal_compliance.py --report

# Verbose checking
python terminal_compliance.py --verbose

# Use in code
from terminal_compliance import TerminalCompliance, check_terminal_compliance

# Quick check
is_compliant = check_terminal_compliance(verbose=True)

# Decorator for automatic compliance
@TerminalCompliance.enforce_compliance_decorator
def run_my_tests():
    # Your test code here
    pass
```

**Features:**
- Programmatic compliance verification
- Configurable rule enforcement
- Detailed compliance reporting
- Decorator-based automatic enforcement
- IDE detection and environment analysis

## Configuration

### Configuration File (`terminal_rules.json`)

The system uses a JSON configuration file to control behavior:

```json
{
  "rules": {
    "require_new_terminal_for_tests": true,
    "require_environment_path_setup": true,
    "detect_ide_embedded_terminals": true
  },
  "terminal_detection": {
    "ide_environment_variables": [
      "VSCODE_PID",
      "PYCHARM_HOSTED",
      "JUPYTER_SERVER_ROOT"
    ],
    "ide_process_names": [
      "code.exe",
      "pycharm64.exe",
      "jupyter.exe"
    ]
  }
}
```

## Usage Scenarios

### Scenario 1: Running Tests from VS Code Terminal

```bash
# The system detects VS Code and opens a new terminal
python run_tests_terminal_safe.py --type unit
```

### Scenario 2: Running Tests from Command Prompt

```batch
# Direct execution in current terminal (no IDE detected)
run_tests.bat --verbose
```

### Scenario 3: Programmatic Test Execution

```python
from terminal_compliance import TerminalCompliance

# Check compliance before running tests
compliance = TerminalCompliance()
is_compliant, message = compliance.check_compliance_for_tests()

if not is_compliant:
    print(f"Compliance issue: {message}")
    # Handle accordingly
else:
    # Run tests
    pass
```

### Scenario 4: Integration with Existing Test Scripts

```python
# Add to existing test runner
from terminal_compliance import check_terminal_compliance

def main():
    # Check compliance first
    if not check_terminal_compliance(verbose=True):
        print("Please use the terminal-safe test runner")
        return 1
    
    # Continue with existing test logic
    run_existing_tests()
```

## IDE Detection Methods

The system uses multiple methods to detect IDE-embedded terminals:

### Environment Variables
- `VSCODE_PID` - Visual Studio Code
- `PYCHARM_HOSTED` - PyCharm
- `JUPYTER_SERVER_ROOT` - Jupyter environments
- `SPYDER_ARGS` - Spyder IDE
- `THEIA_WORKSPACE_ROOT` - Theia-based IDEs

### Process Analysis (Windows)
- Parent process name detection
- Common IDE executable identification
- Process tree analysis

## Server Command ID Management

### Rule: Ignore Running Server Command IDs

When API servers, frontend development servers, or other long-running services are active with existing command IDs, **do not attempt to reuse those terminals**. Instead, always launch new terminal windows for additional commands.

#### Server Process Types to Ignore:
- **API Servers**: `python -m alaf.api.server`, `uvicorn`, `fastapi`, `flask run`
- **Frontend Servers**: `npm start`, `npm run dev`, `vite dev`, `webpack-dev-server`
- **Database Services**: `mongod`, `redis-server`, `postgresql`
- **Other Long-Running Services**: Any process intended to run continuously

#### Implementation Guidelines:

```python
# Example: Check if command ID represents a server process
def is_server_process(command_id, command_info):
    server_keywords = [
        'server', 'serve', 'dev', 'start', 'uvicorn', 
        'fastapi', 'flask', 'mongod', 'redis', 'postgresql'
    ]
    
    command_script = command_info.get('script', '').lower()
    return any(keyword in command_script for keyword in server_keywords)

# Always launch new terminal for non-server commands
if is_server_process(existing_command_id, command_info):
    # Launch new terminal instead of reusing
    launch_new_terminal(new_command)
else:
    # Safe to check status or reuse if appropriate
    check_command_status(existing_command_id)
```

#### Rationale:
- **Server Stability**: Prevents accidental termination of running services
- **Process Isolation**: Keeps server logs separate from other command outputs
- **Development Workflow**: Maintains clean separation between services and utilities
- **Resource Management**: Avoids conflicts between long-running and short-running processes

## Best Practices

### 1. Use the Appropriate Tool
- **PowerShell users**: Use `run_tests_with_terminal.ps1`
- **Command line users**: Use `run_tests.bat`
- **Python developers**: Use `run_tests_terminal_safe.py`
- **Integration scenarios**: Use `terminal_compliance.py`

### 2. Configure for Your Environment
- Modify `terminal_rules.json` for custom IDE detection
- Add environment variables for new IDEs
- Adjust path setup for different systems

### 3. Handle Edge Cases
- Use `--no-new-terminal` flag when absolutely necessary
- Check compliance reports for troubleshooting
- Monitor terminal detection accuracy

### 4. Integration with CI/CD
```yaml
# Example GitHub Actions integration
- name: Run Tests with Terminal Management
  run: python run_tests_terminal_safe.py --no-new-terminal --coverage
```

## Troubleshooting

### Common Issues

#### 1. "ModuleNotFoundError: No module named 'alaf'"
**Solution**: The scripts automatically handle environment path setup. If this persists:
```bash
# Check current directory
pwd
# Should be in project root

# Check Python path
python -c "import sys; print(sys.path)"
```

#### 2. "New terminal not opening"
**Solution**: Check terminal detection:
```bash
python terminal_compliance.py --report
```

#### 3. "Permission denied executing PowerShell script"
**Solution**: Set execution policy:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Debug Mode

Enable verbose output for troubleshooting:
```bash
# PowerShell
.\run_tests_with_terminal.ps1 -Verbose

# Python
python run_tests_terminal_safe.py --verbose

# Compliance check
python terminal_compliance.py --verbose --report
```

## File Structure

```
project_root/
├── run_tests_with_terminal.ps1    # PowerShell script
├── run_tests.bat                   # Batch wrapper
├── run_tests_terminal_safe.py      # Python runner
├── terminal_compliance.py          # Compliance module
├── terminal_rules.json             # Configuration
└── TERMINAL_MANAGEMENT_README.md   # This documentation
```

## Advanced Usage

### Custom Configuration

```python
# Load custom configuration
from terminal_compliance import TerminalCompliance

compliance = TerminalCompliance(config_path="custom_rules.json")
report = compliance.get_compliance_report()
```

### Programmatic Terminal Management

```python
from run_tests_terminal_safe import TerminalTestRunner

runner = TerminalTestRunner()
exit_code = runner.run_tests(
    test_path="specific/test/path",
    test_type="integration",
    verbose=True,
    coverage=True
)
```

### Integration with pytest

```python
# conftest.py
import pytest
from terminal_compliance import check_terminal_compliance

def pytest_configure(config):
    """Check compliance before running tests."""
    if not check_terminal_compliance():
        pytest.exit("Terminal compliance check failed. Use terminal-safe runner.")
```

## Compliance Verification

To verify that the solution meets Trae.ai IDE Execution Rules:

1. **Rule 1 - New Terminal**: All scripts detect IDE environments and open new terminals
2. **Rule 2 - Environment Path**: Scripts set proper Windows PATH before execution
3. **Rule 3 - IDE Detection**: Multiple detection methods ensure accurate identification

### Verification Commands

```bash
# Test compliance detection
python terminal_compliance.py --report

# Test new terminal opening
python run_tests_terminal_safe.py --type unit

# Test environment setup
.\run_tests_with_terminal.ps1 -TestType "unit" -Verbose
```

## Support and Maintenance

### Updating IDE Detection

To add support for new IDEs:

1. Update `terminal_rules.json`:
```json
{
  "terminal_detection": {
    "ide_environment_variables": [
      "NEW_IDE_ENV_VAR"
    ],
    "ide_process_names": [
      "new_ide.exe"
    ]
  }
}
```

2. Test detection:
```bash
python terminal_compliance.py --verbose
```

### Performance Monitoring

Monitor terminal management performance:
```bash
# Time the execution
Measure-Command { .\run_tests_with_terminal.ps1 }
```

## Enhanced Terminal Management with Windows Terminal (tmux-like functionality)

### Overview of Enhanced Features

While `tmux` is not natively available on Windows, **Windows Terminal (wt.exe)** provides similar multiplexing capabilities that can significantly enhance our terminal management solution. This section outlines recommendations for implementing tmux-like functionality using Windows Terminal.

### 5. Windows Terminal Session Manager (`wt_session_manager.py`)

**Advanced session management with Windows Terminal integration.**

```python
# Create named test sessions
python wt_session_manager.py --create-session "unit-tests" --type unit

# Run parallel test sessions
python wt_session_manager.py --parallel --sessions "unit,integration,performance"

# Attach to existing session
python wt_session_manager.py --attach "unit-tests"

# List active sessions
python wt_session_manager.py --list-sessions

# Kill specific session
python wt_session_manager.py --kill-session "unit-tests"
```

**Features:**
- Named session management with persistence
- Parallel test execution across multiple tabs/panes
- Session state tracking and recovery
- Integration with existing terminal compliance
- Windows Terminal profile management

### Enhanced Configuration (`terminal_rules_enhanced.json`)

```json
{
  "rules": {
    "require_new_terminal_for_tests": true,
    "require_environment_path_setup": true,
    "detect_ide_embedded_terminals": true,
    "enable_session_management": true,
    "enable_parallel_execution": true
  },
  "windows_terminal": {
    "executable_path": "wt.exe",
    "default_profile": "PowerShell",
    "session_persistence": true,
    "max_parallel_sessions": 4,
    "tab_naming_pattern": "Test-{type}-{timestamp}"
  },
  "session_management": {
    "session_storage_path": "./.terminal_sessions",
    "auto_cleanup_hours": 24,
    "session_recovery_enabled": true
  },
  "terminal_detection": {
    "ide_environment_variables": [
      "VSCODE_PID",
      "PYCHARM_HOSTED",
      "JUPYTER_SERVER_ROOT",
      "SPYDER_ARGS",
      "THEIA_WORKSPACE_ROOT"
    ],
    "ide_process_names": [
      "code.exe",
      "pycharm64.exe",
      "jupyter.exe",
      "spyder.exe"
    ]
  }
}
```

### Enhanced Usage Scenarios

#### Scenario 5: Parallel Test Execution

```powershell
# Run multiple test suites simultaneously
.\run_tests_with_terminal.ps1 -Parallel -TestTypes "unit,integration,performance"

# Using Windows Terminal session manager
python wt_session_manager.py --parallel --sessions "unit,integration" --verbose
```

#### Scenario 6: Persistent Development Sessions

```bash
# Create a persistent development session
python wt_session_manager.py --create-session "dev-main" --persistent

# Attach to session later
python wt_session_manager.py --attach "dev-main"

# Run tests in existing session
python wt_session_manager.py --run-in-session "dev-main" --command "pytest tests/"
```

#### Scenario 7: Multi-Environment Testing

```powershell
# Test across different Python environments
.\run_tests_with_terminal.ps1 -MultiEnv -Environments "python3.9,python3.10,python3.11"
```

### Implementation Recommendations

#### 1. Windows Terminal Integration

**Create `wt_session_manager.py`:**

```python
import subprocess
import json
import os
from datetime import datetime
from pathlib import Path

class WindowsTerminalSessionManager:
    def __init__(self, config_path="terminal_rules_enhanced.json"):
        self.config = self.load_config(config_path)
        self.session_storage = Path(self.config["session_management"]["session_storage_path"])
        self.session_storage.mkdir(exist_ok=True)
    
    def create_session(self, session_name, test_type=None, persistent=False):
        """Create a new Windows Terminal session."""
        profile = self.config["windows_terminal"]["default_profile"]
        tab_name = f"Test-{test_type or 'general'}-{datetime.now().strftime('%H%M%S')}"
        
        # Build Windows Terminal command
        wt_cmd = [
            "wt.exe",
            "new-tab",
            "--profile", profile,
            "--title", tab_name,
            "powershell.exe", "-NoExit", "-Command",
            f"cd '{os.getcwd()}'; Write-Host 'Session {session_name} created'"
        ]
        
        # Execute and track session
        process = subprocess.Popen(wt_cmd)
        self.save_session_info(session_name, {
            "pid": process.pid,
            "tab_name": tab_name,
            "test_type": test_type,
            "created": datetime.now().isoformat(),
            "persistent": persistent
        })
        
        return process.pid
    
    def run_parallel_sessions(self, test_types):
        """Run multiple test sessions in parallel."""
        sessions = []
        for test_type in test_types:
            session_name = f"{test_type}-{datetime.now().strftime('%H%M%S')}"
            pid = self.create_session(session_name, test_type)
            sessions.append((session_name, pid))
        
        return sessions
```

#### 2. Enhanced PowerShell Script Updates

**Add to `run_tests_with_terminal.ps1`:**

```powershell
# Add parallel execution support
param(
    [string]$TestType = "all",
    [switch]$Parallel,
    [string[]]$TestTypes = @(),
    [switch]$UseWindowsTerminal,
    [string]$SessionName = ""
)

function Start-ParallelTestSessions {
    param(
        [string[]]$TestTypes
    )
    
    $sessions = @()
    foreach ($type in $TestTypes) {
        $tabName = "Test-$type-$(Get-Date -Format 'HHmmss')"
        $wtCmd = "wt.exe new-tab --title '$tabName' powershell.exe -NoExit -Command `"cd '$PWD'; .\run_tests_with_terminal.ps1 -TestType '$type'`""
        
        $process = Start-Process -FilePath "wt.exe" -ArgumentList "new-tab", "--title", $tabName, "powershell.exe", "-NoExit", "-Command", "cd '$PWD'; .\run_tests_with_terminal.ps1 -TestType '$type'" -PassThru
        $sessions += @{ Type = $type; PID = $process.Id; TabName = $tabName }
    }
    
    return $sessions
}

if ($Parallel -and $TestTypes.Count -gt 0) {
    Write-Host "Starting parallel test sessions..." -ForegroundColor Green
    $sessions = Start-ParallelTestSessions -TestTypes $TestTypes
    
    foreach ($session in $sessions) {
        Write-Host "Started $($session.Type) tests in tab '$($session.TabName)' (PID: $($session.PID))" -ForegroundColor Cyan
    }
    
    exit 0
}
```

#### 3. Session Persistence and Recovery

**Create `session_recovery.py`:**

```python
import json
import psutil
from pathlib import Path
from datetime import datetime, timedelta

class SessionRecovery:
    def __init__(self, session_storage_path="./.terminal_sessions"):
        self.storage_path = Path(session_storage_path)
        self.storage_path.mkdir(exist_ok=True)
    
    def cleanup_stale_sessions(self, max_age_hours=24):
        """Clean up sessions older than specified hours."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        for session_file in self.storage_path.glob("*.json"):
            with open(session_file) as f:
                session_data = json.load(f)
            
            created_time = datetime.fromisoformat(session_data["created"])
            if created_time < cutoff_time:
                # Check if process is still running
                if not psutil.pid_exists(session_data["pid"]):
                    session_file.unlink()
                    print(f"Cleaned up stale session: {session_file.stem}")
    
    def recover_sessions(self):
        """Attempt to recover active sessions."""
        active_sessions = []
        
        for session_file in self.storage_path.glob("*.json"):
            with open(session_file) as f:
                session_data = json.load(f)
            
            if psutil.pid_exists(session_data["pid"]):
                active_sessions.append({
                    "name": session_file.stem,
                    "data": session_data
                })
        
        return active_sessions
```

### Benefits of Enhanced Terminal Management

#### 1. **Session Persistence**
- Named sessions that survive IDE restarts
- Automatic session recovery after system crashes
- Session state tracking and management

#### 2. **Parallel Execution**
- Run multiple test suites simultaneously
- Improved development workflow efficiency
- Better resource utilization

#### 3. **Enhanced Debugging**
- Dedicated tabs for different test types
- Persistent terminal history
- Easy switching between test contexts

#### 4. **Better Organization**
- Named tabs with meaningful titles
- Session categorization by test type
- Visual separation of different test runs

### Migration Path

#### Phase 1: Basic Windows Terminal Integration
1. Install Windows Terminal (if not already available)
2. Create `wt_session_manager.py`
3. Update existing scripts to support `--use-wt` flag

#### Phase 2: Session Management
1. Implement session persistence
2. Add session recovery mechanisms
3. Create session cleanup utilities

#### Phase 3: Advanced Features
1. Add parallel execution support
2. Implement multi-environment testing
3. Create session monitoring and analytics

### Verification Commands for Enhanced Features

```bash
# Test Windows Terminal integration
python wt_session_manager.py --create-session "test-wt" --type unit

# Test parallel execution
.\run_tests_with_terminal.ps1 -Parallel -TestTypes "unit,integration"

# Test session recovery
python session_recovery.py --recover

# Test session cleanup
python session_recovery.py --cleanup --max-age 12
```

## Conclusion

This enhanced terminal management solution extends the original Trae.ai IDE compliance framework with tmux-like functionality through Windows Terminal integration. The solution maintains full compliance with IDE execution rules while adding powerful session management, parallel execution, and persistent terminal capabilities.

Key improvements include:
- **Session Management**: Named, persistent sessions with recovery capabilities
- **Parallel Execution**: Multiple test suites running simultaneously
- **Enhanced Organization**: Better tab management and visual separation
- **Improved Debugging**: Persistent history and dedicated test contexts
- **Scalability**: Support for complex development workflows

The phased implementation approach ensures smooth migration from the existing solution while providing immediate benefits. For questions or issues, refer to the troubleshooting section or examine the compliance reports for detailed diagnostic information.

### Next Steps

1. **Immediate**: Test Windows Terminal availability and basic integration
2. **Short-term**: Implement basic session management features
3. **Medium-term**: Add parallel execution and advanced session features
4. **Long-term**: Develop comprehensive session analytics and monitoring

This enhanced solution transforms the terminal management system from a compliance-focused tool into a powerful development productivity platform while maintaining all original safety and compliance guarantees.  <!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have Kiro refine them for you:   
-------------------------------------------------------------------------------------> 