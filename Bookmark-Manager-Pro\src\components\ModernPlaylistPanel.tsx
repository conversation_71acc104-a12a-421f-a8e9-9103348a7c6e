/**
 * MODER<PERSON> PLAYLIST PANEL WITH STREAMING RECOMMENDATIONS
 * Implements progressive enhancement and user feedback loops
 */

import { Info, Sparkles, ThumbsDown, ThumbsUp, X, Zap } from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import type {
  FeedbackType,
  RankedRecommendation,
  StreamingRecommendation
} from '../services/recommendation/interfaces'

interface ModernPlaylistPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const ModernPlaylistPanel: React.FC<ModernPlaylistPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, playlists, createPlaylist } = useBookmarks()
  const { currentTheme } = useTheme()
  // Unified theme system - no more modern/classic distinction
  const isModernTheme = false

  // State management
  const [recommendations, setRecommendations] = useState<StreamingRecommendation[]>([])
  const [loading, setLoading] = useState(false)
  const [streamingActive, setStreamingActive] = useState(false)
  const [selectedRecommendation, setSelectedRecommendation] = useState<RankedRecommendation | null>(null)
  const [showExplanation, setShowExplanation] = useState(false)
  const [feedbackGiven, setFeedbackGiven] = useState(new Set<string>())

  // Refs for streaming control
  const streamRef = useRef<any>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.close()
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Start streaming recommendations
  const startStreamingRecommendations = useCallback(async () => {
    if (streamingActive || bookmarks.length === 0) return

    setLoading(true)
    setStreamingActive(true)
    setRecommendations([])

    try {
      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController()

      // Simulate streaming recommendations (replace with actual service)
      const stream = await simulateRecommendationStream({
        userId: 'current-user',
        context: {
          bookmarks,
          existingPlaylists: playlists,
          userProfile: {
            userId: 'current-user',
            preferences: {
              favoriteTopics: [],
              preferredContentTypes: [],
              timePatterns: [],
              diversityPreference: 0.7,
              noveltyPreference: 0.6
            },
            interactions: [],
            embeddings: { id: 'current-user', vector: [], dimension: 0, model: 'user_profile' },
            clusters: [],
            lastUpdated: new Date()
          }
        },
        maxRecommendations: 10,
        onRecommendation: (recommendation) => {
          setRecommendations(prev => [...prev, { ...recommendation, isStreaming: true }])
        },
        onExplanation: (explanation) => {
          console.log('Explanation received:', explanation)
        },
        onError: (error) => {
          console.error('Streaming error:', error)
          setStreamingActive(false)
        }
      })

      streamRef.current = stream
    } catch (error) {
      console.error('Failed to start streaming:', error)
      setStreamingActive(false)
    } finally {
      setLoading(false)
    }
  }, [bookmarks, playlists, streamingActive])

  // Stop streaming
  const stopStreaming = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.close()
      streamRef.current = null
    }
    setStreamingActive(false)
  }, [])

  // Handle feedback
  const handleFeedback = useCallback(async (recommendationId: string, feedback: FeedbackType) => {
    try {
      // Record feedback (replace with actual service call)
      await recordFeedback(recommendationId, feedback)

      setFeedbackGiven(prev => new Set([...prev, recommendationId]))

      // Update recommendation display
      setRecommendations(prev =>
        prev.map(rec =>
          rec.id === recommendationId
            ? { ...rec, userFeedback: feedback }
            : rec
        )
      )
    } catch (error) {
      console.error('Failed to record feedback:', error)
    }
  }, [])

  // Create playlist from recommendation
  const createPlaylistFromRecommendation = useCallback(async (recommendation: RankedRecommendation) => {
    try {
      await createPlaylist(
        recommendation.name,
        recommendation.description,
        '#4a9eff', // Default color
        recommendation.bookmarkIds
      )

      // Record positive feedback
      await handleFeedback(recommendation.id, 'created_playlist')

      console.log(`Created playlist: ${recommendation.name}`)
    } catch (error) {
      console.error('Failed to create playlist:', error)
    }
  }, [createPlaylist, handleFeedback])

  // Show explanation modal
  const showRecommendationExplanation = useCallback((recommendation: RankedRecommendation) => {
    setSelectedRecommendation(recommendation)
    setShowExplanation(true)
  }, [])

  if (!isOpen) return null

  return (
    <div className={`playlist-panel ${isModernTheme ? 'modern-theme' : 'classic-theme'}`}>
      <div className="panel-header">
        <h2 className="panel-title">
          <Sparkles className="title-icon" />
          Smart Playlists
        </h2>
        <button onClick={onClose} className="close-button">
          <X size={20} />
        </button>
      </div>

      <div className="panel-content">
        {/* Streaming Controls */}
        <div className="streaming-controls">
          <button
            onClick={streamingActive ? stopStreaming : startStreamingRecommendations}
            className={`stream-button ${streamingActive ? 'active' : ''}`}
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="loading-spinner" />
                Generating...
              </>
            ) : streamingActive ? (
              <>
                <Zap className="button-icon" />
                Stop Streaming
              </>
            ) : (
              <>
                <Sparkles className="button-icon" />
                Get AI Recommendations
              </>
            )}
          </button>
        </div>

        {/* Recommendations List */}
        <div className="recommendations-container">
          {recommendations.length === 0 && !loading && (
            <div className="empty-state">
              <Sparkles size={48} className="empty-icon" />
              <h3>No Recommendations Yet</h3>
              <p>Click "Get AI Recommendations" to discover smart playlist suggestions based on your bookmarks.</p>
            </div>
          )}

          {recommendations.map((recommendation, index) => (
            <RecommendationCard
              key={recommendation.id}
              recommendation={recommendation}
              index={index}
              onFeedback={handleFeedback}
              onCreatePlaylist={createPlaylistFromRecommendation}
              onShowExplanation={showRecommendationExplanation}
              feedbackGiven={feedbackGiven.has(recommendation.id)}
              isModernTheme={isModernTheme}
            />
          ))}
        </div>
      </div>

      {/* Explanation Modal */}
      {showExplanation && selectedRecommendation && (
        <ExplanationModal
          recommendation={selectedRecommendation}
          onClose={() => setShowExplanation(false)}
          isModernTheme={isModernTheme}
        />
      )}
    </div>
  )
}

// Recommendation Card Component
interface RecommendationCardProps {
  recommendation: StreamingRecommendation
  index: number
  onFeedback: (id: string, feedback: FeedbackType) => void
  onCreatePlaylist: (recommendation: RankedRecommendation) => void
  onShowExplanation: (recommendation: RankedRecommendation) => void
  feedbackGiven: boolean
  isModernTheme: boolean
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  index,
  onFeedback,
  onCreatePlaylist,
  onShowExplanation,
  feedbackGiven,
  isModernTheme
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div
      className={`recommendation-card ${isModernTheme ? 'modern-card' : 'classic-card'} ${recommendation.isStreaming ? 'streaming' : ''}`}
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className="card-header">
        <div className="card-title-section">
          <h3 className="card-title">{recommendation.name}</h3>
          <div className="card-metadata">
            <span className="confidence-badge">
              {(recommendation.confidence * 100).toFixed(0)}% confidence
            </span>
            <span className="bookmark-count">
              {recommendation.bookmarkIds.length} bookmarks
            </span>
          </div>
        </div>
        <div className="card-actions">
          <button
            onClick={() => onShowExplanation(recommendation)}
            className="action-button explanation-button"
            title="Why was this recommended?"
          >
            <Info size={16} />
          </button>
        </div>
      </div>

      <div className="card-content">
        <p className="card-description">{recommendation.description}</p>

        {isExpanded && (
          <div className="card-details">
            <div className="recommendation-metrics">
              <div className="metric">
                <span className="metric-label">Diversity:</span>
                <span className="metric-value">{(recommendation.diversity * 100).toFixed(0)}%</span>
              </div>
              <div className="metric">
                <span className="metric-label">Novelty:</span>
                <span className="metric-value">{(recommendation.novelty * 100).toFixed(0)}%</span>
              </div>
              <div className="metric">
                <span className="metric-label">Source:</span>
                <span className="metric-value">{recommendation.source}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="card-footer">
        <div className="feedback-section">
          {!feedbackGiven ? (
            <>
              <button
                onClick={() => onFeedback(recommendation.id, 'like')}
                className="feedback-button like-button"
                title="This is a good recommendation"
              >
                <ThumbsUp size={16} />
              </button>
              <button
                onClick={() => onFeedback(recommendation.id, 'dislike')}
                className="feedback-button dislike-button"
                title="This is not a good recommendation"
              >
                <ThumbsDown size={16} />
              </button>
            </>
          ) : (
            <span className="feedback-given">Thanks for your feedback!</span>
          )}
        </div>

        <div className="action-section">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="toggle-button"
          >
            {isExpanded ? 'Show Less' : 'Show More'}
          </button>
          <button
            onClick={() => onCreatePlaylist(recommendation)}
            className="create-button primary-button"
          >
            Create Playlist
          </button>
        </div>
      </div>
    </div>
  )
}

// Explanation Modal Component
interface ExplanationModalProps {
  recommendation: RankedRecommendation
  onClose: () => void
  isModernTheme: boolean
}

const ExplanationModal: React.FC<ExplanationModalProps> = ({
  recommendation,
  onClose,
  isModernTheme
}) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div
        className={`explanation-modal ${isModernTheme ? 'modern-modal' : 'classic-modal'}`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          <h3>Why was this recommended?</h3>
          <button onClick={onClose} className="modal-close">
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          <div className="explanation-content">
            <h4>{recommendation.name}</h4>
            <p className="primary-explanation">{recommendation.explanation.primary}</p>

            {recommendation.explanation.secondary.length > 0 && (
              <div className="secondary-explanations">
                <h5>Additional factors:</h5>
                <ul>
                  {recommendation.explanation.secondary.map((explanation, index) => (
                    <li key={index}>{explanation}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="explanation-factors">
              <h5>Recommendation factors:</h5>
              {recommendation.explanation.factors.map((factor, index) => (
                <div key={index} className="factor">
                  <div className="factor-header">
                    <span className="factor-type">{factor.type.replace('_', ' ')}</span>
                    <span className="factor-weight">{(factor.weight * 100).toFixed(0)}%</span>
                  </div>
                  <p className="factor-description">{factor.description}</p>
                  {factor.evidence.length > 0 && (
                    <ul className="factor-evidence">
                      {factor.evidence.map((evidence, evidenceIndex) => (
                        <li key={evidenceIndex}>{evidence}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Utility functions (to be replaced with actual service calls)
async function simulateRecommendationStream(options: any) {
  // Simulate streaming recommendations
  const recommendations = [
    {
      id: 'rec-1',
      name: 'Frontend Development Essentials',
      description: 'Core resources for modern frontend development',
      bookmarkIds: ['1', '2', '3'],
      confidence: 0.92,
      rank: 1,
      score: 0.95,
      explanation: {
        primary: 'Based on your frequent visits to React and TypeScript resources',
        secondary: ['Similar users also created this type of playlist'],
        confidence: 0.92,
        factors: []
      },
      diversity: 0.8,
      novelty: 0.6,
      type: 'playlist' as const,
      source: 'hybrid' as const,
      features: { contentFeatures: [], collaborativeFeatures: [], temporalFeatures: [], contextualFeatures: [] }
    }
    // Add more mock recommendations...
  ]

  let index = 0
  const interval = setInterval(() => {
    if (index < recommendations.length) {
      options.onRecommendation(recommendations[index])
      index++
    } else {
      clearInterval(interval)
    }
  }, 1000)

  return {
    close: () => clearInterval(interval),
    pause: () => { },
    resume: () => { },
    updateContext: () => { }
  }
}

async function recordFeedback(recommendationId: string, feedback: FeedbackType) {
  // Simulate feedback recording
  console.log(`Feedback recorded: ${recommendationId} -> ${feedback}`)
}
