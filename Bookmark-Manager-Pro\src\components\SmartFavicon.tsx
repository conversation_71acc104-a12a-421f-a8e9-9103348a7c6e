import React, { useState } from 'react';
import { getFaviconUrl, handleFaviconError, handleFaviconSuccess } from '../services/faviconService';

interface SmartFaviconProps {
  url: string;
  title: string;
  favicon?: string;
  priority?: 'high' | 'low';
  lazy?: boolean;
  className?: string;
}

const SmartFavicon: React.FC<SmartFaviconProps> = ({
  url,
  title,
  favicon,
  priority = 'low',
  lazy = true,
  className = 'favicon-image'
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const handleImageError = () => {
    setImageError(true);
    // Silently handle favicon errors to prevent console spam
    try {
      handleFaviconError(url);
    } catch (error) {
      // Suppress any errors from the favicon service
    }
  };

  const handleImageLoad = () => {
    setIsLoaded(true);
    if (faviconUrl) {
      handleFaviconSuccess(url, faviconUrl);
    }
  };

  // Generate favicon URL using the favicon service
  const faviconUrl = getFaviconUrl(url, favicon, priority);

  // Fallback letter icon
  const fallbackLetter = title.charAt(0).toUpperCase();

  // Show favicon if available and no error
  if (faviconUrl && !imageError) {
    return (
      <img
        src={faviconUrl}
        alt={`${title} favicon`}
        className={className}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading={lazy ? "lazy" : "eager"}
        decoding="async"
        style={{
          width: '32px',
          height: '32px',
          objectFit: 'contain',
          opacity: isLoaded ? 1 : 0.7,
          transition: 'opacity 0.2s ease'
        }}
      />
    );
  }

  // Fallback to letter icon
  return (
    <div
      className="favicon-fallback"
      style={{
        width: '32px',
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        borderRadius: '4px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        border: '1px solid #e5e7eb'
      }}
    >
      {fallbackLetter}
    </div>
  );
};

export default SmartFavicon;
