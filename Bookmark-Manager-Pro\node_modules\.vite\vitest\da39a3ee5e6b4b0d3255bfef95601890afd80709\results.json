{"version": "3.2.4", "results": [[":src/tests/playlist.test.tsx", {"duration": 326.91859999999997, "failed": true}], [":src/test/components/FileUpload.test.tsx", {"duration": 4082.0243, "failed": true}], [":tests/comprehensive-testing.spec.js", {"duration": 0, "failed": true}], [":tests/mobile-optimization.spec.js", {"duration": 0, "failed": true}], [":tests/cross-browser.spec.js", {"duration": 0, "failed": true}], [":tests/domain-organization.spec.js", {"duration": 0, "failed": true}], [":tests/accessibility.spec.js", {"duration": 0, "failed": true}], [":tests/performance.spec.js", {"duration": 0, "failed": true}], [":src/tests/playlist-integration.test.ts", {"duration": 3340.1814999999997, "failed": true}], [":tests/core-web-vitals.spec.js", {"duration": 0, "failed": true}], [":src/components/__tests__/BookmarkItem.test.tsx", {"duration": 1376.5103, "failed": true}], [":tests/collection-color-consistency.test.js", {"duration": 0, "failed": true}], [":tests/modern-theme.test.js", {"duration": 0, "failed": true}], [":tests/visual-regression.spec.js", {"duration": 0, "failed": true}], [":tests/collection-colors.test.js", {"duration": 0, "failed": true}], [":tests/memory-optimization.test.js", {"duration": 0, "failed": true}], [":tests/home-button-e2e.spec.js", {"duration": 0, "failed": true}], [":src/test/bookmark-minimal.test.tsx", {"duration": 102.27460000000019, "failed": false}], [":src/test/minimal-debug.test.tsx", {"duration": 37.01269999999977, "failed": false}], [":src/test/bookmark-simple.test.tsx", {"duration": 22.796100000000024, "failed": false}], [":src/test/basic.test.ts", {"duration": 3.991999999999962, "failed": false}], [":tests/vibe/core/star-interaction-confidence.spec.js", {"duration": 0, "failed": true}]]}