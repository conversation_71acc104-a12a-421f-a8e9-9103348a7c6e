import {
  Check,
  Download,
  ListMusic,
  Search,
  SplitSquareVertical,
  Upload,
  X
} from 'lucide-react'
import React from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

interface HeaderProps {
  onToggleImport: () => void
  onToggleExport: () => void
  onToggleSplit: () => void
  onTogglePlaylist: () => void
  importPanelOpen: boolean
  exportPanelOpen: boolean
  splitPanelOpen: boolean
  playlistPanelOpen: boolean
}

export const Header: React.FC<HeaderProps> = ({
  onToggleImport,
  onToggleExport,
  onToggleSplit,
  onTogglePlaylist,
  importPanelOpen,
  exportPanelOpen,
  splitPanelOpen,
  playlistPanelOpen
}) => {
  const {
    searchQuery,
    setSearchQuery,
    filterType,
    setFilterType,
    filteredBookmarks,
    isSelectMode,
    toggleSelectMode,
    selectedBookmarks,
    selectAllBookmarks,
    deselectAllBookmarks,
    selectedPlaylist
  } = useBookmarks()

  return (
    <header className="header">
      <div className="header-left">
        <h1 className="header-title">Bookmark Studio</h1>
        <span className="bookmark-count">
          {isSelectMode
            ? `${selectedBookmarks.length} selected / ${filteredBookmarks.length} total`
            : `${filteredBookmarks.length} bookmarks`}
        </span>
      </div>

      {!isSelectMode ? (
        <div className="header-center">
          <div className="search-container">
            <Search className="search-icon" size={20} />
            <input
              type="text"
              placeholder="Search bookmarks, tags, or descriptions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="search-clear"
                aria-label="Clear search"
              >
                ×
              </button>
            )}
          </div>

          <div className="filter-buttons">
            <button
              onClick={() => setFilterType('all')}
              className={`filter-btn ${filterType === 'all' ? 'active' : ''}`}
            >
              All
            </button>
            <button
              onClick={() => setFilterType('favorites')}
              className={`filter-btn ${filterType === 'favorites' ? 'active' : ''}`}
            >
              Favorites
            </button>
            <button
              onClick={() => setFilterType('recent')}
              className={`filter-btn ${filterType === 'recent' ? 'active' : ''}`}
            >
              Recent
            </button>
          </div>
        </div>
      ) : (
        <div className="header-center selection-controls">
          <div className="selection-message">
            Select bookmarks to delete, export, or split
          </div>
          <div className="selection-actions">
            <button
              onClick={selectAllBookmarks}
              className="selection-action-btn"
              aria-label="Select all"
            >
              Select All
            </button>
            <button
              onClick={deselectAllBookmarks}
              className="selection-action-btn"
              aria-label="Deselect all"
            >
              Deselect All
            </button>
          </div>
        </div>
      )}

      {!isSelectMode ? (
        <div className="header-right">
          <button
            onClick={onToggleImport}
            className={`import-btn ${importPanelOpen ? 'active' : ''}`}
            aria-label="Toggle import panel"
          >
            <Upload size={18} />
            Import
          </button>

          <button
            onClick={onToggleExport}
            className={`action-btn ${exportPanelOpen ? 'active' : ''}`}
            aria-label="Export bookmarks"
          >
            <Download size={18} />
          </button>

          <button
            onClick={onToggleSplit}
            className={`action-btn ${splitPanelOpen ? 'active' : ''}`}
            aria-label="Split bookmarks"
          >
            <SplitSquareVertical size={18} />
          </button>

          <button
            onClick={onTogglePlaylist}
            className={`action-btn ${playlistPanelOpen ? 'active' : ''} ${selectedPlaylist ? 'playlist-active' : ''}`}
            aria-label="Manage playlists"
          >
            <ListMusic size={18} />
          </button>

          <button
            onClick={toggleSelectMode}
            className="action-btn"
            aria-label="Select bookmarks"
          >
            <Check size={18} />
          </button>
        </div>
      ) : (
        <div className="header-right">
          <button
            onClick={toggleSelectMode}
            className="action-btn danger"
            aria-label="Cancel selection"
          >
            <X size={18} />
          </button>

          <button
            onClick={onToggleSplit}
            className={`action-btn ${splitPanelOpen ? 'active' : ''}`}
            disabled={selectedBookmarks.length === 0}
            aria-label="Split selected bookmarks"
          >
            <SplitSquareVertical size={18} />
          </button>

          <button
            onClick={onToggleExport}
            className={`action-btn ${exportPanelOpen ? 'active' : ''}`}
            disabled={selectedBookmarks.length === 0}
            aria-label="Export selected bookmarks"
          >
            <Download size={18} />
          </button>

          <button
            onClick={onTogglePlaylist}
            className={`action-btn ${playlistPanelOpen ? 'active' : ''} ${selectedPlaylist ? 'playlist-active' : ''}`}
            aria-label="Save to playlist"
          >
            <ListMusic size={18} />
          </button>
        </div>
      )}
    </header>
  )
}
