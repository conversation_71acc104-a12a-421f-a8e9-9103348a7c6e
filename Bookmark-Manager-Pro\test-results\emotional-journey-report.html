
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emotional Journey Report - Dr<PERSON> <PERSON>'s Vibe Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.5em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
        }
        .recommendation {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #f39c12;
        }
        .recommendation.high {
            border-left-color: #e74c3c;
        }
        .recommendation.critical {
            border-left-color: #c0392b;
        }
        .recommendation h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 Emotional Journey Report</h1>
            <p>Dr. Elena's Vibe Testing Analysis - Favorites System</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 Journey Summary</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">6</div>
                        <div class="metric-label">Total Sessions</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">12</div>
                        <div class="metric-label">Satisfaction Points</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Frustration Points</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Delight Moments</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">100.0%</div>
                        <div class="metric-label">Satisfaction Ratio</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0.00</div>
                        <div class="metric-label">Delight Frequency</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🌊 Emotional Flow Analysis</h2>
                <div class="chart-container">
                    <canvas id="emotionalFlowChart"></canvas>
                </div>
                <h3>Identified Patterns:</h3>
                
            </div>
            
            <div class="section">
                <h2>😊 Satisfaction Analysis</h2>
                <div class="chart-container">
                    <canvas id="satisfactionChart"></canvas>
                </div>
                <p><strong>Average Intensity:</strong> 7.5/10</p>
            </div>
            
            <div class="section">
                <h2>😤 Frustration Analysis</h2>
                <div class="chart-container">
                    <canvas id="frustrationChart"></canvas>
                </div>
                <p><strong>Resolution Rate:</strong> 0.0%</p>
                <p><strong>Average Intensity:</strong> 0.0/10</p>
            </div>
            
            <div class="section">
                <h2>✨ Delight Analysis</h2>
                <div class="chart-container">
                    <canvas id="delightChart"></canvas>
                </div>
                <p><strong>Frequency:</strong> 0.00 moments per session</p>
                <p><strong>Average Intensity:</strong> 0.0/10</p>
            </div>
            
            <div class="section">
                <h2>💡 Recommendations</h2>
                <div class="recommendations">
                    
                        <div class="recommendation medium">
                            <h4>MEDIUM: delight</h4>
                            <p><strong>Issue:</strong> Low frequency of delight moments</p>
                            <p><strong>Recommendation:</strong> Add more surprising and delightful micro-interactions</p>
                        </div>
                    
                        <div class="recommendation high">
                            <h4>HIGH: frustration</h4>
                            <p><strong>Issue:</strong> Low frustration resolution rate</p>
                            <p><strong>Recommendation:</strong> Improve error recovery and user guidance systems</p>
                        </div>
                    
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Report generated on 17/07/2025, 07:40:25
        </div>
    </div>
    
    <script>
        // Emotional Flow Chart
        const emotionalFlowCtx = document.getElementById('emotionalFlowChart').getContext('2d');
        new Chart(emotionalFlowCtx, {
            type: 'doughnut',
            data: {
                labels: ["curious","confident","satisfied"],
                datasets: [{
                    data: [6,6,6],
                    backgroundColor: ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#ffeaa7', '#fab1a0', '#fd79a8']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Emotional State Distribution'
                    }
                }
            }
        });
        
        // Satisfaction Chart
        const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
        new Chart(satisfactionCtx, {
            type: 'bar',
            data: {
                labels: ["visual-feedback","accomplishment"],
                datasets: [{
                    label: 'Satisfaction Points',
                    data: [6,6],
                    backgroundColor: '#43e97b'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Satisfaction by Category'
                    }
                }
            }
        });
        
        // Frustration Chart
        const frustrationCtx = document.getElementById('frustrationChart').getContext('2d');
        new Chart(frustrationCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Frustration Points',
                    data: [],
                    backgroundColor: '#fd79a8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Frustration by Category'
                    }
                }
            }
        });
        
        // Delight Chart
        const delightCtx = document.getElementById('delightChart').getContext('2d');
        new Chart(delightCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Delight Moments',
                    data: [],
                    backgroundColor: '#ffeaa7'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Delight by Category'
                    }
                }
            }
        });
    </script>
</body>
</html>