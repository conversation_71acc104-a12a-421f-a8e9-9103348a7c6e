import { AlertCircle, CheckCircle, FileText, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ap } from 'lucide-react'
import React from 'react'
import { useTheme } from '../contexts/ThemeContext'
import useSummaryGeneration from '../hooks/useSummaryGeneration'

interface SummaryGenerationPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SummaryGenerationPanel: React.FC<SummaryGenerationPanelProps> = React.memo(({
  isOpen,
  onClose
}) => {
  const {
    state,
    bookmarksNeedingSummaries,
    generateSummariesForAll,
    clearSummaryCache
  } = useSummaryGeneration()

  // Theme integration
  const { currentTheme } = useTheme()

  // Generate theme-aware classes
  const panelClasses = `import-panel`

  if (!isOpen) return null

  return (
    <div className={panelClasses}>
      <div className="import-header">
        <h2 className="import-title">
          📝 Summary Generation
        </h2>
        <button onClick={onClose} className="close-btn" aria-label="Close summary generation panel">
          <FileText size={20} />
        </button>
      </div>

      <div className="import-content" style={{
        maxHeight: 'calc(85vh - 120px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollBehavior: 'smooth'
      }}>
        {/* Summary Generation Overview */}
        <div className="import-section">
          <h3 className="section-title">
            <Zap size={16} />
            Summary Generation Overview
          </h3>
          <p className="section-description">
            Automatically generate intelligent summaries for your bookmarks. Get quick 1-sentence overviews
            and detailed content analysis from web pages, YouTube videos, GitHub repositories, and more.
          </p>

          <div className="summary-stats">
            <div className="stat-card">
              <div className="stat-number">{bookmarksNeedingSummaries.length}</div>
              <div className="stat-label">Need Summaries</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">{state.completed}</div>
              <div className="stat-label">Completed</div>
            </div>
            {state.errors.length > 0 && (
              <div className="stat-card error">
                <div className="stat-number">{state.errors.length}</div>
                <div className="stat-label">Errors</div>
              </div>
            )}
          </div>
        </div>

        {/* Generation Controls */}
        <div className="import-section">
          <h3 className="section-title">
            <RefreshCw size={16} />
            Generation Controls
          </h3>

          <div className="form-group">
            <button
              onClick={generateSummariesForAll}
              disabled={state.isGenerating}
              className={`import-btn primary ${state.isGenerating ? 'loading' : ''}`}
            >
              {state.isGenerating ? (
                <>
                  <div className="spinner" />
                  Generating... ({state.completed}/{state.total})
                </>
              ) : (
                <>
                  <Zap size={16} />
                  Generate All Summaries ({bookmarksNeedingSummaries.length})
                </>
              )}
            </button>

            <button
              onClick={clearSummaryCache}
              className="import-btn secondary"
              disabled={state.isGenerating}
            >
              <RefreshCw size={16} />
              Clear Cache
            </button>
          </div>

          {bookmarksNeedingSummaries.length === 0 && (
            <div className="no-bookmarks-message">
              <CheckCircle size={24} />
              <p>All bookmarks have summaries!</p>
              <small>New bookmarks will automatically get summaries when added.</small>
            </div>
          )}
        </div>

        {/* Enhanced Progress Section */}
        {state.isGenerating && (
          <div className="import-section">
            <h3 className="section-title">
              <Loader2 size={16} className="animate-spin" />
              📊 Generation Progress
            </h3>
            <div className="progress-section" style={{
              padding: '20px',
              backgroundColor: 'var(--bg-secondary)',
              borderRadius: '12px',
              border: '1px solid var(--border-color)'
            }}>
              {/* Progress Bar */}
              <div style={{ marginBottom: '16px' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '8px'
                }}>
                  <span style={{ fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)' }}>
                    Processing Summaries
                  </span>
                  <span style={{ fontSize: '14px', fontWeight: '500', color: 'var(--accent-color)' }}>
                    {Math.round(state.progress)}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '12px',
                  backgroundColor: 'var(--bg-tertiary)',
                  borderRadius: '6px',
                  overflow: 'hidden',
                  position: 'relative'
                }}>
                  <div
                    style={{
                      width: `${state.progress}%`,
                      height: '100%',
                      background: 'linear-gradient(90deg, var(--accent-color), var(--accent-color-light))',
                      borderRadius: '6px',
                      transition: 'width 0.3s ease',
                      position: 'relative'
                    }}
                  >
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                      animation: 'shimmer 2s infinite'
                    }} />
                  </div>
                </div>
              </div>

              {/* Status Information */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '16px',
                marginBottom: '16px'
              }}>
                <div style={{
                  padding: '12px',
                  backgroundColor: 'var(--bg-primary)',
                  borderRadius: '8px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '20px', fontWeight: '700', color: 'var(--accent-color)' }}>
                    {state.completed}
                  </div>
                  <div style={{ fontSize: '12px', color: 'var(--text-secondary)' }}>
                    Completed
                  </div>
                </div>
                <div style={{
                  padding: '12px',
                  backgroundColor: 'var(--bg-primary)',
                  borderRadius: '8px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '20px', fontWeight: '700', color: 'var(--text-primary)' }}>
                    {state.total - state.completed}
                  </div>
                  <div style={{ fontSize: '12px', color: 'var(--text-secondary)' }}>
                    Remaining
                  </div>
                </div>
              </div>

              {/* Current Processing */}
              <div style={{
                padding: '12px',
                backgroundColor: 'var(--bg-primary)',
                borderRadius: '8px',
                border: '1px solid var(--accent-color)',
                marginBottom: '12px'
              }}>
                <div style={{
                  fontSize: '12px',
                  color: 'var(--text-secondary)',
                  marginBottom: '4px',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  Currently Processing
                </div>
                <div style={{
                  fontSize: '14px',
                  color: 'var(--text-primary)',
                  fontWeight: '500',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {state.currentBookmark || 'Preparing next bookmark...'}
                </div>
              </div>

              {/* Estimated Time */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                fontSize: '12px',
                color: 'var(--text-secondary)'
              }}>
                <span>
                  📊 {state.completed} of {state.total} bookmarks
                </span>
                <span>
                  ⏱️ Est. {Math.max(1, Math.ceil((state.total - state.completed) * 0.5))}min remaining
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Feature Overview */}
        <div className="import-section">
          <h3 className="section-title">
            <FileText size={16} />
            Summary Features
          </h3>
          <p className="section-description">
            Our intelligent summary system provides different types of analysis based on content type:
          </p>

          <div className="feature-list">
            <div className="feature-item">
              <div className="feature-icon youtube">🎥</div>
              <div className="feature-content">
                <h4>YouTube Videos</h4>
                <p>Video title, channel, duration, and key points from content analysis</p>
              </div>
            </div>

            <div className="feature-item">
              <div className="feature-icon github">💻</div>
              <div className="feature-content">
                <h4>GitHub Repositories</h4>
                <p>Repository description, language, stars, and project overview</p>
              </div>
            </div>

            <div className="feature-item">
              <div className="feature-icon docs">📚</div>
              <div className="feature-content">
                <h4>Documentation & Articles</h4>
                <p>Content summary, reading time, author, and key concepts</p>
              </div>
            </div>

            <div className="feature-item">
              <div className="feature-icon webpage">🌐</div>
              <div className="feature-content">
                <h4>Web Pages</h4>
                <p>Page summary, meta information, and content analysis</p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {state.errors.length > 0 && (
          <div className="import-section">
            <h3 className="section-title">
              <AlertCircle size={16} />
              Generation Errors ({state.errors.length})
            </h3>

            <div className="error-list">
              {state.errors.slice(0, 5).map((error, index) => (
                <div key={index} className="error-item">
                  <AlertCircle size={14} />
                  <span>{error}</span>
                </div>
              ))}
              {state.errors.length > 5 && (
                <div className="error-item">
                  <span>+{state.errors.length - 5} more errors</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Usage Tips */}
        <div className="import-section">
          <h3 className="section-title">
            💡 Usage Tips
          </h3>
          <div className="tips-list">
            <div className="tip-item">
              <strong>Flip Cards:</strong> Click the flip icon on any bookmark card to see detailed summaries and content analysis
            </div>
            <div className="tip-item">
              <strong>Auto-Generation:</strong> New bookmarks automatically get summaries when imported or added
            </div>
            <div className="tip-item">
              <strong>Content Types:</strong> Different content types (YouTube, GitHub, etc.) get specialized analysis
            </div>
            <div className="tip-item">
              <strong>Performance:</strong> Summaries are cached for faster loading on subsequent views
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})
