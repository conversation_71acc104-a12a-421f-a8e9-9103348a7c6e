{"config": {"configFile": "C:\\Nexicon\\Bookmark-Manager-Pro\\playwright.config.js", "rootDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\global-setup.js", "globalTeardown": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\global-teardown.js", "globalTimeout": 600000, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 7}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}], ["C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\reporters\\vibe-metrics-reporter.js", null], ["C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\reporters\\emotional-journey-reporter.js", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "chromium", "name": "chromium", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "firefox", "name": "firefox", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "webkit", "name": "webkit", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "vibe-desktop", "name": "vibe-desktop", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "vibe-mobile", "name": "vibe-mobile", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 7}, "id": "vibe-accessibility", "name": "vibe-accessibility", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "vibe\\core\\star-interaction-confidence.spec.js", "file": "vibe/core/star-interaction-confidence.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Star Interaction Confidence - Vibe Testing", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 16, "column": 6, "specs": [{"title": "Star interaction provides immediate confident feedback", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 4115, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m\"poor\"\u001b[39m\nReceived array: \u001b[31m[\"excellent\", \"acceptable\"]\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m\"poor\"\u001b[39m\nReceived array: \u001b[31m[\"excellent\", \"acceptable\"]\u001b[39m\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:95:41", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 41, "line": 95}, "snippet": "\u001b[0m \u001b[90m 93 |\u001b[39m     expect(emotionalFeedback\u001b[33m.\u001b[39moverallQuality)\u001b[33m.\u001b[39mtoBe(\u001b[32m'excellent'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 94 |\u001b[39m     \u001b[90m// For vibe testing, accept both excellent and acceptable response quality\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 95 |\u001b[39m     expect([\u001b[32m'excellent'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'acceptable'\u001b[39m])\u001b[33m.\u001b[39mtoContain(responseMetrics\u001b[33m.\u001b[39mquality)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 96 |\u001b[39m\n \u001b[90m 97 |\u001b[39m     \u001b[90m// Attach vibe metrics for reporting\u001b[39m\n \u001b[90m 98 |\u001b[39m     \u001b[36mawait\u001b[39m test\u001b[33m.\u001b[39minfo()\u001b[33m.\u001b[39mattach(\u001b[32m'vibe-metrics'\u001b[39m\u001b[33m,\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 41, "line": 95}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m\"poor\"\u001b[39m\nReceived array: \u001b[31m[\"excellent\", \"acceptable\"]\u001b[39m\n\n\u001b[0m \u001b[90m 93 |\u001b[39m     expect(emotionalFeedback\u001b[33m.\u001b[39moverallQuality)\u001b[33m.\u001b[39mtoBe(\u001b[32m'excellent'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 94 |\u001b[39m     \u001b[90m// For vibe testing, accept both excellent and acceptable response quality\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 95 |\u001b[39m     expect([\u001b[32m'excellent'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'acceptable'\u001b[39m])\u001b[33m.\u001b[39mtoContain(responseMetrics\u001b[33m.\u001b[39mquality)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 96 |\u001b[39m\n \u001b[90m 97 |\u001b[39m     \u001b[90m// Attach vibe metrics for reporting\u001b[39m\n \u001b[90m 98 |\u001b[39m     \u001b[36mawait\u001b[39m test\u001b[33m.\u001b[39minfo()\u001b[33m.\u001b[39mattach(\u001b[32m'vibe-metrics'\u001b[39m\u001b[33m,\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:95:41\u001b[22m"}], "stdout": [{"text": "Number of bookmarks found: \u001b[33m21\u001b[39m\n"}, {"text": "Initial aria-label: Remove from favorites\n"}, {"text": "Initial class: favorite-btn-inline active starred\n"}, {"text": "Updated aria-label: Add to favorites\n"}, {"text": "Updated class: favorite-btn-inline \n"}, {"text": "✅ Favorite state changed successfully - functionality is working\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.241Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 41, "line": 95}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-59a213993d1ed97c080d", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 32, "column": 7}, {"title": "Star visual states feel distinct and satisfying", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 4007, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\n\nExpected: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js-snapshots\\star-unstarred-state-vibe-desktop-win32.png\u001b[39m\nReceived: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-actual.png\u001b[39m\n    Diff: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(star-unstarred-state.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\n\nExpected: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js-snapshots\\star-unstarred-state-vibe-desktop-win32.png\u001b[39m\nReceived: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-actual.png\u001b[39m\n    Diff: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(star-unstarred-state.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:121:32", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 32, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m\n \u001b[90m 120 |\u001b[39m     \u001b[90m// Capture unstarred state - should feel neutral but inviting\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m     \u001b[36mawait\u001b[39m expect(bookmarkItem)\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'star-unstarred-state.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m\n \u001b[90m 123 |\u001b[39m     \u001b[90m// Test hover state - should feel interactive and inviting\u001b[39m\n \u001b[90m 124 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mhover()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 32, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\n\nExpected: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js-snapshots\\star-unstarred-state-vibe-desktop-win32.png\u001b[39m\nReceived: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-actual.png\u001b[39m\n    Diff: \u001b[33mC:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(star-unstarred-state.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-item\"], .bookmark-card, .list-row').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"bookmark-card-flip \" data-testid=\"bookmark-item\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.\u001b[22m\n\n\n\u001b[0m \u001b[90m 119 |\u001b[39m\n \u001b[90m 120 |\u001b[39m     \u001b[90m// Capture unstarred state - should feel neutral but inviting\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m     \u001b[36mawait\u001b[39m expect(bookmarkItem)\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'star-unstarred-state.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m\n \u001b[90m 123 |\u001b[39m     \u001b[90m// Test hover state - should feel interactive and inviting\u001b[39m\n \u001b[90m 124 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mhover()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:121:32\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.241Z", "annotations": [], "attachments": [{"name": "star-unstarred-state-expected.png", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js-snapshots\\star-unstarred-state-vibe-desktop-win32.png"}, {"name": "star-unstarred-state-actual.png", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-actual.png"}, {"name": "star-unstarred-state-diff.png", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\star-unstarred-state-diff.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 32, "line": 121}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-a47be6f47c33f9068bb4", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 110, "column": 7}, {"title": "Double-clicking star feels intentional, not broken", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 8795, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:186:30", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 186}, "snippet": "\u001b[0m \u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Allow state to update\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 186 |\u001b[39m     \u001b[36mawait\u001b[39m expect(starButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|starred|favorited/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// Second click immediately - should unstar smoothly\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m doubleClickMetrics \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[33mVibeMetrics\u001b[39m\u001b[33m.\u001b[39mmeasureResponseTime(page\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 186}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n\n\n\u001b[0m \u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Allow state to update\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 186 |\u001b[39m     \u001b[36mawait\u001b[39m expect(starButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|starred|favorited/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// Second click immediately - should unstar smoothly\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m doubleClickMetrics \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[33mVibeMetrics\u001b[39m\u001b[33m.\u001b[39mmeasureResponseTime(page\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:186:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.238Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 186}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-5c881c318d403f2d8fb7", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 174, "column": 7}, {"title": "Rapid starring maintains flow state without anxiety", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 4482, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"excellent\"\u001b[39m\nReceived: \u001b[31m\"acceptable\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"excellent\"\u001b[39m\nReceived: \u001b[31m\"acceptable\"\u001b[39m\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:247:37", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 37, "line": 247}, "snippet": "\u001b[0m \u001b[90m 245 |\u001b[39m     \u001b[90m// Verify no flow-breaking elements appeared\u001b[39m\n \u001b[90m 246 |\u001b[39m     expect(flowMetrics\u001b[33m.\u001b[39mdisruptionCount)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m     expect(flowMetrics\u001b[33m.\u001b[39mflowQuality)\u001b[33m.\u001b[39mtoBe(\u001b[32m'excellent'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m\n \u001b[90m 249 |\u001b[39m     \u001b[90m// Verify all stars were applied correctly (no missed clicks)\u001b[39m\n \u001b[90m 250 |\u001b[39m     \u001b[36mconst\u001b[39m starredCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 37, "line": 247}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"excellent\"\u001b[39m\nReceived: \u001b[31m\"acceptable\"\u001b[39m\n\n\u001b[0m \u001b[90m 245 |\u001b[39m     \u001b[90m// Verify no flow-breaking elements appeared\u001b[39m\n \u001b[90m 246 |\u001b[39m     expect(flowMetrics\u001b[33m.\u001b[39mdisruptionCount)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m     expect(flowMetrics\u001b[33m.\u001b[39mflowQuality)\u001b[33m.\u001b[39mtoBe(\u001b[32m'excellent'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m\n \u001b[90m 249 |\u001b[39m     \u001b[90m// Verify all stars were applied correctly (no missed clicks)\u001b[39m\n \u001b[90m 250 |\u001b[39m     \u001b[36mconst\u001b[39m starredCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:247:37\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.238Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 37, "line": 247}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-2ae4a120b26fcf97734a", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 221, "column": 7}, {"title": "Star feedback prevents uncertainty pause", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 3961, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:323:88", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 88, "line": 323}, "snippet": "\u001b[0m \u001b[90m 321 |\u001b[39m     }\u001b[33m,\u001b[39m \u001b[33mSELECTORS\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 322 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 323 |\u001b[39m     expect(confidenceIndicators\u001b[33m.\u001b[39mhasVisualChange \u001b[33m||\u001b[39m confidenceIndicators\u001b[33m.\u001b[39mhasAriaUpdate)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 324 |\u001b[39m     expect(confidenceIndicators\u001b[33m.\u001b[39mnoLoadingState)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 325 |\u001b[39m\n \u001b[90m 326 |\u001b[39m     \u001b[90m// Test that user can immediately continue without waiting (if multiple bookmarks exist)\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 88, "line": 323}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 321 |\u001b[39m     }\u001b[33m,\u001b[39m \u001b[33mSELECTORS\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 322 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 323 |\u001b[39m     expect(confidenceIndicators\u001b[33m.\u001b[39mhasVisualChange \u001b[33m||\u001b[39m confidenceIndicators\u001b[33m.\u001b[39mhasAriaUpdate)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 324 |\u001b[39m     expect(confidenceIndicators\u001b[33m.\u001b[39mnoLoadingState)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 325 |\u001b[39m\n \u001b[90m 326 |\u001b[39m     \u001b[90m// Test that user can immediately continue without waiting (if multiple bookmarks exist)\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:323:88\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.241Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 88, "line": 323}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-5acb4bb00128d4949080", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 286, "column": 7}, {"title": "Star animation feels polished and satisfying", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 3355, "error": {"message": "Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')\n    at eval (eval at evaluate (:291:30), <anonymous>:3:55)\n    at new Promise (<anonymous>)\n    at eval (eval at evaluate (:291:30), <anonymous>:2:14)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "stack": "Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')\n    at eval (eval at evaluate (:291:30), <anonymous>:3:55)\n    at new Promise (<anonymous>)\n    at eval (eval at evaluate (:291:30), <anonymous>:2:14)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:55)\n    at eval (eval at evaluate (:291:30), <anonymous>:2:14)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:358:41", "location": {"file": "eval at evaluate (:291:30), <anonymous>", "column": 55, "line": 3}}, "errors": [{"location": {"file": "eval at evaluate (:291:30), <anonymous>", "column": 55, "line": 3}, "message": "Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')\n\u001b[2m    at eval (eval at evaluate (:291:30), <anonymous>:3:55)\u001b[22m\n\u001b[2m    at new Promise (<anonymous>)\u001b[22m\n\u001b[2m    at eval (eval at evaluate (:291:30), <anonymous>:2:14)\u001b[22m\n\u001b[2m    at UtilityScript.evaluate (<anonymous>:293:16)\u001b[22m\n\u001b[2m    at UtilityScript.<anonymous> (<anonymous>:1:44)\u001b[22m\n\u001b[2m    at eval (eval at evaluate (:291:30), <anonymous>:3:55)\u001b[22m\n\u001b[2m    at eval (eval at evaluate (:291:30), <anonymous>:2:14)\u001b[22m\n\u001b[2m    at UtilityScript.evaluate (<anonymous>:293:16)\u001b[22m\n\u001b[2m    at UtilityScript.<anonymous> (<anonymous>:1:44)\u001b[22m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:358:41\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.258Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "eval at evaluate (:291:30), <anonymous>", "column": 55, "line": 3}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-9eea6ae02deff27b6f0c", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 348, "column": 7}, {"title": "Star state persists across navigation without anxiety", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "vibe-desktop", "projectName": "vibe-desktop", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "failed", "duration": 8701, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n\n    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:454:30", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 454}, "snippet": "\u001b[0m \u001b[90m 452 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 453 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Allow state to update\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 454 |\u001b[39m     \u001b[36mawait\u001b[39m expect(starButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|starred|favorited/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 455 |\u001b[39m\n \u001b[90m 456 |\u001b[39m     \u001b[90m// Navigate away (simulate interruption by refreshing the page)\u001b[39m\n \u001b[90m 457 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mreload()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 454}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\nExpected pattern: \u001b[32m/active|starred|favorited/\u001b[39m\nReceived string:  \u001b[31m\"favorite-btn-inline \"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"bookmark-star\"], .star-button, .favorite-btn').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <button data-testid=\"bookmark-star\" class=\"favorite-btn-inline \" aria-label=\"Add to favorites\">…</button>\u001b[22m\n\u001b[2m      - unexpected value \"favorite-btn-inline \"\u001b[22m\n\n\n\u001b[0m \u001b[90m 452 |\u001b[39m     \u001b[36mawait\u001b[39m starButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 453 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Allow state to update\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 454 |\u001b[39m     \u001b[36mawait\u001b[39m expect(starButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|starred|favorited/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 455 |\u001b[39m\n \u001b[90m 456 |\u001b[39m     \u001b[90m// Navigate away (simulate interruption by refreshing the page)\u001b[39m\n \u001b[90m 457 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mreload()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js:454:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-17T13:55:54.285Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Nexicon\\Bookmark-Manager-Pro\\test-results\\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\\error-context.md"}], "errorLocation": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\star-interaction-confidence.spec.js", "column": 30, "line": 454}}], "status": "unexpected"}], "id": "31f1cf37b03ee400975c-08c3b850b117bc88b25e", "file": "vibe/core/star-interaction-confidence.spec.js", "line": 442, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-17T13:55:49.540Z", "duration": 13666.372000000001, "expected": 0, "skipped": 0, "unexpected": 7, "flaky": 0}}