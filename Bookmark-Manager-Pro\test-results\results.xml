<testsuites id="" name="" tests="7" failures="7" skipped="0" errors="0" time="13.666372">
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T13:55:53.541Z" hostname="vibe-desktop" tests="7" failures="7" skipped="0" time="37.416" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="4.115">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "poor"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="4.007">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: expect(locator).toHaveScreenshot(expected)

      Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.

    Expected: C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png
    Received: C:\Nexicon\Bookmark-Manager-Pro\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png
        Diff: C:\Nexicon\Bookmark-Manager-Pro\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png

    Call log:
      - Expect "toHaveScreenshot(star-unstarred-state.png)" with timeout 5000ms
        - verifying given screenshot expectation
      - waiting for locator('[data-testid="bookmark-item"], .bookmark-card, .list-row').first()
        - locator resolved to <div class="bookmark-card-flip " data-testid="bookmark-item">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.
      - waiting 100ms before taking screenshot
      - waiting for locator('[data-testid="bookmark-item"], .bookmark-card, .list-row').first()
        - locator resolved to <div class="bookmark-card-flip " data-testid="bookmark-item">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - captured a stable screenshot
      - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.


      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |                                ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:32

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-state-diff.png (image/png) ───────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="8.795">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="4.482">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="3.961">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="3.355">
<failure message="star-interaction-confidence.spec.js:348:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:348:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:358:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.701">
<failure message="star-interaction-confidence.spec.js:442:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:442:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      452 |     await starButton.click();
      453 |     await page.waitForTimeout(100); // Allow state to update
    > 454 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      455 |
      456 |     // Navigate away (simulate interruption by refreshing the page)
      457 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:454:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>