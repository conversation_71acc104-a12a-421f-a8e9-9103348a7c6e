<testsuites id="" name="" tests="70" failures="70" skipped="0" errors="0" time="102.043065">
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="chromium" tests="7" failures="7" skipped="0" time="39.374" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="4.099">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "unacceptable"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="5.2">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-chromium-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-chromium-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-chromium-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-chromium-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-chromium-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-chromium-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-chromium-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-chromium-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-chromium-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-chromium-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-chromium-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-chromium-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\star-starred-hover-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="9.092">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="4.991">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="3.842">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="3.167">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.983">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [chromium] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="firefox" tests="7" failures="7" skipped="0" time="60.872" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="6.661">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "unacceptable"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="8.316">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-firefox-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-firefox-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-firefox-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-firefox-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-firefox-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-firefox-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-firefox-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-firefox-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-firefox-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-firefox-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-firefox-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-firefox-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\star-starred-hover-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="11.785">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.625">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "poor"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="7.492">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="6.144">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: selectors is undefined
    @debugger eval code line 291 > eval:3:45
    @debugger eval code line 291 > eval:2:14
    evaluate@debugger eval code:293:16
    @debugger eval code:1:44

        at @debugger eval code line 291 > eval:3:45
        at @debugger eval code line 291 > eval:2:14
        at evaluate@debugger eval code:293:16
        at @debugger eval code:1:44
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="11.849">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [firefox] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="webkit" tests="7" failures="7" skipped="0" time="46.568" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="6.144">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "unacceptable"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="10.889">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-webkit-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-webkit-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-webkit-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-webkit-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-webkit-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-webkit-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-webkit-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-webkit-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-webkit-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-webkit-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-webkit-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-webkit-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\star-starred-hover-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="8.4">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="6.72">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "poor"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="3.561">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.684">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: undefined is not an object (evaluating 'selectors.bookmarkStar')

      354 |
      355 |     // Test animation quality and timing
    > 356 |     const animationMetrics = await page.evaluate((selectors) => {
          |                                         ^
      357 |       return new Promise((resolve) => {
      358 |         const star = document.querySelector(selectors.bookmarkStar.split(', ')[0]); // Use first selector
      359 |         if (!star) {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.17">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [webkit] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="Mobile Chrome" tests="7" failures="7" skipped="0" time="97.559" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="12.992">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      4 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      2 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
      - retrying click action
        - waiting 500ms


      62 |     // Measure response time and emotional feedback
      63 |     const responseMetrics = await VibeMetrics.measureResponseTime(page, async () => {
    > 64 |       await starButton.click();
         |                        ^
      65 |     }, 200); // 200ms threshold for good feedback (more realistic)
      66 |
      67 |     // Wait for the state to update and check if the bookmark is now favorited
        at action (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:64:24)
        at VibeMetrics.measureResponseTime (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\utils\vibe-metrics.js:8:11)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:63:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="12.914">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Chrome-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    TimeoutError: locator.hover: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting hover action
        2 × waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
        - waiting 20ms
        - waiting for element to be visible and stable
        - element is visible and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying hover action
          - waiting 100ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      4 × retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      2 × retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
      - retrying hover action
        - waiting 500ms


      122 |
      123 |     // Test hover state - should feel interactive and inviting
    > 124 |     await starButton.hover();
          |                      ^
      125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
      126 |
      127 |     // Star the bookmark - should feel satisfying
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:124:22

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Chrome-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\star-unstarred-state-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="12.436">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      4 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      2 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
      - retrying click action
        - waiting 500ms


      182 |
      183 |     // First click - star the bookmark
    > 184 |     await starButton.click();
          |                      ^
      185 |     await page.waitForTimeout(100); // Allow state to update
      186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      187 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:184:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="32.15">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Test timeout of 30000ms exceeded.

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 5

      244 |
      245 |     // Verify no flow-breaking elements appeared
    > 246 |     expect(flowMetrics.disruptionCount).toBe(0);
          |                                         ^
      247 |     expect(flowMetrics.flowQuality).toBe('excellent');
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:246:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="12.586">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      4 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      2 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
      - retrying click action
        - waiting 500ms


      294 |
      295 |     // Click star and immediately check for uncertainty indicators
    > 296 |     await starButton.click();
          |                      ^
      297 |     await page.waitForTimeout(100); // Allow state to update
      298 |
      299 |     // User should not need to wonder "did that work?"
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:296:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="1.883">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="12.598">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [Mobile Chrome] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      4 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      2 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
      - retrying click action
        - waiting 500ms


      448 |
      449 |     // Star a bookmark
    > 450 |     await starButton.click();
          |                      ^
      451 |     await page.waitForTimeout(100); // Allow state to update
      452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      453 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:450:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="Mobile Safari" tests="7" failures="7" skipped="0" time="95.994" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="12.239">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms
        - waiting for element to be visible, enabled and stable


      62 |     // Measure response time and emotional feedback
      63 |     const responseMetrics = await VibeMetrics.measureResponseTime(page, async () => {
    > 64 |       await starButton.click();
         |                        ^
      65 |     }, 200); // 200ms threshold for good feedback (more realistic)
      66 |
      67 |     // Wait for the state to update and check if the bookmark is now favorited
        at action (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:64:24)
        at VibeMetrics.measureResponseTime (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\utils\vibe-metrics.js:8:11)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:63:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="12.898">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Safari-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    TimeoutError: locator.hover: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting hover action
        2 × waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
        - waiting 20ms
        - waiting for element to be visible and stable
        - element is visible and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying hover action
          - waiting 100ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying hover action
        - waiting 500ms
        - waiting for element to be visible and stable


      122 |
      123 |     // Test hover state - should feel interactive and inviting
    > 124 |     await starButton.hover();
          |                      ^
      125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
      126 |
      127 |     // Star the bookmark - should feel satisfying
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:124:22

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Safari-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Mobile-Safari-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\star-unstarred-state-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="12.3">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms


      182 |
      183 |     // First click - star the bookmark
    > 184 |     await starButton.click();
          |                      ^
      185 |     await page.waitForTimeout(100); // Allow state to update
      186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      187 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:184:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="30.415">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Test timeout of 30000ms exceeded.

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 5

      244 |
      245 |     // Verify no flow-breaking elements appeared
    > 246 |     expect(flowMetrics.disruptionCount).toBe(0);
          |                                         ^
      247 |     expect(flowMetrics.flowQuality).toBe('excellent');
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:246:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="12.848">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms


      294 |
      295 |     // Click star and immediately check for uncertainty indicators
    > 296 |     await starButton.click();
          |                      ^
      297 |     await page.waitForTimeout(100); // Allow state to update
      298 |
      299 |     // User should not need to wonder "did that work?"
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:296:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.495">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: undefined is not an object (evaluating 'selectors.bookmarkStar')

      354 |
      355 |     // Test animation quality and timing
    > 356 |     const animationMetrics = await page.evaluate((selectors) => {
          |                                         ^
      357 |       return new Promise((resolve) => {
      358 |         const star = document.querySelector(selectors.bookmarkStar.split(', ')[0]); // Use first selector
      359 |         if (!star) {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="12.799">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [Mobile Safari] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms


      448 |
      449 |     // Star a bookmark
    > 450 |     await starButton.click();
          |                      ^
      451 |     await page.waitForTimeout(100); // Allow state to update
      452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      453 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:450:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="Microsoft Edge" tests="7" failures="7" skipped="0" time="36.404" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="3.888">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "unacceptable"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="4.975">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Microsoft-Edge-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Microsoft-Edge-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Microsoft-Edge-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Microsoft-Edge-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Microsoft-Edge-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Microsoft-Edge-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Microsoft-Edge-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Microsoft-Edge-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Microsoft-Edge-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Microsoft-Edge-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Microsoft-Edge-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Microsoft-Edge-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\star-starred-hover-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="8.377">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="4.427">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="3.308">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.841">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.588">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [Microsoft Edge] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Microsoft-Edge\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="Google Chrome" tests="7" failures="7" skipped="0" time="34.914" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="3.603">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "poor"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-af28a-mmediate-confident-feedback-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="5.032">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Google-Chrome-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Google-Chrome-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Google-Chrome-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Google-Chrome-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Google-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Google-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Google-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Google-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-Google-Chrome-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-Google-Chrome-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-Google-Chrome-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-Google-Chrome-win32.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\star-starred-hover-actual.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-88b41-eel-distinct-and-satisfying-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="8.379">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-33289-eels-intentional-not-broken-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="4.159">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e896b--flow-state-without-anxiety-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="3.252">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-5f5dd--prevents-uncertainty-pause-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.152">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-b4c77-els-polished-and-satisfying-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="8.337">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [Google Chrome] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        8 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\video.webm]]

[[ATTACHMENT|vibe-core-star-interaction-e424b--navigation-without-anxiety-Google-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="vibe-desktop" tests="7" failures="7" skipped="0" time="31.141" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="3.844">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    Error: expect(received).toContain(expected) // indexOf

    Expected value: "unacceptable"
    Received array: ["excellent", "acceptable"]

      93 |     expect(emotionalFeedback.overallQuality).toBe('excellent');
      94 |     // For vibe testing, accept both excellent and acceptable response quality
    > 95 |     expect(['excellent', 'acceptable']).toContain(responseMetrics.quality);
         |                                         ^
      96 |
      97 |     // Attach vibe metrics for reporting
      98 |     await test.info().attach('vibe-metrics', {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:95:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="3.162">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: expect(locator).toHaveScreenshot(expected)

      Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.

    Expected: C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png
    Received: C:\Nexicon\Bookmark-Manager-Pro\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png
        Diff: C:\Nexicon\Bookmark-Manager-Pro\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png

    Call log:
      - Expect "toHaveScreenshot(star-unstarred-state.png)" with timeout 5000ms
        - verifying given screenshot expectation
      - waiting for locator('[data-testid="bookmark-item"], .bookmark-card, .list-row').first()
        - locator resolved to <div class="bookmark-card-flip " data-testid="bookmark-item">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.
      - waiting 100ms before taking screenshot
      - waiting for locator('[data-testid="bookmark-item"], .bookmark-card, .list-row').first()
        - locator resolved to <div class="bookmark-card-flip " data-testid="bookmark-item">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - captured a stable screenshot
      - Expected an image 361px by 280px, received 363px by 280px. 2592 pixels (ratio 0.03 of all image pixels) are different.


      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |                                ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:32

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-state-diff.png (image/png) ───────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-desktop-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-actual.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\star-unstarred-state-diff.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="8.235">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="3.7">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="2.481">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.206">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="7.513">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [vibe-desktop] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-desktop\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="vibe-mobile" tests="7" failures="7" skipped="0" time="96.206" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="12.914">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms
        - waiting for element to be visible, enabled and stable


      62 |     // Measure response time and emotional feedback
      63 |     const responseMetrics = await VibeMetrics.measureResponseTime(page, async () => {
    > 64 |       await starButton.click();
         |                        ^
      65 |     }, 200); // 200ms threshold for good feedback (more realistic)
      66 |
      67 |     // Wait for the state to update and check if the bookmark is now favorited
        at action (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:64:24)
        at VibeMetrics.measureResponseTime (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\utils\vibe-metrics.js:8:11)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:63:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="13.746">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-mobile-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    TimeoutError: locator.hover: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting hover action
        2 × waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
        - waiting 20ms
        - waiting for element to be visible and stable
        - element is visible and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying hover action
          - waiting 100ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying hover action
          - waiting 500ms
          - waiting for element to be visible and stable
          - element is visible and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying hover action
        - waiting 500ms
        - waiting for element to be visible and stable


      122 |
      123 |     // Test hover state - should feel interactive and inviting
    > 124 |     await starButton.hover();
          |                      ^
      125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
      126 |
      127 |     // Star the bookmark - should feel satisfying
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:124:22

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-mobile-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-mobile-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\star-unstarred-state-actual.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="12.692">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms


      182 |
      183 |     // First click - star the bookmark
    > 184 |     await starButton.click();
          |                      ^
      185 |     await page.waitForTimeout(100); // Allow state to update
      186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      187 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:184:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="30.293">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Test timeout of 30000ms exceeded.

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 5

      244 |
      245 |     // Verify no flow-breaking elements appeared
    > 246 |     expect(flowMetrics.disruptionCount).toBe(0);
          |                                         ^
      247 |     expect(flowMetrics.flowQuality).toBe('excellent');
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:246:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="12.247">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms
        - waiting for element to be visible, enabled and stable


      294 |
      295 |     // Click star and immediately check for uncertainty indicators
    > 296 |     await starButton.click();
          |                      ^
      297 |     await page.waitForTimeout(100); // Allow state to update
      298 |
      299 |     // User should not need to wonder "did that work?"
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:296:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="2.316">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: undefined is not an object (evaluating 'selectors.bookmarkStar')

      354 |
      355 |     // Test animation quality and timing
    > 356 |     const animationMetrics = await page.evaluate((selectors) => {
          |                                         ^
      357 |       return new Promise((resolve) => {
      358 |         const star = document.querySelector(selectors.bookmarkStar.split(', ')[0]); // Use first selector
      359 |         if (!star) {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="11.998">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [vibe-mobile] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        - locator resolved to <button data-testid="bookmark-star" aria-label="Remove from favorites" class="favorite-btn-inline active starred">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is visible, enabled and stable
        - scrolling into view if needed
        - done scrolling
        - <header class="header">…</header> intercepts pointer events
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      3 × retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <header class="header">…</header> intercepts pointer events
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 500ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      - retrying click action
        - waiting 500ms
        - waiting for element to be visible, enabled and stable


      448 |
      449 |     // Star a bookmark
    > 450 |     await starButton.click();
          |                      ^
      451 |     await page.waitForTimeout(100); // Allow state to update
      452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
      453 |
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:450:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-mobile\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="vibe\core\star-interaction-confidence.spec.js" timestamp="2025-07-17T06:38:41.834Z" hostname="vibe-accessibility" tests="7" failures="7" skipped="0" time="27.342" errors="0">
<testcase name="Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback" classname="vibe\core\star-interaction-confidence.spec.js" time="2.541">
<failure message="star-interaction-confidence.spec.js:32:7 Star interaction provides immediate confident feedback" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:32:7 › Star Interaction Confidence - Vibe Testing › Star interaction provides immediate confident feedback 

    TypeError: Cannot read properties of undefined (reading 'performanceLog')

       at vibe\utils\performance-monitor.js:364

      362 |     session.totalDuration = session.endTime - session.startTime;
      363 |     
    > 364 |     this.metrics.performanceLog.push(session);
          |                  ^
      365 |     
      366 |     return session;
      367 |   }
        at PerformanceMonitor.endMonitoring (C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\utils\performance-monitor.js:364:18)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:107:30

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Number of bookmarks found: [33m21[39m
Initial aria-label: Remove from favorites
Initial class: favorite-btn-inline active starred
Updated aria-label: Add to favorites
Updated class: favorite-btn-inline 
✅ Favorite state changed successfully - functionality is working

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-72c4b-mmediate-confident-feedback-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="3.339">
<failure message="star-interaction-confidence.spec.js:110:7 Star visual states feel distinct and satisfying" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:110:7 › Star Interaction Confidence - Vibe Testing › Star visual states feel distinct and satisfying 

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-accessibility-win32.png, writing actual.

      119 |
      120 |     // Capture unstarred state - should feel neutral but inviting
    > 121 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-state.png');
          |     ^
      122 |
      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:121:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-vibe-accessibility-win32.png, writing actual.

      123 |     // Test hover state - should feel interactive and inviting
      124 |     await starButton.hover();
    > 125 |     await expect(bookmarkItem).toHaveScreenshot('star-unstarred-hover.png');
          |     ^
      126 |
      127 |     // Star the bookmark - should feel satisfying
      128 |     await starButton.click();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:125:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-vibe-accessibility-win32.png, writing actual.

      129 |
      130 |     // Capture starred state - should feel special and accomplished
    > 131 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-state.png');
          |     ^
      132 |
      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:131:5

    Error: A snapshot doesn't exist at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-vibe-accessibility-win32.png, writing actual.

      133 |     // Test starred hover state - should maintain specialness
      134 |     await starButton.hover();
    > 135 |     await expect(bookmarkItem).toHaveScreenshot('star-starred-hover.png');
          |     ^
      136 |
      137 |     // Verify visual hierarchy makes starred items feel special
      138 |     const visualMetrics = await page.evaluate(() => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:135:5

    attachment #1: star-unstarred-state-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-accessibility-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: star-unstarred-state-actual.png (image/png) ─────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-unstarred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: star-unstarred-hover-expected.png (image/png) ───────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-vibe-accessibility-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: star-unstarred-hover-actual.png (image/png) ─────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-unstarred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: star-starred-state-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-vibe-accessibility-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: star-starred-state-actual.png (image/png) ───────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-starred-state-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: star-starred-hover-expected.png (image/png) ─────────────────────────────────────
    tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-vibe-accessibility-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: star-starred-hover-actual.png (image/png) ───────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-starred-hover-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-state-vibe-accessibility-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-unstarred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-unstarred-hover-vibe-accessibility-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-unstarred-hover-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-state-vibe-accessibility-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-starred-state-actual.png]]

[[ATTACHMENT|..\tests\vibe\core\star-interaction-confidence.spec.js-snapshots\star-starred-hover-vibe-accessibility-win32.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\star-starred-hover-actual.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-be15b-eel-distinct-and-satisfying-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken" classname="vibe\core\star-interaction-confidence.spec.js" time="7.185">
<failure message="star-interaction-confidence.spec.js:174:7 Double-clicking star feels intentional, not broken" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:174:7 › Star Interaction Confidence - Vibe Testing › Double-clicking star feels intentional, not broken 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      184 |     await starButton.click();
      185 |     await page.waitForTimeout(100); // Allow state to update
    > 186 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      187 |
      188 |     // Second click immediately - should unstar smoothly
      189 |     const doubleClickMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:186:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-f27cd-eels-intentional-not-broken-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="2.843">
<failure message="star-interaction-confidence.spec.js:221:7 Rapid starring maintains flow state without anxiety" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:221:7 › Star Interaction Confidence - Vibe Testing › Rapid starring maintains flow state without anxiety 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: "excellent"
    Received: "acceptable"

      245 |     // Verify no flow-breaking elements appeared
      246 |     expect(flowMetrics.disruptionCount).toBe(0);
    > 247 |     expect(flowMetrics.flowQuality).toBe('excellent');
          |                                     ^
      248 |
      249 |     // Verify all stars were applied correctly (no missed clicks)
      250 |     const starredCount = await page.locator(`${SELECTORS.bookmarkStar}.active, ${SELECTORS.bookmarkStar}.starred, ${SELECTORS.bookmarkStar}.favorited`).count();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:247:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-9b24c--flow-state-without-anxiety-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause" classname="vibe\core\star-interaction-confidence.spec.js" time="2.332">
<failure message="star-interaction-confidence.spec.js:286:7 Star feedback prevents uncertainty pause" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:286:7 › Star Interaction Confidence - Vibe Testing › Star feedback prevents uncertainty pause 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      321 |     }, SELECTORS);
      322 |
    > 323 |     expect(confidenceIndicators.hasVisualChange || confidenceIndicators.hasAriaUpdate).toBe(true);
          |                                                                                        ^
      324 |     expect(confidenceIndicators.noLoadingState).toBe(true);
      325 |
      326 |     // Test that user can immediately continue without waiting (if multiple bookmarks exist)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:323:88

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-c91bb--prevents-uncertainty-pause-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying" classname="vibe\core\star-interaction-confidence.spec.js" time="1.974">
<failure message="star-interaction-confidence.spec.js:346:7 Star animation feels polished and satisfying" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:346:7 › Star Interaction Confidence - Vibe Testing › Star animation feels polished and satisfying 

    Error: page.evaluate: TypeError: Cannot read properties of undefined (reading 'bookmarkStar')
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at new Promise (<anonymous>)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at eval (eval at evaluate (:291:30), <anonymous>:3:55)
        at eval (eval at evaluate (:291:30), <anonymous>:2:14)
        at UtilityScript.evaluate (<anonymous>:293:16)
        at UtilityScript.<anonymous> (<anonymous>:1:44)
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:356:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-82039-els-polished-and-satisfying-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety" classname="vibe\core\star-interaction-confidence.spec.js" time="7.128">
<failure message="star-interaction-confidence.spec.js:440:7 Star state persists across navigation without anxiety" type="FAILURE">
<![CDATA[  [vibe-accessibility] › vibe\core\star-interaction-confidence.spec.js:440:7 › Star Interaction Confidence - Vibe Testing › Star state persists across navigation without anxiety 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
    Expected pattern: /active|starred|favorited/
    Received string:  "favorite-btn-inline "
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('[data-testid="bookmark-star"], .star-button, .favorite-btn').first()
        9 × locator resolved to <button data-testid="bookmark-star" class="favorite-btn-inline " aria-label="Add to favorites">…</button>
          - unexpected value "favorite-btn-inline "


      450 |     await starButton.click();
      451 |     await page.waitForTimeout(100); // Allow state to update
    > 452 |     await expect(starButton).toHaveClass(/active|starred|favorited/);
          |                              ^
      453 |
      454 |     // Navigate away (simulate interruption by refreshing the page)
      455 |     await page.reload();
        at C:\Nexicon\Bookmark-Manager-Pro\tests\vibe\core\star-interaction-confidence.spec.js:452:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\test-failed-1.png]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\video.webm]]

[[ATTACHMENT|core-star-interaction-conf-2368b--navigation-without-anxiety-vibe-accessibility\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>