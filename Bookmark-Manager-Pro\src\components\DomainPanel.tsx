import { CheckCircle, Eye, Globe, Network, Settings, X, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

interface DomainPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const DomainPanel: React.FC<DomainPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, autoOrganizeBookmarks, previewAutoOrganize } = useBookmarks()
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [platformRecognition, setPlatformRecognition] = useState(true)
  const [subdomainGrouping, setSubdomainGrouping] = useState(false)
  const [minBookmarksPerDomain, setMinBookmarksPerDomain] = useState(3)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)
  const [activeButton, setActiveButton] = useState<string | null>(null)

  // Remove theme conditionals - always use modern design

  if (!isOpen) return null

  const generatePreview = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []
      results.push('🌐 Analyzing domains and website patterns...')
      setOrganizationResults([...results])
      setShowResults(true)

      const preview = await previewAutoOrganize({
        strategy: 'domain',
        preserveExistingFolders,
        useAI: platformRecognition
      })

      results.push(`📊 Analysis complete: ${bookmarks.length} bookmarks analyzed`)
      results.push(`📁 Proposed domain collections: ${preview.foldersToCreate.length}`)

      preview.foldersToCreate.forEach((folder, index) => {
        const bookmarksInFolder = preview.bookmarksToMove.filter(b => b.newFolder === folder).length
        results.push(`   ${index + 1}. "${folder}" (${bookmarksInFolder} bookmarks)`)
      })

      results.push(`🎯 Minimum bookmarks per domain: ${minBookmarksPerDomain}`)
      results.push(`📋 Preview ready - click "Start Domain Organization" to apply changes`)

      setOrganizationResults(results)

    } catch (error) {
      console.error('Domain preview failed:', error)
      setOrganizationResults(['❌ Domain preview failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  const handleOrganize = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []

      results.push('🌐 Starting domain-based organization...')
      setOrganizationResults([...results])
      setShowResults(true)
      await new Promise(resolve => setTimeout(resolve, 1000))

      results.push('📊 Analyzing website domains and patterns...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 1200))

      results.push('🏗️ Creating domain-based collections...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 800))

      const organizeResult = await autoOrganizeBookmarks({
        strategy: 'domain',
        preserveExistingFolders,
        useAI: platformRecognition
      })

      if (organizeResult.success) {
        results.push(`✅ Successfully organized ${organizeResult.bookmarksMoved} bookmarks`)
        results.push(`📁 Created ${organizeResult.foldersCreated.length} domain collections`)
        results.push(`🎉 Domain organization complete!`)

        if (preserveExistingFolders) {
          results.push('🔒 Existing folder structure preserved')
        }
      } else {
        results.push('❌ Organization failed. Please try again.')
      }

      setOrganizationResults(results)

      // Auto-close panel after successful completion
      if (organizeResult.success) {
        setTimeout(() => {
          console.log('🌐 DOMAIN PANEL: Auto-closing after successful completion');
          onClose();
        }, 3000); // Close after 3 seconds to let user see the results
      }

    } catch (error) {
      console.error('Domain organization failed:', error)
      setOrganizationResults(['❌ Domain organization failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  return (
    <div className="import-panel organization-panel">
      <div className="import-header">
        <h2 className="import-title">🌐 Expert Domain Organization</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close domain organization panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{
        maxHeight: 'calc(85vh - 120px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollBehavior: 'smooth'
      }}>
        {/* Strategy Description */}
        <div className="import-section">
          <h3 className="section-title">
            <Globe size={16} />
            Expert Domain Strategy
          </h3>
          <p className="section-description">
            Advanced domain intelligence with platform recognition, subdomain analysis, and hierarchical organization.
            Creates sophisticated domain-based structures that understand website relationships.
          </p>

          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <Network size={16} className="text-white" />
              <span>Platform ecosystem mapping</span>
              <small>Understands relationships between related platforms</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Subdomain intelligence</span>
              <small>Groups subdomains under parent domains</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Domain hierarchy analysis</span>
              <small>Creates nested structures for complex relationships</small>
            </div>
          </div>
        </div>

        {/* Domain Configuration */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            Domain Configuration
          </h3>

          <div className="format-options">
            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Preserve existing folder structure</span>
                <small>Keep bookmarks in current folders, organize uncategorized ones</small>
              </div>
            </label>

            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={platformRecognition}
                onChange={(e) => setPlatformRecognition(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Advanced platform recognition</span>
                <small>Identify and categorize 200+ platforms and services</small>
              </div>
            </label>

            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={subdomainGrouping}
                onChange={(e) => setSubdomainGrouping(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Subdomain grouping</span>
                <small>Group subdomains under parent domains</small>
              </div>
            </label>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <div style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <span>Min bookmarks per domain</span>
                  <span style={{ fontSize: '12px', color: 'var(--accent-color)' }}>{minBookmarksPerDomain}</span>
                </div>
                <input
                  type="range"
                  min="1"
                  max="10"
                  step="1"
                  value={minBookmarksPerDomain}
                  onChange={(e) => setMinBookmarksPerDomain(parseInt(e.target.value))}
                  style={{ width: '100%' }}
                />
                <small>Minimum bookmarks required to create a domain folder</small>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="import-section">
          <h3 className="section-title">Organization Actions</h3>
          <div className="format-options">
            <button
              onClick={generatePreview}
              className="format-option"
              style={{ cursor: isOrganizing ? 'not-allowed' : 'pointer', opacity: isOrganizing ? 0.7 : 1 }}
              disabled={isOrganizing || bookmarks.length === 0}
            >
              <Eye size={20} />
              <div>
                <span>Preview Organization</span>
                <small>See what changes will be made before organizing</small>
              </div>
            </button>

            <button
              onClick={() => {
                if (!isOrganizing && bookmarks.length > 0) {
                  setActiveButton('organize');
                  setTimeout(() => setActiveButton(null), 200);
                  handleOrganize();
                }
              }}
              className={`format-option ${activeButton === 'organize' ? 'active' : ''}`}
              style={{ cursor: isOrganizing ? 'not-allowed' : 'pointer', opacity: isOrganizing ? 0.7 : 1 }}
              disabled={bookmarks.length === 0 || isOrganizing}
            >
              {isOrganizing ? <div className="spinner" style={{ width: '20px', height: '20px', border: '2px solid #ccc', borderTop: '2px solid #007bff', borderRadius: '50%', animation: 'spin 1s linear infinite' }} /> : <Zap size={20} />}
              <div>
                <span>{isOrganizing ? 'Organizing...' : 'Start Domain Organization'}</span>
                <small>{isOrganizing ? 'Please wait while organizing by domains...' : `Organize ${bookmarks.length} bookmarks by domain`}</small>
              </div>
            </button>
          </div>

          {/* Organization Results - Inline with Actions */}
          {showResults && (
            <div style={{ marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600' }}>Results:</h4>
              <div style={{
                maxHeight: '150px',
                overflowY: 'auto',
                padding: '8px',
                background: 'var(--tertiary-bg)',
                borderRadius: '6px',
                border: '1px solid var(--border-color)'
              }}>
                {organizationResults.map((result, index) => (
                  <div key={index} style={{
                    fontSize: '11px',
                    marginBottom: '2px',
                    padding: '2px 6px',
                    background: index % 2 === 0 ? 'var(--secondary-bg)' : 'transparent',
                    borderRadius: '3px',
                    lineHeight: '1.2'
                  }}>
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Status Info */}
        <div className="import-section">
          <div className="upload-area" style={{ cursor: 'default', border: '2px solid var(--info-color)' }}>
            <Globe size={32} className="text-white" />
            <p className="upload-text">Domain Intelligence Ready</p>
            <p className="upload-hint">
              {bookmarks.length} bookmarks ready for domain-based organization
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
