import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import './EnhancedSearchInput.css';

// Keyboard shortcuts documentation
// Add a comment to ensure KEYBOARD_SHORTCUTS is recognized as used
// This constant defines all available keyboard shortcuts for the component
const KEYBOARD_SHORTCUTS = {
  'Arrow Down': 'Navigate to next suggestion',
  'Arrow Up': 'Navigate to previous suggestion',
  'Enter': 'Select highlighted suggestion or perform search',
  'Tab': 'Auto-complete with highlighted suggestion',
  'Escape': 'Close suggestions dropdown',
  'Ctrl/Cmd + K': 'Focus search input (global shortcut)',
  'Ctrl/Cmd + /': 'Show keyboard shortcuts help'
} as const;

// Base interface for all dropdown items
interface BaseDropdownItem {
  id: string;
  text: string;
  icon?: string;
}

// Specific types for better type discrimination
interface RecentSearchItem extends BaseDropdownItem {
  type: 'recent';
  icon: '🕒';
}

interface SuggestionItem extends BaseDropdownItem {
  type: 'suggestion';
}

interface BookmarkItem extends BaseDropdownItem {
  type: 'bookmark';
}

// Union type for dropdown item discrimination
type DropdownItem = RecentSearchItem | SuggestionItem | BookmarkItem;

// Legacy interface for backward compatibility
interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'suggestion' | 'bookmark';
  icon?: string;
}

interface EnhancedSearchInputProps {
  value?: string;
  placeholder?: string;
  onSearch?: (query: string) => void;
  onChange?: (query: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  suggestions?: SearchSuggestion[];
  showSuggestions?: boolean;
  autoComplete?: boolean;
  debounceMs?: number;
  maxSuggestions?: number;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  clearable?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'compact' | 'expanded';
  // New accessibility props
  showKeyboardShortcuts?: boolean;
  enableFocusTrap?: boolean;
  loadingSuggestions?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
}

const EnhancedSearchInput: React.FC<EnhancedSearchInputProps> = ({
  value = '',
  placeholder = 'Search...',
  onSearch,
  onChange,
  onFocus,
  onBlur,
  suggestions = [],
  showSuggestions = true,
  autoComplete = true,
  debounceMs = 300,
  maxSuggestions = 8,
  className = '',
  disabled = false,
  loading = false,
  clearable = true,
  size = 'medium',
  variant = 'default',
  // New accessibility props with defaults
  showKeyboardShortcuts = false,
  enableFocusTrap = true,
  loadingSuggestions = false,
  ariaLabel = 'Search bookmarks',
  ariaDescribedBy
}) => {
  const [query, setQuery] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const shortcutsHelpRef = useRef<HTMLDivElement>(null);

  // Debounced search handler
  const debouncedSearch = useCallback(
    (searchQuery: string) => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
      
      debounceRef.current = setTimeout(() => {
        onChange?.(searchQuery);
      }, debounceMs);
    },
    [onChange, debounceMs]
  );

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setSelectedIndex(-1);
    
    if (showSuggestions && newQuery.trim()) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
    
    debouncedSearch(newQuery);
  };

  // Handle input focus
  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
    
    if (showSuggestions && (query.trim() || recentSearches.length > 0)) {
      setShowDropdown(true);
    }
  };

  // Handle input blur
  const handleBlur = (e: React.FocusEvent) => {
    // Delay hiding dropdown to allow for suggestion clicks
    setTimeout(() => {
      if (!dropdownRef.current?.contains(e.relatedTarget as Node)) {
        setIsFocused(false);
        setShowDropdown(false);
        setSelectedIndex(-1);
        onBlur?.();
      }
    }, 150);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(query);
  };

  // Perform search with useCallback for performance
  const performSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    onSearch?.(searchQuery);
    
    // Add to recent searches
    setRecentSearches(prev => {
      const filtered = prev.filter(item => item !== searchQuery);
      return [searchQuery, ...filtered].slice(0, 5);
    });
    
    setShowDropdown(false);
    inputRef.current?.blur();
  }, [onSearch]);

  // Handle clear with useCallback for performance
  const handleClear = useCallback(() => {
    setQuery('');
    onChange?.('');
    setShowDropdown(false);
    inputRef.current?.focus();
  }, [onChange]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Global shortcuts
    if ((e.ctrlKey || e.metaKey) && e.key === '/') {
      e.preventDefault();
      setShowShortcutsHelp(prev => !prev);
      return;
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      inputRef.current?.focus();
      return;
    }

    if (!showDropdown) {
      if (e.key === 'ArrowDown' && showSuggestions) {
        setShowDropdown(true);
        setSelectedIndex(0);
        e.preventDefault();
      }
      return;
    }

    const totalItems = Math.min(
      suggestions.length + recentSearches.length,
      maxSuggestions
    );

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < totalItems - 1 ? prev + 1 : prev
        );
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
        
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          const selectedItem = allItems[selectedIndex];
          if (selectedItem) {
            setQuery(selectedItem);
            performSearch(selectedItem);
          }
        } else {
          performSearch(query);
        }
        break;
        
      case 'Escape':
        setShowDropdown(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
        
      case 'Tab':
        if (enableFocusTrap && showDropdown) {
          e.preventDefault();
          // Focus trap: cycle through dropdown items
          if (selectedIndex >= 0) {
            const selectedItem = allItems[selectedIndex];
            if (selectedItem) {
              setQuery(selectedItem);
            }
          }
          // Move to next item or wrap to first
          setSelectedIndex(prev => 
            prev < totalItems - 1 ? prev + 1 : 0
          );
        } else if (selectedIndex >= 0) {
          e.preventDefault();
          const selectedItem = allItems[selectedIndex];
          if (selectedItem) {
            setQuery(selectedItem);
          }
          setShowDropdown(false);
        } else {
          setShowDropdown(false);
        }
        break;
    }
  };

  // Handle suggestion click with useCallback for performance
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setQuery(suggestion);
    performSearch(suggestion);
  }, [performSearch]);

  // Handle recent search clear with useCallback for performance
  const handleClearRecentSearches = useCallback(() => {
    setRecentSearches([]);
  }, []);

  // Update query when value prop changes
  useEffect(() => {
    setQuery(value);
  }, [value]);

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('bookmark-recent-searches');
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('bookmark-recent-searches', JSON.stringify(recentSearches));
    } catch (error) {
      console.warn('Failed to save recent searches:', error);
    }
  }, [recentSearches]);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // Memoized combined items for keyboard navigation
  const allItems: string[] = useMemo(() => [
    ...recentSearches, 
    ...suggestions.map((s: SearchSuggestion) => s.text)
  ], [recentSearches, suggestions]);

  // Memoized dropdown items preparation for performance optimization
  const dropdownItems: DropdownItem[] = useMemo(() => {
    const items: DropdownItem[] = [];
    
    // Add recent searches with proper typing
    if (recentSearches.length > 0 && (!query.trim() || isFocused)) {
      recentSearches.slice(0, 3).forEach((search: string, index: number) => {
        const recentItem: RecentSearchItem = {
          id: `recent-${index}`,
          text: search,
          type: 'recent',
          icon: '🕒'
        };
        items.push(recentItem);
      });
    }
    
    // Add suggestions with proper typing
    if (query.trim()) {
      const filteredSuggestions: SearchSuggestion[] = suggestions
        .filter((s: SearchSuggestion) => s.text.toLowerCase().includes(query.toLowerCase()))
        .slice(0, maxSuggestions - items.length);
      
      filteredSuggestions.forEach((suggestion: SearchSuggestion) => {
        // Convert SearchSuggestion to appropriate DropdownItem type
        const dropdownItem: DropdownItem = {
          id: suggestion.id,
          text: suggestion.text,
          type: suggestion.type,
          ...(suggestion.icon && { icon: suggestion.icon })
        } as DropdownItem;
        
        items.push(dropdownItem);
      });
    }
    
    return items;
  }, [recentSearches, query, isFocused, suggestions, maxSuggestions]);

  const sizeClass = `enhanced-search-input--${size}`;
  const variantClass = `enhanced-search-input--${variant}`;
  const stateClass = isFocused ? 'enhanced-search-input--focused' : '';
  const disabledClass = disabled ? 'enhanced-search-input--disabled' : '';

  return (
    <div className={`enhanced-search-input ${sizeClass} ${variantClass} ${stateClass} ${disabledClass} ${className}`}>
      <form className="enhanced-search-input__form" onSubmit={handleSubmit}>
        <div className="enhanced-search-input__container">
          {/* Search Icon */}
          <div className="enhanced-search-input__icon">
            {loading ? (
              <div className="enhanced-search-input__spinner">
                <svg viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeDasharray="32" strokeDashoffset="32">
                    <animate attributeName="stroke-dashoffset" values="32;0;32" dur="1.5s" repeatCount="indefinite" />
                  </circle>
                </svg>
              </div>
            ) : (
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.35-4.35" />
              </svg>
            )}
          </div>

          {/* Input Field */}
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            autoComplete={autoComplete ? 'on' : 'off'}
            className="enhanced-search-input__field"
            aria-label={ariaLabel}
            aria-describedby={ariaDescribedBy}
            aria-expanded={showDropdown}
            aria-haspopup="listbox"
            role="searchbox"
            tabIndex={0}
          />

          {/* Clear Button */}
          {clearable && query && !disabled && (
            <button
              type="button"
              className="enhanced-search-input__clear touch-target"
              onClick={handleClear}
              aria-label="Clear search"
              role="button"
              tabIndex={0}
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            className="enhanced-search-input__submit touch-target"
            disabled={disabled || !query.trim()}
            aria-label="Submit search"
            role="button"
            tabIndex={0}
          >
            <span className="enhanced-search-input__submit-text">Search</span>
            <svg className="enhanced-search-input__submit-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.35-4.35" />
            </svg>
          </button>
        </div>
      </form>

      {/* Suggestions Dropdown */}
      {showDropdown && dropdownItems.length > 0 && (
        <div 
          ref={dropdownRef}
          className="enhanced-search-input__dropdown"
          role="listbox"
          aria-label="Search suggestions"
        >
          {/* Recent Searches Header */}
          {recentSearches.length > 0 && (!query.trim() || isFocused) && (
            <div className="enhanced-search-input__section">
              <div className="enhanced-search-input__section-header">
                <span>Recent Searches</span>
                <button
                  type="button"
                  className="enhanced-search-input__clear-recent"
                  onClick={handleClearRecentSearches}
                  aria-label="Clear recent searches"
                >
                  Clear
                </button>
              </div>
            </div>
          )}

          {/* Dropdown Items */}
          {loadingSuggestions ? (
            <div className="enhanced-search-input__loading">
              <div className="enhanced-search-input__loading-spinner">
                <svg viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeDasharray="32" strokeDashoffset="32">
                    <animate attributeName="stroke-dashoffset" values="32;0;32" dur="1.5s" repeatCount="indefinite" />
                  </circle>
                </svg>
              </div>
              <span className="enhanced-search-input__loading-text">Loading suggestions...</span>
            </div>
          ) : (
            dropdownItems.map((item, index) => (
              <button
                key={item.id}
                type="button"
                className={`enhanced-search-input__item ${
                  index === selectedIndex ? 'enhanced-search-input__item--selected' : ''
                } enhanced-search-input__item--${item.type}`}
                onClick={() => handleSuggestionClick(item.text)}
                role="option"
                aria-selected={index === selectedIndex}
              >
                {item.icon && (
                  <span className="enhanced-search-input__item-icon">
                    {item.icon}
                  </span>
                )}
                <span className="enhanced-search-input__item-text">
                  {item.text}
                </span>
                {item.type === 'recent' && (
                  <span className="enhanced-search-input__item-label">
                    Recent
                  </span>
                )}
              </button>
            ))
          )}

          {/* No Results */}
          {!loadingSuggestions && query.trim() && dropdownItems.length === 0 && (
            <div className="enhanced-search-input__no-results">
              <span>No suggestions found</span>
            </div>
          )}
        </div>
      )}

      {/* Keyboard Shortcuts Help */}
      {(showKeyboardShortcuts || showShortcutsHelp) && showShortcutsHelp && (
        <div 
          ref={shortcutsHelpRef}
          className="enhanced-search-input__shortcuts-help"
          role="dialog"
          aria-labelledby="shortcuts-title"
          aria-modal="true"
        >
          <div className="enhanced-search-input__shortcuts-content">
            <h3 id="shortcuts-title">Keyboard Shortcuts</h3>
            <ul className="enhanced-search-input__shortcuts-list">
              {Object.entries(KEYBOARD_SHORTCUTS).map(([key, description]) => (
                <li key={key} className="enhanced-search-input__shortcut-item">
                  <kbd className="enhanced-search-input__shortcut-key">{key}</kbd>
                  <span className="enhanced-search-input__shortcut-desc">{description}</span>
                </li>
              ))}
            </ul>
            <button
              type="button"
              className="enhanced-search-input__shortcuts-close"
              onClick={() => setShowShortcutsHelp(false)}
              aria-label="Close keyboard shortcuts help"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Screen reader instructions */}
      <div id="search-instructions" className="sr-only">
        Use arrow keys to navigate suggestions, Enter to select, Escape to close.
        {(showKeyboardShortcuts || true) && ' Press Ctrl+/ or Cmd+/ for keyboard shortcuts help.'}
      </div>
    </div>
  );
};

export default EnhancedSearchInput;
export type { 
  EnhancedSearchInputProps, 
  SearchSuggestion, 
  DropdownItem, 
  RecentSearchItem, 
  SuggestionItem, 
  BookmarkItem,
  BaseDropdownItem 
};