import {
  <PERSON>, Check,
  Copy, Edit3, ExternalLink, Eye,
  Loader2, MoreVertical,
  RotateCcw, Star, Trash2
} from 'lucide-react'
import React, { useState } from 'react'
import { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import { SummaryResult } from '../services/summaryService'
import { getConsistentCollectionColor } from '../utils/collectionColors'
import SmartFavicon from './SmartFavicon'
// Test color assignments
import '../utils/colorTest'
import { PanelType } from './TabbedRightPanel'

// Global flip state management to persist across re-renders
const globalFlipStates = new Map<string, boolean>()
const flipStateListeners = new Set<() => void>()

// Notify all components when flip state changes
const notifyFlipStateChange = () => {
  flipStateListeners.forEach(listener => listener())
}

// Helper to update flip state and notify listeners
const updateGlobalFlipState = (bookmarkId: string, isFlipped: boolean) => {
  globalFlipStates.set(bookmarkId, isFlipped)
  notifyFlipStateChange()
}

interface BookmarkCardFlipProps {
  bookmark: Bookmark
  onToggleFavorite?: (id: string) => void
  onEdit?: (bookmark: Bookmark) => void
  onDelete?: (id: string) => void
  onOpenPanel?: (panelType: PanelType) => void
}

export const BookmarkCardFlip = ({
  bookmark,
  onToggleFavorite,
  onEdit: _onEdit,
  onDelete: _onDelete,
  onOpenPanel
}: BookmarkCardFlipProps) => {
  // Use global flip state to persist across re-renders and summary updates
  const [localFlipState, setLocalFlipState] = useState(() => globalFlipStates.get(bookmark.id) || false)
  const isFlipped = localFlipState

  const [summaryData, setSummaryData] = useState<SummaryResult | null>(null)
  const [isLoadingSummary, setIsLoadingSummary] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const [_imageError, _setImageError] = useState(false)

  // Subscribe to global flip state changes
  React.useEffect(() => {
    const listener = () => {
      const currentGlobalState = globalFlipStates.get(bookmark.id) || false
      if (currentGlobalState !== localFlipState) {
        setLocalFlipState(currentGlobalState)
      }
    }

    flipStateListeners.add(listener)
    return () => {
      flipStateListeners.delete(listener)
    }
  }, [bookmark.id]) // Remove localFlipState dependency to prevent infinite loops

  // Track renders for performance monitoring
  React.useEffect(() => {
    if (typeof window !== 'undefined' && (window as any).performanceMonitor) {
      (window as any).performanceMonitor.trackRender('BookmarkCardFlip');
    }
  })

  // Stable summary data management - use useMemo to prevent flickering
  const currentSummaryData = React.useMemo(() => {
    // Return null if no summary exists
    if (!bookmark.summary || bookmark.summary.trim() === '') {
      return null;
    }

    // Simple content type detection
    const detectContentType = (url: string): SummaryResult['contentType'] => {
      try {
        const domain = new URL(url).hostname.toLowerCase()
        if (domain.includes('youtube.com') || domain.includes('youtu.be')) return 'youtube'
        if (domain.includes('github.com')) return 'github'
        if (domain.includes('docs.') || domain.includes('documentation')) return 'documentation'
        if (domain.includes('medium.com') || domain.includes('dev.to')) return 'article'
        return 'webpage'
      } catch {
        return 'webpage'
      }
    }

    // Create summary data from bookmark summary
    return {
      quickSummary: bookmark.summary.substring(0, 100) + (bookmark.summary.length > 100 ? '...' : ''),
      detailedSummary: bookmark.summary.trim(),
      contentType: detectContentType(bookmark.url),
      extractedData: {
        title: bookmark.title
      }
    } as SummaryResult;
  }, [bookmark.summary, bookmark.url, bookmark.title])

  // Update state only when computed data changes
  React.useEffect(() => {
    setSummaryData(currentSummaryData);
    setIsLoadingSummary(false);
  }, [currentSummaryData])

  const {
    isSelectMode,
    toggleBookmarkSelection,
    deleteBookmark,
    clearRecentStatus,
    incrementVisitCount
  } = useBookmarks()



  // Smart title processing - creates clean, readable titles like mock data
  const smartTruncateTitle = (title: string, maxLength: number = 35) => {
    if (!title) return 'Untitled'

    let cleaned = title

    // Step 1: Remove leading numbers/IDs (like "1456-", "1479-")
    cleaned = cleaned.replace(/^\d+[-\s]*/, '')

    // Step 2: Remove common redundant patterns
    cleaned = cleaned
      .replace(/\s*\([^)]*\)\s*/g, ' ') // Remove parenthetical content
      .replace(/\s*\[[^\]]*\]\s*/g, ' ') // Remove bracketed content
      .replace(/\s*–\s*[^–]*$/g, '') // Remove everything after final dash
      .replace(/\s*-\s*[^-]*$/g, '') // Remove everything after final hyphen
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()

    // Step 3: If still empty or too short, try to extract meaningful content
    if (!cleaned || cleaned.length < 3) {
      // Try to find the main content between common separators
      const parts = title.split(/[-–:|]/)
      for (const part of parts) {
        const candidate = part.replace(/^\d+\s*/, '').trim()
        if (candidate.length > 3 && !candidate.match(/^(v\d|from|gb|mb|repack)/i)) {
          cleaned = candidate
          break
        }
      }
    }

    // Step 4: If still problematic, use domain-based fallback
    if (!cleaned || cleaned.length < 3) {
      cleaned = 'Website Content'
    }

    // Step 5: Smart truncation at word boundaries
    if (cleaned.length > maxLength) {
      const words = cleaned.split(' ')
      let result = ''
      for (const word of words) {
        if ((result + ' ' + word).length > maxLength - 3) break
        result += (result ? ' ' : '') + word
      }
      cleaned = result + '...'
    }

    // Step 6: Capitalize first letter for consistency
    return cleaned.charAt(0).toUpperCase() + cleaned.slice(1)
  }

  // Smart domain extraction logic - shows domain + truncated path for long URLs
  const extractSmartDomain = (url: string) => {
    try {
      const urlObj = new URL(url)
      const hostname = urlObj.hostname.replace('www.', '')

      // For very long URLs like dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/
      // Show domain + truncated path to prevent layout issues
      if (urlObj.pathname && urlObj.pathname.length > 20) {
        const pathParts = urlObj.pathname.split('/').filter(Boolean)
        if (pathParts.length > 0) {
          const firstPath = pathParts[0]
          if (firstPath.length > 20) {
            return `${hostname}/${firstPath.substring(0, 20)}...`
          }
          return `${hostname}/${firstPath}`
        }
      }

      return hostname
    } catch (_error) {
      // Fallback for invalid URLs - truncate if too long
      return url.length > 30 ? url.substring(0, 30) + '...' : url
    }
  }





  const handleFlip = async () => {
    const newFlippedState = !isFlipped

    // Debug logging for flip state changes
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Flipping bookmark "${bookmark.title}" from ${isFlipped} to ${newFlippedState}`)
    }

    // Update both global and local state immediately
    globalFlipStates.set(bookmark.id, newFlippedState)
    setLocalFlipState(newFlippedState)

    // Notify other components
    notifyFlipStateChange()

    // Summary data is now managed by the useEffect above
    // No need to duplicate logic here
  }

  const handleCardClick = (_e: React.MouseEvent) => {
    // Only handle selection mode clicks on the card itself
    if (isSelectMode) {
      console.log('🎯 Card clicked in select mode, toggling selection for:', bookmark.title)
      toggleBookmarkSelection(bookmark.id)
    }
    // For normal mode, we don't open links on general card clicks
    // Links will be opened via specific clickable elements (title, visit button)
  }

  const handleLinkClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation() // Prevent card click handler
    console.log('🌐 Opening bookmark in new tab:', bookmark.url)

    // Increment visit count
    if (incrementVisitCount) {
      incrementVisitCount(bookmark.id)
    }

    // Clear recently added status when user visits the bookmark
    if (bookmark.isRecentlyAdded && clearRecentStatus) {
      clearRecentStatus(bookmark.id)
    }

    // Ensure URL is valid
    if (bookmark.url && bookmark.url.trim()) {
      let url = bookmark.url.trim()

      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url
      }

      console.log('🚀 Final URL:', url)
      window.open(url, '_blank', 'noopener,noreferrer')
    } else {
      console.error('❌ Invalid bookmark URL:', bookmark.url)
    }
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  // Use modern theme context
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // Use consistent collection color system - ensures borders and indicators match exactly
  const collectionColors = getConsistentCollectionColor(bookmark.collection)
  const { borderColor, indicatorColor, lightColor } = collectionColors

  // Debug: Log collection colors (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log(`Collection: ${bookmark.collection}, Border Color: ${borderColor}, Theme: ${isModernTheme ? 'Modern' : 'Classic'}`)
  }

  // Debug logging for flip state
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 BookmarkCardFlip render - ID: ${bookmark.id}, Title: ${bookmark.title}, isFlipped: ${isFlipped}`);
  }

  return (
    <div
      className={`bookmark-card-flip ${isFlipped ? 'flipped' : ''}`}
      data-testid="bookmark-item"
      style={{
        pointerEvents: 'auto' // Ensure pointer events are enabled
      }}
    >
      <div
        className="bookmark-card-inner"
        style={{
          // Override problematic CSS that might interfere with click detection
          backfaceVisibility: 'visible', // Override hidden backface
          pointerEvents: 'auto',
          position: 'relative',
          width: '100%',
          height: '100%',
          transformStyle: 'preserve-3d',
          transition: 'transform 0.6s ease-in-out',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
        }}
      >
        {/* Front Side - Collection Color Borders for Both Themes */}
        <div
          className="bookmark-card-front-redesigned"
          onClick={(e) => {
            console.log('🎯 Card front clicked, isSelectMode:', isSelectMode)
            if (isSelectMode) {
              handleCardClick(e)
            } else {
              console.log('🎯 Card front clicked in normal mode - no action')
            }
          }}
          onDoubleClick={(e) => {
            console.log('🔄 Double-click detected on card front')
            handleFlip()
          }}
          style={{
            // Override problematic CSS
            position: 'absolute',
            width: '100%',
            height: '100%',
            backfaceVisibility: 'visible', // Override hidden backface
            transform: 'rotateY(0deg)', // Explicit transform
            cursor: isSelectMode ? 'default' : 'auto',
            pointerEvents: 'auto',
            zIndex: isFlipped ? 1 : 10, // Lower z-index when flipped
            // Collection color border for BOTH themes
            borderColor: borderColor,
            borderWidth: isModernTheme ? '3px' : '2px',
            borderStyle: 'solid',
            // Modern theme gets additional effects
            ...(isModernTheme ? {
              boxShadow: `0 0 0 1px ${borderColor}20, 0 4px 12px rgba(0, 0, 0, 0.1)`,
              background: `linear-gradient(135deg, ${lightColor}, rgba(255, 255, 255, 0.95))`
            } : {
              // Classic theme keeps original background from CSS
            })
          }}
        >
          {/* Collection Color Dot Indicator - Only in Modern Theme */}
          {isModernTheme && (
            <div
              className="collection-dot-indicator"
              style={{ backgroundColor: indicatorColor }}
              title={`Collection: ${bookmark.collection}`}
            />
          )}
          {isSelectMode && (
            <div
              className={`selection-indicator ${bookmark.selected ? 'selected' : ''}`}
              onClick={(e) => {
                e.stopPropagation()
                toggleBookmarkSelection(bookmark.id)
              }}
            >
              {bookmark.selected && <Check size={16} />}
            </div>
          )}

          {bookmark.isRecentlyAdded && (
            <div className="new-indicator-small">
              NEW
            </div>
          )}

          {/* Header with Icon + Title */}
          <div className="bookmark-header-redesigned">
            <div className="bookmark-icon-title">
              <div className="bookmark-favicon-redesigned">
                <SmartFavicon
                  url={bookmark.url}
                  title={bookmark.title}
                  favicon={bookmark.favicon}
                  priority="high"
                  lazy={false}
                  className="favicon-image"
                />
              </div>

              <div className="title-with-star">
                <h3
                  className="bookmark-title-redesigned"
                  title={bookmark.title}
                  onClick={(e) => {
                    console.log('🎯 Title clicked for:', bookmark.title)
                    handleLinkClick(e)
                  }}
                  style={{
                    cursor: 'pointer',
                    display: 'inline',
                    margin: 0,
                    lineHeight: '1.4'
                  }}
                >
                  {smartTruncateTitle(bookmark.title)}
                  <button
                    data-testid="bookmark-star"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      console.log('⭐ Favorite button clicked for:', bookmark.title)
                      if (onToggleFavorite) {
                        onToggleFavorite(bookmark.id)
                        console.log('⭐ onToggleFavorite called')
                      } else {
                        console.error('⭐ onToggleFavorite is not available')
                      }
                    }}
                    className={`favorite-btn-inline ${bookmark.isFavorite ? 'active starred' : ''}`}
                    aria-label={bookmark.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                    style={{
                      display: 'inline',
                      marginLeft: '4px',
                      verticalAlign: 'baseline'
                    }}
                  >
                    <Star size={16} fill={bookmark.isFavorite ? 'currentColor' : 'none'} />
                  </button>
                </h3>
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="bookmark-content-redesigned">
            <p className="bookmark-description-redesigned" title={bookmark.description}>
              {bookmark.description.length > 80
                ? bookmark.description.substring(0, 80).trim() + '...'
                : bookmark.description
              }
            </p>

            <div className="bookmark-url-redesigned" style={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '200px',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              <span style={{ flex: 1, minWidth: 0 }}>
                {extractSmartDomain(bookmark.url)}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  handleFlip()
                }}
                className="flip-button-inline"
                title="View detailed summary"
                style={{
                  flexShrink: 0,
                  marginLeft: 'auto'
                }}
              >
                <RotateCcw size={14} />
              </button>
            </div>
          </div>

          {/* Footer with Collections and Meta - RESPONSIVE DESIGN */}
          <div className="bookmark-footer-redesigned">
            <div className="bookmark-collections-redesigned">
              <span
                className="collection-redesigned"
                title={bookmark.collection} // Tooltip for long collection names
              >
                {/* Collection dot only in modern theme */}
                {isModernTheme && (
                  <span
                    className="collection-dot-small"
                    style={{ backgroundColor: indicatorColor }}
                  />
                )}
                {bookmark.collection}
              </span>
            </div>

            <div className="bookmark-meta-redesigned">
              <div className="meta-item-redesigned">
                <Eye size={12} />
                <span>{bookmark.visits}</span>
              </div>
              <div className="meta-item-with-menu">
                <div className="meta-item-redesigned">
                  <Calendar size={12} />
                  <span>{formatDate(bookmark.dateAdded)}</span>
                </div>
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    console.log('📋 Menu button clicked for:', bookmark.title)
                    setShowMenu(!showMenu)
                  }}
                  className="menu-btn-inline"
                  aria-label="More options"
                >
                  <MoreVertical size={12} />
                </button>
              </div>
            </div>
          </div>

          {/* Dropdown menu positioned relative to inline button */}
          {showMenu && (
            <div className="dropdown-menu-inline" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={() => {
                  console.log('🌐 Visit menu item clicked for:', bookmark.title)
                  if (isSelectMode) {
                    toggleBookmarkSelection(bookmark.id)
                  } else {
                    if (bookmark.isRecentlyAdded) {
                      clearRecentStatus(bookmark.id)
                    }
                    window.open(bookmark.url, '_blank', 'noopener,noreferrer')
                  }
                  setShowMenu(false)
                }}
                className="menu-item"
              >
                <ExternalLink size={14} />
                Visit
              </button>
              <button
                onClick={async () => {
                  try {
                    await navigator.clipboard.writeText(bookmark.url)
                    setShowMenu(false)
                  } catch (err) {
                    console.error('Failed to copy URL:', err)
                  }
                }}
                className="menu-item"
              >
                <Copy size={14} />
                Copy URL
              </button>
              <button className="menu-item">
                <Edit3 size={14} />
                Edit
              </button>
              <hr className="menu-separator" />
              <button
                onClick={() => {
                  console.log('🗑️ Delete menu item clicked for:', bookmark.title)
                  if (deleteBookmark) {
                    deleteBookmark(bookmark.id)
                  } else {
                    console.error('🗑️ deleteBookmark function not available')
                  }
                  setShowMenu(false)
                }}
                className="menu-item danger"
              >
                <Trash2 size={14} />
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Back Side */}
        <div
          className="bookmark-card-back"
          onDoubleClick={handleFlip}
          onClick={(e) => {
            // Only handle clicks that aren't on interactive elements
            const target = e.target as HTMLElement;
            const isInteractive = target.closest('a, button, .detailed-actions');

            console.log('🔄 Back panel clicked:', {
              target: target.tagName,
              className: target.className,
              isInteractive: !!isInteractive,
              closestLink: target.closest('a')?.href
            });

            // Don't interfere with interactive element clicks
            if (isInteractive) {
              console.log('🔄 Interactive element clicked, allowing event to proceed');
              return; // Let the event bubble normally
            }
          }}
          style={{
            // Override all problematic CSS
            position: 'absolute',
            width: '100%',
            height: '100%',
            backfaceVisibility: 'visible', // Override hidden backface
            transform: 'rotateY(180deg)', // Explicit transform
            cursor: 'default',
            pointerEvents: 'auto', // Ensure pointer events work
            zIndex: isFlipped ? 10 : 1, // Higher z-index when flipped
            overflow: 'visible', // Ensure content is visible
            display: 'flex',
            flexDirection: 'column',
            padding: '16px',
            boxSizing: 'border-box',
            borderRadius: 'var(--radius-lg)'
          }}
        >
          <div
            className="bookmark-card-header"
            style={{
              marginBottom: '8px', // Reduced from var(--padding-md) which is typically 16px
              padding: '16px 16px 0 16px', // Remove bottom padding
              borderBottom: 'none' // Remove border for cleaner look
            }}
          >
            <h3
              className="bookmark-back-title"
              style={{
                margin: '0', // Remove default h3 margins
                fontSize: '1.1rem',
                fontWeight: '600',
                color: 'var(--text-primary)',
                lineHeight: '1.3'
              }}
            >
              {bookmark.title}
            </h3>
          </div>

          <div
            className="bookmark-detailed-content"
            style={{
              padding: '0 16px 16px 16px', // Remove top padding to close the gap
              flex: '1',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '12px' // Add consistent spacing between sections
            }}
          >
            {isLoadingSummary ? (
              <div className="loading-state">
                <Loader2 size={24} className="animate-spin" />
                <p>Analyzing content...</p>
              </div>
            ) : currentSummaryData ? (
              <>
                <div
                  className="detailed-summary"
                  style={{
                    marginTop: '0' // Ensure no extra top margin
                  }}
                >
                  <h4
                    style={{
                      margin: '0 0 8px 0', // Reduced bottom margin
                      fontSize: '0.9rem',
                      fontWeight: '600',
                      color: 'var(--text-primary)'
                    }}
                  >
                    Detailed Summary
                  </h4>
                  <p
                    style={{
                      fontSize: '0.85rem',
                      lineHeight: '1.5',
                      color: 'var(--text-secondary)',
                      margin: '0'
                    }}
                  >
                    {currentSummaryData.detailedSummary}
                  </p>
                </div>

                {currentSummaryData.keyPoints && currentSummaryData.keyPoints.length > 0 && (
                  <div className="key-points">
                    <h4
                      style={{
                        margin: '0 0 8px 0',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        color: 'var(--text-primary)'
                      }}
                    >
                      Key Points
                    </h4>
                    <ul
                      style={{
                        margin: '0',
                        paddingLeft: '16px',
                        fontSize: '0.85rem',
                        lineHeight: '1.4',
                        color: 'var(--text-secondary)'
                      }}
                    >
                      {currentSummaryData.keyPoints.map((point, index) => (
                        <li key={index} style={{ marginBottom: '4px' }}>{point}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {currentSummaryData.extractedData && (
                  <div className="extracted-data">
                    <h4
                      style={{
                        margin: '0 0 8px 0',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        color: 'var(--text-primary)'
                      }}
                    >
                      Content Details
                    </h4>
                    <div
                      className="data-grid"
                      style={{
                        display: 'grid',
                        gap: '6px',
                        fontSize: '0.8rem'
                      }}
                    >
                      {currentSummaryData.extractedData.author && (
                        <div
                          className="data-item"
                          style={{
                            display: 'flex',
                            gap: '8px'
                          }}
                        >
                          <span
                            className="data-label"
                            style={{
                              fontWeight: '500',
                              color: 'var(--text-primary)',
                              minWidth: '80px'
                            }}
                          >
                            Author:
                          </span>
                          <span
                            className="data-value"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            {currentSummaryData.extractedData.author}
                          </span>
                        </div>
                      )}
                      {currentSummaryData.extractedData.publishDate && (
                        <div
                          className="data-item"
                          style={{
                            display: 'flex',
                            gap: '8px'
                          }}
                        >
                          <span
                            className="data-label"
                            style={{
                              fontWeight: '500',
                              color: 'var(--text-primary)',
                              minWidth: '80px'
                            }}
                          >
                            Published:
                          </span>
                          <span
                            className="data-value"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            {formatDate(currentSummaryData.extractedData.publishDate)}
                          </span>
                        </div>
                      )}
                      {currentSummaryData.extractedData.readingTime && (
                        <div
                          className="data-item"
                          style={{
                            display: 'flex',
                            gap: '8px'
                          }}
                        >
                          <span
                            className="data-label"
                            style={{
                              fontWeight: '500',
                              color: 'var(--text-primary)',
                              minWidth: '80px'
                            }}
                          >
                            Reading Time:
                          </span>
                          <span
                            className="data-value"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            {currentSummaryData.extractedData.readingTime}
                          </span>
                        </div>
                      )}
                      {currentSummaryData.extractedData.language && (
                        <div
                          className="data-item"
                          style={{
                            display: 'flex',
                            gap: '8px'
                          }}
                        >
                          <span
                            className="data-label"
                            style={{
                              fontWeight: '500',
                              color: 'var(--text-primary)',
                              minWidth: '80px'
                            }}
                          >
                            Language:
                          </span>
                          <span
                            className="data-value"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            {currentSummaryData.extractedData.language}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {currentSummaryData.youtubeData && (
                  <div className="youtube-data">
                    <h4
                      style={{
                        margin: '0 0 8px 0',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        color: 'var(--text-primary)'
                      }}
                    >
                      Video Details
                    </h4>
                    <div
                      className="youtube-info"
                      style={{
                        display: 'flex',
                        gap: '12px',
                        alignItems: 'flex-start'
                      }}
                    >
                      <img
                        src={currentSummaryData.youtubeData.thumbnailUrl}
                        alt="Video thumbnail"
                        className="youtube-thumbnail"
                        style={{
                          width: '80px',
                          height: '60px',
                          objectFit: 'cover',
                          borderRadius: '4px',
                          flexShrink: 0
                        }}
                      />
                      <div
                        className="youtube-meta"
                        style={{
                          fontSize: '0.8rem',
                          lineHeight: '1.4'
                        }}
                      >
                        <p
                          style={{
                            margin: '0 0 4px 0',
                            color: 'var(--text-secondary)'
                          }}
                        >
                          <strong style={{ color: 'var(--text-primary)' }}>Channel:</strong> {currentSummaryData.youtubeData.channelTitle}
                        </p>
                        <p
                          style={{
                            margin: '0 0 4px 0',
                            color: 'var(--text-secondary)'
                          }}
                        >
                          <strong style={{ color: 'var(--text-primary)' }}>Duration:</strong> {currentSummaryData.youtubeData.duration}
                        </p>
                        <p
                          style={{
                            margin: '0',
                            color: 'var(--text-secondary)'
                          }}
                        >
                          <strong style={{ color: 'var(--text-primary)' }}>Views:</strong> {currentSummaryData.youtubeData.viewCount}
                        </p>
                      </div>
                    </div>
                  </div>
                )}


              </>
            ) : (
              <div className="error-state">
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <h4>📝 No Summary Available</h4>
                  <p style={{ marginBottom: '16px', color: 'var(--text-secondary)' }}>
                    This bookmark doesn't have a generated summary yet. Visit the Generate Summaries panel to create AI-powered summaries for your bookmarks.
                  </p>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('📝 Generate Summaries button clicked')
                      if (onOpenPanel) {
                        onOpenPanel('summaries')
                        // Keep the card flipped so user can see the summary when it's generated
                      } else {
                        alert('💡 Tip: Use the "Generate Summaries" tool in the sidebar to create AI-powered summaries for your bookmarks!')
                      }
                    }}
                    style={{
                      padding: '8px 16px',
                      background: 'var(--accent-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      marginRight: '8px',
                      pointerEvents: 'auto',
                      position: 'relative',
                      zIndex: 1001
                    }}
                  >
                    📝 Open Generate Summaries
                  </button>

                </div>
              </div>
            )}
          </div>

          {/* Flip Button - Bottom Right Corner */}
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleFlip();
            }}
            className="flip-button-corner"
            title="Back to front"
            style={{
              position: 'absolute',
              bottom: '10px',
              right: '10px',
              background: 'var(--accent-color)',
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
              zIndex: 1002,
              pointerEvents: 'auto'
            }}
          >
            <RotateCcw size={16} />
          </button>

        </div>
      </div>
    </div>
  )
}
