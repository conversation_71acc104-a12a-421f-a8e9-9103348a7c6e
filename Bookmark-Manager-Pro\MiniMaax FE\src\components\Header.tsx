import { Download, Search, Settings, Upload } from 'lucide-react'
import React from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

interface HeaderProps {
  onToggleImport: () => void
  importPanelOpen: boolean
}

export const Header: React.FC<HeaderProps> = ({ onToggleImport, importPanelOpen }) => {
  const {
    searchQuery,
    setSearchQuery,
    filterType,
    setFilterType,
    filteredBookmarks
  } = useBookmarks()

  return (
    <header className="header">
      <div className="header-left">
        <h1 className="header-title">Bookmark Studio</h1>
        <span className="bookmark-count">
          {filteredBookmarks.length} bookmarks
        </span>
      </div>

      <div className="header-center">
        <div className="search-container">
          <Search className="search-icon" size={20} />
          <input
            type="text"
            placeholder="Search bookmarks, tags, or descriptions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="search-clear"
              aria-label="Clear search"
            >
              ×
            </button>
          )}
        </div>

        <div className="filter-buttons">
          <button
            onClick={() => setFilterType('all')}
            className={`filter-btn ${filterType === 'all' ? 'active' : ''}`}
          >
            All
          </button>
          <button
            onClick={() => setFilterType('favorites')}
            className={`filter-btn ${filterType === 'favorites' ? 'active' : ''}`}
          >
            Favorites
          </button>
          <button
            onClick={() => setFilterType('recent')}
            className={`filter-btn ${filterType === 'recent' ? 'active' : ''}`}
          >
            Recent
          </button>
        </div>
      </div>

      <div className="header-right">
        <button
          onClick={onToggleImport}
          className={`import-btn ${importPanelOpen ? 'active' : ''}`}
          aria-label="Toggle import panel"
        >
          <Upload size={18} />
          Import
        </button>

        <button className="action-btn" aria-label="Export bookmarks">
          <Download size={18} />
        </button>

        <button className="action-btn" aria-label="Settings">
          <Settings size={18} />
        </button>
      </div>
    </header>
  )
}
