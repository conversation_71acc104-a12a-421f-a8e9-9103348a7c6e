import {
  Bar<PERSON><PERSON>3,
  <PERSON>,
  Edit3,
  ListMusic,
  Play,
  Plus,
  Sparkles,
  Trash2,
  X,
  Zap
} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { PlaylistAnalytics, smartPlaylistService, SmartPlaylistSuggestion } from '../services/smartPlaylistService'
import type { Playlist } from '../types'

interface PlaylistPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const PlaylistPanel: React.FC<PlaylistPanelProps> = ({ isOpen, onClose }) => {
  // Remove theme conditionals - always use modern design

  const {
    bookmarks,
    playlists,
    selectedBookmarks,
    filteredBookmarks,
    isSelectMode,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    getPlaylistBookmarks,
    selectedPlaylist,
    setSelectedPlaylist
  } = useBookmarks()

  const [newPlaylistName, setNewPlaylistName] = useState('')
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('')
  const [newPlaylistColor, setNewPlaylistColor] = useState('#4a9eff')
  const [editMode, setEditMode] = useState<string | null>(null)
  const [editedName, setEditedName] = useState('')
  const [editedDescription, setEditedDescription] = useState('')
  const [editedColor, setEditedColor] = useState('')

  // Smart playlist features
  const [smartSuggestions, setSmartSuggestions] = useState<SmartPlaylistSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [selectedPlaylistAnalytics, setSelectedPlaylistAnalytics] = useState<PlaylistAnalytics | null>(null)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [autoCreateEnabled, setAutoCreateEnabled] = useState(true)

  // Reset form when panel opens
  useEffect(() => {
    if (isOpen) {
      setNewPlaylistName('')
      setNewPlaylistDescription('')
      setNewPlaylistColor('#4a9eff')
      setEditMode(null)
    }
  }, [isOpen])

  const handleCreatePlaylist = () => {
    if (!newPlaylistName.trim()) return

    // Get bookmarkIds from selected bookmarks if in select mode, otherwise use filtered bookmarks
    const bookmarkIds = isSelectMode && selectedBookmarks.length > 0
      ? selectedBookmarks.map(b => b.id)
      : []

    createPlaylist(
      newPlaylistName.trim(),
      newPlaylistDescription.trim(),
      newPlaylistColor,
      bookmarkIds
    )

    // Reset form
    setNewPlaylistName('')
    setNewPlaylistDescription('')
    setNewPlaylistColor('#4a9eff')
  }

  const handleUpdatePlaylist = (id: string) => {
    if (!editedName.trim()) return

    updatePlaylist(id, {
      name: editedName.trim(),
      description: editedDescription.trim(),
      color: editedColor
    })

    setEditMode(null)
  }

  const handleEditPlaylist = (playlist: Playlist) => {
    setEditMode(playlist.id)
    setEditedName(playlist.name)
    setEditedDescription(playlist.description)
    setEditedColor(playlist.color)
  }

  const handleDeletePlaylist = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this playlist?')) {
      await deletePlaylist(id)
      if (selectedPlaylist === id) {
        setSelectedPlaylist(null)
      }
      // Refresh suggestions after deletion
      if (showSuggestions) {
        generateSmartSuggestions()
      }
    }
  }

  // Smart playlist functions
  const generateSmartSuggestions = async () => {
    setLoadingSuggestions(true)
    try {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        bookmarks,
        playlists,
        {
          maxSuggestions: 5,
          minConfidence: 0.6,
          includeTemporalAnalysis: true,
          includeBehavioralAnalysis: true,
          includeSemanticAnalysis: true
        }
      )
      setSmartSuggestions(suggestions)
      setShowSuggestions(true)
    } catch (error) {
      console.error('Failed to generate smart suggestions:', error)
    } finally {
      setLoadingSuggestions(false)
    }
  }

  const createPlaylistFromSuggestion = async (suggestion: SmartPlaylistSuggestion) => {
    try {
      await createPlaylist(
        suggestion.name,
        suggestion.description,
        suggestion.color,
        suggestion.bookmarkIds
      )

      // Remove the suggestion from the list
      setSmartSuggestions(prev => prev.filter(s => s.id !== suggestion.id))
    } catch (error) {
      console.error('Failed to create playlist from suggestion:', error)
    }
  }



  const autoCreateSmartPlaylists = async () => {
    try {
      const autoSuggestions = await smartPlaylistService.autoCreatePlaylists(
        bookmarks,
        playlists,
        { autoCreateThreshold: 0.85 }
      )

      for (const suggestion of autoSuggestions) {
        await createPlaylist(
          suggestion.name,
          suggestion.description + ' (Auto-created)',
          suggestion.color,
          suggestion.bookmarkIds
        )
      }

      if (autoSuggestions.length > 0) {
        alert(`Auto-created ${autoSuggestions.length} smart playlist(s)!`)
      }
    } catch (error) {
      console.error('Failed to auto-create playlists:', error)
    }
  }

  // Show playlist analytics
  const showPlaylistAnalytics = async (playlistId: string) => {
    try {
      const playlist = playlists.find(p => p.id === playlistId)
      if (!playlist) return

      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        bookmarks
      )

      setSelectedPlaylistAnalytics(analytics)
      setShowAnalytics(true)
      console.log(`📊 Analytics for playlist: ${playlist.name}`, analytics)
    } catch (error) {
      console.error('Failed to generate playlist analytics:', error)
    }
  }

  const handleSelectPlaylist = (id: string) => {
    setSelectedPlaylist(id === selectedPlaylist ? null : id)
    // Don't close panel in tabbed mode - let user continue working with playlists
  }

  const getBookmarkCount = (playlistId: string) => {
    return getPlaylistBookmarks(playlistId).length
  }

  const availableColors = [
    '#4a9eff', // Blue
    '#22c55e', // Green
    '#f59e0b', // Yellow
    '#ef4444', // Red
    'var(--accent-color)', // Purple
    '#06b6d4', // Cyan
    '#ec4899', // Pink
    '#64748b', // Gray
  ]

  // Early return if panel is closed
  if (!isOpen) return null

  return (
    <div className="import-panel organization-panel playlist-panel">
      <div className="playlist-header">
        <h2 className="playlist-title">Smart Playlists</h2>
        <div className="smart-controls">
          <button
            className="smart-btn generate-btn"
            onClick={generateSmartSuggestions}
            disabled={loadingSuggestions}
            title="Generate AI playlist suggestions"
          >
            <Sparkles size={16} />
            {loadingSuggestions ? 'Generating...' : 'AI Suggest'}
          </button>
          <button
            className="smart-btn auto-create-btn"
            onClick={autoCreateSmartPlaylists}
            disabled={!autoCreateEnabled}
            title="Auto-create smart playlists"
          >
            <Zap size={16} />
            Auto-Create
          </button>
        </div>
        <button onClick={onClose} className="playlist-close-btn" aria-label="Close playlist panel">
          <X size={20} />
        </button>
      </div>

      <div className="playlist-content">
        {/* Smart Suggestions Panel */}
        {showSuggestions && (
          <div className="smart-suggestions-panel">
            <div className="suggestions-header">
              <h4>🤖 AI Playlist Suggestions</h4>
              <button
                className="close-suggestions-btn"
                onClick={() => setShowSuggestions(false)}
              >
                ✕
              </button>
            </div>
            <div className="suggestions-list">
              {smartSuggestions.length === 0 ? (
                <p className="no-suggestions">No smart suggestions available. Try adding more bookmarks!</p>
              ) : (
                smartSuggestions.map(suggestion => (
                  <div key={suggestion.id} className="suggestion-card">
                    <div className="suggestion-header">
                      <div
                        className="suggestion-color"
                        style={{ backgroundColor: suggestion.color }}
                      ></div>
                      <div className="suggestion-info">
                        <h5>{suggestion.name}</h5>
                        <p className="suggestion-description">{suggestion.description}</p>
                        <div className="suggestion-meta">
                          <span className="bookmark-count">{suggestion.bookmarkIds.length} bookmarks</span>
                          <span className="confidence-score">Confidence: {Math.round(suggestion.confidence * 100)}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="suggestion-actions">
                      <button
                        className="create-from-suggestion-btn"
                        onClick={() => createPlaylistFromSuggestion(suggestion)}
                      >
                        ✨ Create Playlist
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Create New Playlist Section */}
        <div className="playlist-section">
          <h3 className="playlist-section-title">Create New Playlist</h3>
          <p className="playlist-section-description">
            Create a custom playlist to organize your bookmarks by theme or purpose.
          </p>

          <div className="playlist-input-group">
            <label htmlFor="playlist-name" className="playlist-label">Playlist Name</label>
            <input
              id="playlist-name"
              type="text"
              value={newPlaylistName}
              onChange={(e) => setNewPlaylistName(e.target.value)}
              className="playlist-input-field"
              placeholder="My Awesome Playlist"
            />
          </div>

          <div className="playlist-input-group">
            <label htmlFor="playlist-description" className="playlist-label">Description</label>
            <textarea
              id="playlist-description"
              value={newPlaylistDescription}
              onChange={(e) => setNewPlaylistDescription(e.target.value)}
              className="playlist-textarea-field"
              placeholder="A collection of my favorite websites"
              rows={3}
              style={{ resize: 'vertical', minHeight: '80px' }}
            />
          </div>

          <div className="playlist-input-group">
            <label className="playlist-label">Color Theme</label>
            <div className="format-options" style={{ gridTemplateColumns: 'repeat(4, 1fr)' }}>
              {availableColors.map(color => (
                <button
                  key={color}
                  onClick={() => setNewPlaylistColor(color)}
                  className={`format-option ${newPlaylistColor === color ? 'active' : ''}`}
                  style={{
                    backgroundColor: color,
                    color: 'white',
                    border: newPlaylistColor === color ? '3px solid white' : '1px solid transparent'
                  }}
                >
                  <div style={{ width: '20px', height: '20px', borderRadius: '50%', backgroundColor: 'rgba(255,255,255,0.3)' }} />
                  <span style={{ fontSize: '12px' }}>Color</span>
                </button>
              ))}
            </div>
          </div>

          <div className="export-area">
            <ListMusic size={32} className="upload-icon" />
            <p className="upload-text">Ready to create playlist</p>
            <p className="upload-hint">
              {isSelectMode ? (
                selectedBookmarks.length > 0
                  ? `${selectedBookmarks.length} selected bookmarks will be added`
                  : 'No bookmarks selected. Creating an empty playlist'
              ) : (
                'Creating an empty playlist. Add bookmarks later'
              )}
            </p>
            <button
              onClick={handleCreatePlaylist}
              className="import-another-btn"
              disabled={!newPlaylistName.trim()}
            >
              <Plus size={16} />
              Create Playlist
            </button>
          </div>
        </div>

        {/* Existing Playlists Section */}
        <div className="import-section">
          <h3 className="section-title">Your Playlists</h3>
          <p className="section-description">
            Manage your existing playlists and their bookmarks.
          </p>

          {playlists.length === 0 ? (
            <div className="browser-instructions">
              <div className="instruction-item">
                <ListMusic size={16} />
                <span>You don't have any playlists yet. Create your first playlist above.</span>
              </div>
            </div>
          ) : (
            <div className="browser-instructions">
              {playlists.map(playlist => (
                <div key={playlist.id}>
                  {editMode === playlist.id ? (
                    <div className="import-section">
                      <h3 className="section-title">Edit Playlist</h3>
                      <div className="filename-input">
                        <label className="upload-text">Name</label>
                        <input
                          type="text"
                          value={editedName}
                          onChange={(e) => setEditedName(e.target.value)}
                          className="filename-field"
                          placeholder="Playlist Name"
                        />
                      </div>

                      <div className="filename-input">
                        <label className="upload-text">Description</label>
                        <textarea
                          value={editedDescription}
                          onChange={(e) => setEditedDescription(e.target.value)}
                          className="filename-field"
                          placeholder="Description"
                          rows={2}
                          style={{ resize: 'vertical', minHeight: '60px' }}
                        />
                      </div>

                      <div className="template-actions">
                        <button
                          onClick={() => setEditMode(null)}
                          className="template-btn"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={() => handleUpdatePlaylist(playlist.id)}
                          className="template-btn"
                          disabled={!editedName.trim()}
                          style={{ backgroundColor: 'var(--accent-color)', color: 'white' }}
                        >
                          <Check size={16} />
                          Save
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="instruction-item">
                      <div
                        style={{
                          width: '16px',
                          height: '16px',
                          borderRadius: '50%',
                          backgroundColor: playlist.color,
                          flexShrink: 0
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <span style={{ fontWeight: '500' }}>{playlist.name}</span>
                        {playlist.description && (
                          <span style={{ color: 'var(--text-secondary)', fontSize: '13px', marginLeft: '8px' }}>
                            - {playlist.description}
                          </span>
                        )}
                        <span style={{ color: 'var(--text-muted)', fontSize: '12px', marginLeft: '8px' }}>
                          ({getBookmarkCount(playlist.id)} bookmarks)
                        </span>
                      </div>
                      <div className="template-actions" style={{ gap: '4px' }}>
                        <button
                          onClick={() => handleSelectPlaylist(playlist.id)}
                          className={`template-btn ${selectedPlaylist === playlist.id ? 'active' : ''}`}
                          style={{
                            padding: '4px 8px',
                            backgroundColor: selectedPlaylist === playlist.id ? 'var(--accent-color)' : 'transparent',
                            color: selectedPlaylist === playlist.id ? 'white' : 'var(--text-secondary)'
                          }}
                        >
                          <Play size={12} />
                        </button>
                        <button
                          onClick={() => showPlaylistAnalytics(playlist.id)}
                          className="template-btn"
                          style={{ padding: '4px 8px' }}
                          title="View Analytics"
                        >
                          <BarChart3 size={12} />
                        </button>
                        <button
                          onClick={() => handleEditPlaylist(playlist)}
                          className="template-btn"
                          style={{ padding: '4px 8px' }}
                        >
                          <Edit3 size={12} />
                        </button>
                        <button
                          onClick={() => handleDeletePlaylist(playlist.id)}
                          className="template-btn"
                          style={{ padding: '4px 8px', color: 'var(--error-color)' }}
                        >
                          <Trash2 size={12} />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Analytics Modal */}
      {showAnalytics && selectedPlaylistAnalytics && (
        <div className="analytics-modal-overlay" onClick={() => setShowAnalytics(false)}>
          <div className="analytics-modal" onClick={e => e.stopPropagation()}>
            <div className="analytics-header">
              <h3>📊 Playlist Analytics</h3>
              <button
                className="close-analytics-btn"
                onClick={() => setShowAnalytics(false)}
              >
                ✕
              </button>
            </div>
            <div className="analytics-content">
              <div className="analytics-section">
                <h4>📈 Overview</h4>
                <div className="analytics-grid">
                  <div className="analytics-card">
                    <span className="analytics-label">Total Bookmarks</span>
                    <span className="analytics-value">{selectedPlaylistAnalytics.totalBookmarks}</span>
                  </div>
                  <div className="analytics-card">
                    <span className="analytics-label">Avg. Engagement</span>
                    <span className="analytics-value">{selectedPlaylistAnalytics.averageEngagement.toFixed(1)}</span>
                  </div>
                  <div className="analytics-card">
                    <span className="analytics-label">Content Diversity</span>
                    <span className="analytics-value">{selectedPlaylistAnalytics.contentDiversity.toFixed(1)}%</span>
                  </div>
                </div>
              </div>

              <div className="analytics-section">
                <h4>🏷️ Top Categories</h4>
                <div className="categories-list">
                  {selectedPlaylistAnalytics.topCategories.map((category, index) => (
                    <div key={index} className="category-item">
                      <span className="category-name">{category.name}</span>
                      <div className="category-bar">
                        <div
                          className="category-fill"
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <span className="category-percentage">{category.percentage.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="analytics-section">
                <h4>🌐 Top Domains</h4>
                <div className="domains-list">
                  {selectedPlaylistAnalytics.topDomains.map((domain, index) => (
                    <div key={index} className="domain-item">
                      <span className="domain-name">{domain.domain}</span>
                      <span className="domain-count">{domain.count} bookmarks</span>
                    </div>
                  ))}
                </div>
              </div>

              {selectedPlaylistAnalytics.temporalPatterns.length > 0 && (
                <div className="analytics-section">
                  <h4>📅 Temporal Patterns</h4>
                  <div className="temporal-patterns">
                    {selectedPlaylistAnalytics.temporalPatterns.map((pattern, index) => (
                      <div key={index} className="pattern-item">
                        <span className="pattern-period">{pattern.period}</span>
                        <span className="pattern-count">{pattern.count} bookmarks</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {selectedPlaylistAnalytics.recommendations.length > 0 && (
                <div className="analytics-section">
                  <h4>💡 Recommendations</h4>
                  <div className="recommendations-list">
                    {selectedPlaylistAnalytics.recommendations.map((rec, index) => (
                      <div key={index} className="recommendation-item">
                        <span className="recommendation-text">{rec}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}