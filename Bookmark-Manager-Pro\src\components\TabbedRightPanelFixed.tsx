import { Brain, Download, FileText, Globe, Layers, ListMusic, Play, Shield, SplitSquareVertical, Upload, Video, X } from 'lucide-react'
import React from 'react'
import { ContentPanel } from './ContentPanel'
import { DomainPanel } from './DomainPanel'
import { ExportPanel } from './ExportPanel'
import { HealthCheckPanel } from './HealthCheckPanel'
import { HybridPanel } from './HybridPanel'
import { ImportPanel } from './ImportPanel'
import { MultimediaOrganizationPanel } from './MultimediaOrganizationPanel'
import { MultimediaPlaybackPanel } from './MultimediaPlaybackPanel'
import { PlaylistPanel } from './PlaylistPanel'
import { SmartAIPanel } from './SmartAIPanel'
import { SplitPanel } from './SplitPanel'
import { SummaryPanel } from './SummaryPanel'

export type PanelType = 'health-check' | 'summaries' | 'hybrid' | 'smart-ai' | 'domain' | 'content' | 'import' | 'export' | 'split' | 'playlist' | 'multimedia' | 'multimedia-player'

interface Tab {
  id: PanelType
  label: string
  icon: React.ComponentType<{ size?: number }>
  component: React.ComponentType<{ isOpen: boolean; onClose: () => void }>
}

interface TabbedRightPanelProps {
  activePanels: PanelType[]
  activeTab: PanelType | null
  onTabChange: (tab: PanelType) => void
  onCloseTab: (tab: PanelType) => void
  onCloseAll: () => void
  onOpenPanel?: (panelType: PanelType) => void
}

// Temporary placeholder component for panels that don't exist yet
const PlaceholderPanel: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ isOpen, onClose }) => {
  if (!isOpen) return null
  return (
    <div className="import-panel">
      <div className="import-header">
        <h2 className="import-title">Coming Soon</h2>
        <button onClick={onClose} className="close-btn">
          <X size={20} />
        </button>
      </div>
      <div className="import-content">
        <p>This feature is being developed...</p>
      </div>
    </div>
  )
}

const tabs: Tab[] = [
  {
    id: 'health-check',
    label: 'Health Check',
    icon: Shield,
    component: HealthCheckPanel
  },
  {
    id: 'summaries',
    label: 'Summaries',
    icon: FileText,
    component: SummaryPanel
  },
  {
    id: 'hybrid',
    label: 'Hybrid',
    icon: Layers,
    component: HybridPanel
  },
  {
    id: 'smart-ai',
    label: 'Smart AI',
    icon: Brain,
    component: SmartAIPanel
  },
  {
    id: 'domain',
    label: 'Domain',
    icon: Globe,
    component: DomainPanel
  },
  {
    id: 'content',
    label: 'Content',
    icon: FileText,
    component: ContentPanel
  },
  {
    id: 'import',
    label: 'Import',
    icon: Upload,
    component: ImportPanel
  },
  {
    id: 'export',
    label: 'Export',
    icon: Download,
    component: ExportPanel
  },
  {
    id: 'split',
    label: 'Split',
    icon: SplitSquareVertical,
    component: SplitPanel
  },
  {
    id: 'playlist',
    label: 'Playlist',
    icon: ListMusic,
    component: PlaylistPanel
  },
  {
    id: 'multimedia',
    label: 'Multimedia',
    icon: Video,
    component: MultimediaOrganizationPanel
  },
  {
    id: 'multimedia-player',
    label: 'Media Player',
    icon: Play,
    component: MultimediaPlaybackPanel
  }
]

export const TabbedRightPanel: React.FC<TabbedRightPanelProps> = ({
  activePanels,
  activeTab,
  onTabChange,
  onCloseTab,
  onCloseAll,
  onOpenPanel
}) => {
  if (activePanels.length === 0) return null

  const activeTabData = tabs.find(tab => tab.id === activeTab)
  const ActiveComponent = activeTabData?.component

  return (
    <div className="tabbed-right-panel">
      {/* Tab Header */}
      <div className="tab-header">
        <div className="tab-list">
          {activePanels.map(panelId => {
            const tab = tabs.find(t => t.id === panelId)
            if (!tab) return null
            
            const Icon = tab.icon
            const isActive = activeTab === panelId
            
            return (
              <div
                key={panelId}
                className={`tab-item ${isActive ? 'active' : ''}`}
                onClick={() => onTabChange(panelId)}
                title={panelId === 'smart-ai' ? 'Smart AI Organization (requires Gemini API key)' : 
                       panelId === 'multimedia-player' ? 'Multimedia Player - Integrated with Tabbed System' : 
                       tab.label}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onCloseTab(panelId)
                  }}
                  className="close-btn ml-2"
                  aria-label={`Close ${tab.label}`}
                  title={`Close ${tab.label}`}
                >
                  <X size={14} />
                </button>
              </div>
            )
          })}
        </div>
        
        {activePanels.length > 1 && (
          <button
            onClick={onCloseAll}
            className="close-btn"
            aria-label="Close all panels"
            title="Close all panels"
          >
            <X size={16} />
          </button>
        )}
      </div>

      {/* Panel Content */}
      <div className="tab-content">
        {ActiveComponent && (
          <ActiveComponent
            isOpen={true}
            onClose={() => activeTab && onCloseTab(activeTab)}
          />
        )}
      </div>
    </div>
  )
}
