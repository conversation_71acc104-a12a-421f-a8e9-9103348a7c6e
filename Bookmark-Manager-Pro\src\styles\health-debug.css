/* HEALTH CHECK PANEL DEBUG STYLES - WORKS IN ALL THEME MODES */

/* Force visibility for debug indicator in both Classic and Modern themes */
.health-debug-indicator-closed {
  position: fixed !important;
  top: 10px !important;
  right: 10px !important;
  background: #ff0000 !important;
  color: #ffffff !important;
  padding: 10px !important;
  z-index: 999999 !important;
  font-size: 14px !important;
  font-weight: bold !important;
  border: 2px solid #ffff00 !important;
  border-radius: 4px !important;
  box-shadow: 0 0 10px rgba(0,0,0,0.5) !important;
  font-family: Arial, sans-serif !important;
  text-align: center !important;
  min-width: 120px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: none !important;
  left: auto !important;
  bottom: auto !important;
}

/* Override any theme-specific styles for debug indicator */
.theme-classic .health-debug-indicator-closed,
.theme-modern .health-debug-indicator-closed,
.light .health-debug-indicator-closed,
.dark .health-debug-indicator-closed {
  position: fixed !important;
  top: 10px !important;
  right: 10px !important;
  background: #ff0000 !important;
  color: #ffffff !important;
  z-index: 999999 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force visibility for debug panel in both Classic and Modern themes */
.health-debug-panel-open {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 400px !important;
  height: 100vh !important;
  z-index: 999999 !important;
  background: #ffffff !important;
  color: #000000 !important;
  border: 5px solid #ff0000 !important;
  border-left: 5px solid #ff0000 !important;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: none !important;
  left: auto !important;
  bottom: auto !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Override any theme-specific styles for debug panel */
.theme-classic .health-debug-panel-open,
.theme-modern .health-debug-panel-open,
.light .health-debug-panel-open,
.dark .health-debug-panel-open {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 400px !important;
  height: 100vh !important;
  z-index: 999999 !important;
  background: #ffffff !important;
  color: #000000 !important;
  border: 5px solid #ff0000 !important;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force visibility for debug header in both Classic and Modern themes */
.health-debug-header {
  background: #f0f0f0 !important;
  color: #000000 !important;
  border-bottom: 3px solid #ff0000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 16px 20px !important;
  min-height: 60px !important;
  flex-shrink: 0 !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  box-sizing: border-box !important;
  width: 100% !important;
}

/* Override any theme-specific styles for debug header */
.theme-classic .health-debug-header,
.theme-modern .health-debug-header,
.light .health-debug-header,
.dark .health-debug-header {
  background: #f0f0f0 !important;
  color: #000000 !important;
  border-bottom: 3px solid #ff0000 !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force visibility for all debug content */
.health-debug-panel-open * {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Override theme-specific text colors in debug panel */
.health-debug-panel-open .import-title,
.health-debug-panel-open .section-title,
.health-debug-panel-open .section-description,
.health-debug-panel-open h1,
.health-debug-panel-open h2,
.health-debug-panel-open h3,
.health-debug-panel-open h4,
.health-debug-panel-open h5,
.health-debug-panel-open h6,
.health-debug-panel-open p,
.health-debug-panel-open span,
.health-debug-panel-open div {
  color: #000000 !important;
}

/* Override theme-specific button styles in debug panel */
.health-debug-panel-open .close-btn,
.health-debug-panel-open .format-option,
.health-debug-panel-open button {
  background: #e0e0e0 !important;
  color: #000000 !important;
  border: 1px solid #666666 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.health-debug-panel-open .close-btn:hover,
.health-debug-panel-open .format-option:hover,
.health-debug-panel-open button:hover {
  background: #d0d0d0 !important;
  color: #000000 !important;
}

/* Ensure debug styles work in all possible theme combinations */
.theme-classic.light .health-debug-indicator-closed,
.theme-classic.dark .health-debug-indicator-closed,
.theme-modern.light .health-debug-indicator-closed,
.theme-modern.dark .health-debug-indicator-closed,
.theme-classic.light .health-debug-panel-open,
.theme-classic.dark .health-debug-panel-open,
.theme-modern.light .health-debug-panel-open,
.theme-modern.dark .health-debug-panel-open {
  position: fixed !important;
  z-index: 999999 !important;
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
}

/* Additional specificity overrides for stubborn theme styles */
body .health-debug-indicator-closed,
html .health-debug-indicator-closed,
body .health-debug-panel-open,
html .health-debug-panel-open {
  position: fixed !important;
  z-index: 999999 !important;
  visibility: visible !important;
  opacity: 1 !important;
}
