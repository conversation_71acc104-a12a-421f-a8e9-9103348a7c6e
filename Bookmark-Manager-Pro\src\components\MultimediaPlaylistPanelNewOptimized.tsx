import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Check,
    ChevronDown,
    ChevronUp,
    Mic,
    Play,
    Settings,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>or<PERSON>,
    Volume2,
    X,
    Zap
} from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { FixedSizeList } from 'react-window'
import type { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import '../styles/optimized-panels.css'

interface PlaylistItem {
  id: string
  title: string
  url: string
  type: 'video' | 'audio' | 'document' | 'article' | 'image'
  thumbnail?: string
  duration?: string
  readingTime?: string
}

interface Playlist {
  id: string
  name: string
  type: 'video' | 'audio' | 'reading' | 'mixed'
  items: PlaylistItem[]
}

interface MultimediaPlaylistPanelNewOptimizedProps {
  isOpen: boolean
  onClose: () => void
  bookmarks?: Bookmark[]
}

const MultimediaPlaylistPanelNewOptimized: React.FC<MultimediaPlaylistPanelNewOptimizedProps> = ({
  isOpen,
  onClose,
  bookmarks: propBookmarks
}) => {
  const { bookmarks: contextBookmarks } = useBookmarks()
  const bookmarks = propBookmarks || contextBookmarks
  // State management
  const [playlistType, setPlaylistType] = useState<'video' | 'audio' | 'reading' | 'mixed'>('video')
  const [playlistName, setPlaylistName] = useState('')
  const [playlistDescription, setPlaylistDescription] = useState('')
  const [enableAI, setEnableAI] = useState(true)
  const [enableTTS, setEnableTTS] = useState(false)
  const [autoAdvance, setAutoAdvance] = useState(true)
  const [selectedBookmarksForPlaylist, setSelectedBookmarksForPlaylist] = useState<Bookmark[]>([])
  const [creationStatus, setCreationStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [createdPlaylist, setCreatedPlaylist] = useState<Playlist | null>(null)
  const [showPlayer, setShowPlayer] = useState(false)
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [preloadedVideos] = useState(new Set<number>())
  const [showCountdown, setShowCountdown] = useState(false)
  const [countdownSeconds, setCountdownSeconds] = useState(5)
  const [showVideoBuilder, setShowVideoBuilder] = useState(false)

  // Collapsible sections state
  const [showBookmarkSelection, setShowBookmarkSelection] = useState(true)
  const [showEnhancementOptions, setShowEnhancementOptions] = useState(false)
  const [showQuickTemplates, setShowQuickTemplates] = useState(false)

  // New optimizations for memory efficiency
  const selectedWithHosts = useMemo(() => {
    return selectedBookmarksForPlaylist.map(bookmark => ({
      ...bookmark,
      hostname: (() => {
        try {
          return new URL(bookmark.url).hostname
        } catch {
          return 'Invalid URL'
        }
      })()
    }))
  }, [selectedBookmarksForPlaylist])

  const handleRemoveBookmark = useCallback((id: string) => {
    setSelectedBookmarksForPlaylist(prev => prev.filter(b => b.id !== id))
  }, [setSelectedBookmarksForPlaylist])

  // Removed unused handleSelectQueueItem

  // Auto-generate playlist name based on type
  useEffect(() => {
    if (!playlistName || playlistName.includes('Playlist')) {
      const typeEmojis = {
        video: '🎥',
        audio: '🎵',
        reading: '📚',
        mixed: '🎬'
      }
      setPlaylistName(`${typeEmojis[playlistType]} ${playlistType.charAt(0).toUpperCase() + playlistType.slice(1)} Playlist`)
    }
  }, [playlistType])

  // Initialize selected bookmarks
  useEffect(() => {
    if (selectedBookmarksForPlaylist.length === 0 && bookmarks.length > 0) {
      setSelectedBookmarksForPlaylist(bookmarks.slice(0, 10))
    }
    return () => {
      setSelectedBookmarksForPlaylist([]) // Cleanup large arrays on unmount
    }
  }, [bookmarks])

  // Helper functions
  const getBookmarkTypeCount = (type: string) => {
    return bookmarks.filter(bookmark => {
      const url = bookmark.url.toLowerCase()
      switch (type) {
        case 'video':
          return url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')
        case 'audio':
          return url.includes('spotify.com') || url.includes('soundcloud.com') || url.includes('audio')
        case 'document':
          return url.includes('.pdf') || url.includes('docs.google.com') || url.includes('document')
        default:
          return false
      }
    }).length
  }

  const getBookmarkTypeIcon = (bookmark: Bookmark) => {
    const url = bookmark.url.toLowerCase()
    if (url.includes('youtube.com') || url.includes('vimeo.com')) return '🎥'
    if (url.includes('spotify.com') || url.includes('soundcloud.com')) return '🎵'
    if (url.includes('.pdf') || url.includes('docs.google.com')) return '📄'
    return '🌐'
  }

  const convertBookmarksToPlaylistItems = useCallback((bookmarks: Bookmark[]): PlaylistItem[] => {
    return bookmarks.map(bookmark => {
      const url = bookmark.url.toLowerCase()
      let type: PlaylistItem['type'] = 'article'
      
      if (url.includes('youtube.com') || url.includes('vimeo.com')) type = 'video'
      else if (url.includes('spotify.com') || url.includes('soundcloud.com')) type = 'audio'
      else if (url.includes('.pdf') || url.includes('docs.google.com')) type = 'document'
      
      return {
        id: bookmark.id,
        title: bookmark.title,
        url: bookmark.url,
        type,
        thumbnail: bookmark.favicon
      }
    })
  }, [])

  const handleCreatePlaylist = async () => {
    if (selectedBookmarksForPlaylist.length === 0) return
    
    setCreationStatus('processing')
    
    // Simulate playlist creation
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const playlist: Playlist = {
      id: `playlist-${Date.now()}`,
      name: playlistName,
      type: playlistType,
      items: convertBookmarksToPlaylistItems(selectedBookmarksForPlaylist)
    }
    
    setCreatedPlaylist(playlist)
    setCreationStatus('success')
  }

  const playPlaylist = () => {
    if (createdPlaylist) {
      setShowPlayer(true)
      setCurrentVideoIndex(0)
    }
  }

  const resetForm = () => {
    setCreationStatus('idle')
    setCreatedPlaylist(null)
    setSelectedBookmarksForPlaylist([])
    setPlaylistName('')
    setPlaylistDescription('')
  }

  const playVideo = (index: number) => {
    if (createdPlaylist?.items[index]) {
      setCurrentVideoIndex(index)
      setIsPlaying(true)
      window.open(createdPlaylist.items[index].url, '_blank')
    }
  }

  const cancelCountdown = () => {
    setShowCountdown(false)
    setCountdownSeconds(5)
  }

  const handleVideoBuilderPlaylist = (playlist: Playlist) => {
    setCreatedPlaylist(playlist)
    setShowVideoBuilder(false)
    setShowPlayer(true)
  }

  // Memoized counts for performance
  const videoCount = useMemo(() => getBookmarkTypeCount('video'), [bookmarks])
  const audioCount = useMemo(() => getBookmarkTypeCount('audio'), [bookmarks])
  const documentCount = useMemo(() => getBookmarkTypeCount('document'), [bookmarks])
  const mixedCount = useMemo(() => bookmarks.length, [bookmarks])

  if (!isOpen) return null

  return (
    <div className="optimized-panel multimedia-playlist-panel-new">
      {/* Header */}
      <div className="panel-header">
        <div className="header-content">
          <h2 className="panel-title">
            🎬 Enhanced Multimedia Playlist Creator
          </h2>
          <p className="panel-subtitle">
            Create intelligent playlists with AI enhancement and smart playback
          </p>
        </div>
        <button onClick={onClose} className="close-button">
          <X size={20} />
        </button>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <button
          onClick={() => {
            if (selectedBookmarksForPlaylist.length > 0) {
              const quickPlaylist = {
                id: 'quick-play',
                name: `Quick Play - ${playlistType}`,
                type: playlistType,
                items: convertBookmarksToPlaylistItems(selectedBookmarksForPlaylist)
              }
              setCreatedPlaylist(quickPlaylist)
              setShowPlayer(true)
            }
          }}
          className="action-button primary"
          disabled={selectedBookmarksForPlaylist.length === 0}
        >
          <Play size={16} />
          Quick Play ({selectedBookmarksForPlaylist.length})
        </button>
        <button
          onClick={handleCreatePlaylist}
          className="action-button secondary"
          disabled={selectedBookmarksForPlaylist.length === 0 || creationStatus === 'processing'}
        >
          {creationStatus === 'processing' ? (
            <div className="spinner" />
          ) : (
            <Zap size={16} />
          )}
          Create Playlist
        </button>
        <button
          onClick={() => setShowVideoBuilder(true)}
          className="action-button tertiary"
        >
          <Settings size={16} />
          Advanced Builder
        </button>
      </div>

      {/* Progress Indicator */}
      {creationStatus === 'processing' && (
        <div className="progress-section">
          <div className="progress-bar">
            <div className="progress-fill" style={{ width: '60%' }} />
          </div>
          <p className="progress-text">Creating multimedia playlist...</p>
        </div>
      )}

      {/* Statistics */}
      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-icon">🎥</span>
          <span className="stat-value">{videoCount}</span>
          <span className="stat-label">Videos</span>
        </div>
        <div className="stat-item">
          <span className="stat-icon">🎵</span>
          <span className="stat-value">{audioCount}</span>
          <span className="stat-label">Audio</span>
        </div>
        <div className="stat-item">
          <span className="stat-icon">📄</span>
          <span className="stat-value">{documentCount}</span>
          <span className="stat-label">Documents</span>
        </div>
        <div className="stat-item">
          <span className="stat-icon">📚</span>
          <span className="stat-value">{selectedBookmarksForPlaylist.length}</span>
          <span className="stat-label">Selected</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="panel-content">
        {/* Playlist Type Selection */}
        <div className="content-section">
          <h3 className="section-title">Playlist Type</h3>
          <div className="playlist-type-grid">
            {[
              { type: 'video' as const, icon: '🎥', label: 'Video', count: videoCount },
              { type: 'audio' as const, icon: '🎵', label: 'Audio', count: audioCount },
              { type: 'reading' as const, icon: '📚', label: 'Reading', count: documentCount },
              { type: 'mixed' as const, icon: '🎬', label: 'Mixed', count: mixedCount }
            ].map(({ type, icon, label, count }) => (
              <button
                key={type}
                onClick={() => setPlaylistType(type)}
                className={`type-button ${playlistType === type ? 'active' : ''}`}
              >
                <span className="type-icon">{icon}</span>
                <span className="type-label">{label}</span>
                <span className="type-count">{count}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Playlist Configuration */}
        <div className="content-section">
          <h3 className="section-title">Playlist Configuration</h3>
          <div className="config-grid">
            <div className="config-field">
              <label htmlFor="playlist-name">Name</label>
              <input
                id="playlist-name"
                type="text"
                value={playlistName}
                onChange={(e) => setPlaylistName(e.target.value)}
                placeholder="Enter playlist name..."
                className="config-input"
              />
            </div>
            <div className="config-field">
              <label htmlFor="playlist-description">Description (Optional)</label>
              <textarea
                id="playlist-description"
                value={playlistDescription}
                onChange={(e) => setPlaylistDescription(e.target.value)}
                placeholder="Describe your playlist..."
                className="config-textarea"
                rows={2}
              />
            </div>
          </div>
        </div>

        {/* Enhancement Options */}
        <div className="content-section">
          <button
            onClick={() => setShowEnhancementOptions(!showEnhancementOptions)}
            className="section-toggle"
          >
            <h3 className="section-title">Enhancement Options</h3>
            {showEnhancementOptions ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>
          {showEnhancementOptions && (
            <div className="enhancement-options">
              <label className="enhancement-option">
                <input
                  type="checkbox"
                  checked={enableAI}
                  onChange={(e) => setEnableAI(e.target.checked)}
                />
                <div className="option-content">
                  <Zap size={16} />
                  <span>AI Enhancement</span>
                  <small>Auto-generate summaries and optimize content</small>
                </div>
              </label>
              <label className="enhancement-option">
                <input
                  type="checkbox"
                  checked={enableTTS}
                  onChange={(e) => setEnableTTS(e.target.checked)}
                />
                <div className="option-content">
                  <Mic size={16} />
                  <span>Text-to-Speech</span>
                  <small>Enable voice narration for hands-free experience</small>
                </div>
              </label>
              <label className="enhancement-option">
                <input
                  type="checkbox"
                  checked={autoAdvance}
                  onChange={(e) => setAutoAdvance(e.target.checked)}
                />
                <div className="option-content">
                  <SkipForward size={16} />
                  <span>Auto-Advance</span>
                  <small>Automatically play next item with countdown</small>
                </div>
              </label>
            </div>
          )}
        </div>

        {/* Bookmark Selection */}
        <div className="content-section">
          <button
            onClick={() => setShowBookmarkSelection(!showBookmarkSelection)}
            className="section-toggle"
          >
            <h3 className="section-title">
              Select Bookmarks ({selectedBookmarksForPlaylist.length})
            </h3>
            {showBookmarkSelection ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>
          {showBookmarkSelection && (
            <div className="bookmark-selection">
              <div className="selection-controls">
                <button
                  onClick={() => {
                    const allBookmarks = bookmarks.slice(0, 20)
                    setSelectedBookmarksForPlaylist(allBookmarks)
                  }}
                  className="control-button"
                >
                  Select All ({Math.min(bookmarks.length, 20)})
                </button>
                <button
                  onClick={() => setSelectedBookmarksForPlaylist([])}
                  className="control-button secondary"
                >
                  Clear Selection
                </button>
              </div>
              
              <div className="bookmarks-preview">
                {/* Add virtualization for large lists - assuming react-window is installed */}
                <FixedSizeList
                  height={200}
                  itemCount={selectedBookmarksForPlaylist.length}
                  itemSize={50}
                  width="100%"
                >
                  {({ index, style }) => {
                    const bookmark = selectedWithHosts[index]
                    return (
                      <div style={style} key={bookmark.id} className="bookmark-preview-item">
                        <span className="bookmark-icon">{getBookmarkTypeIcon(bookmark)}</span>
                        <span className="bookmark-title">{bookmark.title}</span>
                        <span className="bookmark-url">{bookmark.hostname}</span>
                        <button
                          onClick={() => handleRemoveBookmark(bookmark.id)}
                          className="remove-button"
                          title="Remove from playlist"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    )
                  }}
                </FixedSizeList>
                {selectedBookmarksForPlaylist.length === 0 && (
                  <div className="empty-selection">
                    No bookmarks selected. Click "Select All" to get started.
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Quick Templates */}
        <div className="content-section">
          <button
            onClick={() => setShowQuickTemplates(!showQuickTemplates)}
            className="section-toggle"
          >
            <h3 className="section-title">Quick Templates</h3>
            {showQuickTemplates ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>
          {showQuickTemplates && (
            <div className="template-actions">
              <button
                onClick={() => {
                  setPlaylistType('video')
                  setPlaylistName('🏃‍♂️ Gym Workout Videos')
                  setEnableTTS(true)
                  setEnableAI(true)
                }}
                className="template-button"
              >
                <Play size={16} />
                Gym Mode
              </button>
              <button
                onClick={() => {
                  setPlaylistType('reading')
                  setPlaylistName('📚 Study Session')
                  setEnableTTS(true)
                  setEnableAI(true)
                }}
                className="template-button"
              >
                <BookOpen size={16} />
                Study Mode
              </button>
              <button
                onClick={() => {
                  setPlaylistType('mixed')
                  setPlaylistName('🌙 Evening Relaxation')
                  setEnableTTS(false)
                  setEnableAI(true)
                }}
                className="template-button"
              >
                <Volume2 size={16} />
                Relax Mode
              </button>
            </div>
          )}
        </div>

        {/* Creation Status */}
        {creationStatus === 'success' && createdPlaylist && (
          <div className="content-section success">
            <div className="status-content">
              <Check size={32} className="status-icon" />
              <h3>Playlist Created Successfully!</h3>
              <p>"{createdPlaylist.name}" is ready with {createdPlaylist.items.length} items</p>
              <div className="status-actions">
                <button onClick={playPlaylist} className="action-button primary">
                  <Play size={16} />
                  Play Playlist
                </button>
                <button onClick={resetForm} className="action-button secondary">
                  Create Another
                </button>
              </div>
            </div>
          </div>
        )}

        {creationStatus === 'error' && (
          <div className="content-section error">
            <div className="status-content">
              <AlertCircle size={32} className="status-icon" />
              <h3>Failed to Create Playlist</h3>
              <p>Please try again or select different bookmarks</p>
              <button onClick={() => setCreationStatus('idle')} className="action-button secondary">
                Try Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Playlist Player Modal */}
      {showPlayer && createdPlaylist && (
        <div className="modal-overlay">
          <div className="modal-content large">
            <div className="modal-header">
              <div>
                <h2>🎬 {createdPlaylist.name}</h2>
                <p>{createdPlaylist.items.length} items • Ready to play</p>
              </div>
              <button onClick={() => setShowPlayer(false)} className="close-button">
                <X size={20} />
              </button>
            </div>
            
            <div className="player-content">
              <div className="now-playing">
                <h3>Now Playing: {currentVideoIndex + 1} of {createdPlaylist.items.length}</h3>
                <div className="current-item">
                  <span className="item-icon">
                    {createdPlaylist.items[currentVideoIndex]?.type === 'video' ? '🎥' :
                     createdPlaylist.items[currentVideoIndex]?.type === 'audio' ? '🎵' :
                     createdPlaylist.items[currentVideoIndex]?.type === 'document' ? '📄' : '🌐'}
                  </span>
                  <span className="item-title">
                    {createdPlaylist.items[currentVideoIndex]?.title || 'Select a video to play'}
                  </span>
                  <span className="item-status">
                    {preloadedVideos.has(currentVideoIndex) ? '✅ Ready' : '⏳ Loading...'}
                  </span>
                </div>
              </div>

              <div className="playback-controls">
                <button
                  onClick={() => setCurrentVideoIndex(Math.max(0, currentVideoIndex - 1))}
                  disabled={currentVideoIndex === 0}
                  className="control-button"
                >
                  <SkipBack size={20} />
                </button>
                <button
                  onClick={() => playVideo(currentVideoIndex)}
                  className="control-button primary"
                >
                  <Play size={20} />
                  {isPlaying ? 'Playing...' : 'Play Current'}
                </button>
                <button
                  onClick={() => setCurrentVideoIndex(Math.min(createdPlaylist.items.length - 1, currentVideoIndex + 1))}
                  disabled={currentVideoIndex >= createdPlaylist.items.length - 1}
                  className="control-button"
                >
                  <SkipForward size={20} />
                </button>
              </div>

              <div className="playlist-queue">
                <h4>Playlist Queue</h4>
                <div className="queue-items">
                  <FixedSizeList
                    height={300}
                    itemCount={createdPlaylist.items.length}
                    itemSize={50}
                    width="100%"
                  >
                    {({ index, style }) => {
                      const item = createdPlaylist.items[index]
                      return (
                        <div
                          style={style}
                          key={item.id}
                          onClick={() => setCurrentVideoIndex(index)}
                          className={`queue-item ${index === currentVideoIndex ? 'active' : ''}`}
                        >
                          <span className="item-number">{index + 1}.</span>
                          <span className="item-icon">
                            {item.type === 'video' ? '🎥' :
                             item.type === 'audio' ? '🎵' :
                             item.type === 'document' ? '📄' : '🌐'}
                          </span>
                          <span className="item-title">{item.title}</span>
                          <span className="item-status">
                            {preloadedVideos.has(index) ? '✅' : '⏳'}
                          </span>
                        </div>
                      )
                    }}
                  </FixedSizeList>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Video Builder Modal */}
      {showVideoBuilder && (
        <div className="modal-overlay">
          <div className="modal-content large">
            <div className="modal-header">
              <div>
                <h2>🎥 Advanced Video Playlist Builder</h2>
                <p>Select from {getBookmarkTypeCount('video')} videos • Smart filtering and batch selection</p>
              </div>
              <button onClick={() => setShowVideoBuilder(false)} className="close-button">
                <X size={20} />
              </button>
            </div>
            
            <div className="builder-content">
              <div className="builder-placeholder">
                <div className="placeholder-icon">🎬</div>
                <h3>Advanced Video Selection Coming Soon!</h3>
                <p>
                  The advanced video builder will include smart filtering by platform (YouTube, Vimeo),
                  collection grouping, batch selection, search, and sorting options perfect for managing
                  large video collections like your {getBookmarkTypeCount('video')} videos.
                </p>
                <div className="placeholder-actions">
                  <button
                    onClick={() => {
                      const videoBookmarks = bookmarks.filter(b => {
                        const url = b.url.toLowerCase()
                        return url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')
                      }).slice(0, 20)

                      const quickPlaylist = {
                        id: `video-playlist-${Date.now()}`,
                        name: `Quick Video Playlist (${videoBookmarks.length} videos)`,
                        type: 'video' as const,
                        items: videoBookmarks.map(video => ({
                          id: video.id,
                          title: video.title,
                          url: video.url,
                          type: 'video' as const,
                          thumbnail: video.favicon
                        }))
                      }

                      handleVideoBuilderPlaylist(quickPlaylist)
                    }}
                    className="action-button primary"
                  >
                    <Play size={20} />
                    Quick Video Playlist (First 20)
                  </button>
                  <button
                    onClick={() => setShowVideoBuilder(false)}
                    className="action-button secondary"
                  >
                    Back to Simple Builder
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Countdown Overlay */}
      {showCountdown && (
        <div className="modal-overlay">
          <div className="countdown-modal">
            <div className="countdown-number">{countdownSeconds}</div>
            <div className="countdown-text">Next video starting in...</div>
            <div className="countdown-info">
              Video {currentVideoIndex + 2} of {createdPlaylist?.items?.length || 0}
            </div>
            <button onClick={cancelCountdown} className="countdown-cancel">
              Cancel Auto-Advance
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default MultimediaPlaylistPanelNewOptimized