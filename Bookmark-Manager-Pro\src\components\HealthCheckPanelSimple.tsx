import { CheckCircle, X } from 'lucide-react'
import React from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'

interface HealthCheckPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const HealthCheckPanelSimple: React.FC<HealthCheckPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks = [] } = useBookmarks()
  const { t } = useLocalization()

  if (!isOpen) return null

  console.log('HealthCheckPanelSimple rendering:', { 
    isOpen, 
    bookmarksCount: bookmarks.length
  })

  return (
    <div className="import-panel organization-panel bookmark-health-checker">
      <div className="import-header">
        <h2 className="import-title">
          <CheckCircle size={20} />
          {t('health.title')}
        </h2>
        <button onClick={onClose} className="close-btn" aria-label="Close health checker">
          <X size={20} />
        </button>
      </div>

      <div className="import-content">
        {/* Debug Section - Highly Visible */}
        <div 
          className="import-section" 
          style={{ 
            background: 'red', 
            color: 'white', 
            border: '3px solid yellow', 
            padding: '20px', 
            margin: '20px 0',
            fontSize: '18px',
            fontWeight: 'bold'
          }}
        >
          <h3 style={{ color: 'yellow', fontSize: '24px' }}>
            🚨 HEALTH CHECK PANEL IS WORKING! 🚨
          </h3>
          <p style={{ color: 'white', fontSize: '16px' }}>
            Total Bookmarks: {bookmarks.length}
          </p>
          <p style={{ color: 'white', fontSize: '16px' }}>
            Panel Status: VISIBLE AND FUNCTIONAL
          </p>
        </div>

        {/* Simple Controls Section */}
        <div className="import-section">
          <h3 className="section-title">
            🔧 Health Check Controls
          </h3>
          <p className="section-description">
            This is a simplified version of the Health Check panel for debugging.
          </p>

          <div className="format-option">
            <CheckCircle size={20} />
            <div>
              <span>Health Check Ready</span>
              <small>Found {bookmarks.length} bookmarks to analyze</small>
            </div>
          </div>
        </div>

        {/* Bookmark List */}
        <div className="import-section">
          <h3 className="section-title">
            📋 Bookmark List
          </h3>
          <p className="section-description">
            Showing first 5 bookmarks for testing:
          </p>

          <div style={{ maxHeight: '200px', overflow: 'auto' }}>
            {bookmarks.slice(0, 5).map((bookmark, index) => (
              <div key={bookmark.id} className="format-option" style={{ marginBottom: '8px' }}>
                <CheckCircle size={16} />
                <div>
                  <span>{bookmark.title || 'Untitled'}</span>
                  <small>{bookmark.url}</small>
                </div>
              </div>
            ))}
            {bookmarks.length === 0 && (
              <div className="format-option">
                <span>No bookmarks found</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
