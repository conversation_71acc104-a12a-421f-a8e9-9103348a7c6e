import { CheckCircle, X } from 'lucide-react'
import React from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/health-debug.css'

interface HealthCheckPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const HealthCheckPanelSimple: React.FC<HealthCheckPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks = [] } = useBookmarks()
  const { t } = useLocalization()

  // Always log, even if not open
  console.log('HealthCheckPanelSimple called:', {
    isOpen,
    bookmarksCount: bookmarks.length,
    timestamp: new Date().toISOString()
  })

  if (!isOpen) {
    console.log('HealthCheckPanelSimple: isOpen is false, returning null')
    // Temporary: render a small indicator even when closed for debugging
    return (
      <div
        className="health-debug-indicator-closed"
        style={{
          position: 'fixed !important',
          top: '10px !important',
          right: '10px !important',
          background: '#ff0000 !important',
          color: '#ffffff !important',
          padding: '10px !important',
          zIndex: '999999 !important',
          fontSize: '14px !important',
          fontWeight: 'bold !important',
          border: '2px solid #ffff00 !important',
          borderRadius: '4px !important',
          boxShadow: '0 0 10px rgba(0,0,0,0.5) !important',
          fontFamily: 'Arial, sans-serif !important',
          textAlign: 'center !important',
          minWidth: '120px !important',
          // Override any theme-specific positioning or visibility
          display: 'block !important',
          visibility: 'visible !important',
          opacity: '1 !important',
          transform: 'none !important',
          left: 'auto !important',
          bottom: 'auto !important'
        }}
      >
        Health Panel: CLOSED
      </div>
    )
  }

  console.log('HealthCheckPanelSimple: About to render panel')

  return (
    <div
      className="import-panel organization-panel bookmark-health-checker health-debug-panel-open"
      style={{
        // Force positioning and visibility for both theme modes
        position: 'fixed !important',
        top: '0 !important',
        right: '0 !important',
        width: '400px !important',
        height: '100vh !important',
        zIndex: '999999 !important',
        // Force colors and appearance
        background: '#ffffff !important',
        color: '#000000 !important',
        border: '5px solid #ff0000 !important',
        borderLeft: '5px solid #ff0000 !important',
        // Override any theme-specific styles
        display: 'flex !important',
        flexDirection: 'column !important',
        visibility: 'visible !important',
        opacity: '1 !important',
        transform: 'none !important',
        left: 'auto !important',
        bottom: 'auto !important',
        // Remove any backdrop filters that might cause issues
        backdropFilter: 'none !important',
        WebkitBackdropFilter: 'none !important',
        // Ensure proper box model
        boxSizing: 'border-box !important',
        overflow: 'hidden !important'
      }}
    >
      <div
        className="import-header health-debug-header"
        style={{
          // Force header appearance for both theme modes
          background: '#f0f0f0 !important',
          color: '#000000 !important',
          borderBottom: '3px solid #ff0000 !important',
          // Override positioning and layout
          display: 'flex !important',
          alignItems: 'center !important',
          justifyContent: 'space-between !important',
          padding: '16px 20px !important',
          minHeight: '60px !important',
          flexShrink: '0 !important',
          // Override any theme-specific styles
          visibility: 'visible !important',
          opacity: '1 !important',
          transform: 'none !important',
          // Remove backdrop filters
          backdropFilter: 'none !important',
          WebkitBackdropFilter: 'none !important',
          // Ensure proper box model
          boxSizing: 'border-box !important',
          width: '100% !important'
        }}
      >
        <h2
          className="import-title"
          style={{
            color: '#000000 !important',
            fontSize: '18px !important'
          }}
        >
          <CheckCircle size={20} />
          {t('health.title')}
        </h2>
        <button
          onClick={onClose}
          className="close-btn"
          aria-label="Close health checker"
          style={{
            background: '#ff0000 !important',
            color: '#ffffff !important',
            border: '1px solid #000000 !important'
          }}
        >
          <X size={20} />
        </button>
      </div>

      <div
        className="import-content"
        style={{
          background: '#ffffff !important',
          color: '#000000 !important',
          padding: '20px !important'
        }}
      >
        {/* Debug Section - Highly Visible */}
        <div
          className="import-section"
          style={{
            background: '#ff0000 !important',
            color: '#ffffff !important',
            border: '5px solid #ffff00 !important',
            padding: '20px !important',
            margin: '20px 0 !important',
            fontSize: '18px !important',
            fontWeight: 'bold !important',
            zIndex: 10000
          }}
        >
          <h3 style={{ color: 'yellow', fontSize: '24px' }}>
            🚨 HEALTH CHECK PANEL IS WORKING! 🚨
          </h3>
          <p style={{ color: 'white', fontSize: '16px' }}>
            Total Bookmarks: {bookmarks.length}
          </p>
          <p style={{ color: 'white', fontSize: '16px' }}>
            Panel Status: VISIBLE AND FUNCTIONAL
          </p>
        </div>

        {/* Simple Controls Section */}
        <div
          className="import-section"
          style={{
            background: '#f8f8f8 !important',
            color: '#000000 !important',
            border: '2px solid #0000ff !important',
            padding: '15px !important',
            margin: '10px 0 !important'
          }}
        >
          <h3
            className="section-title"
            style={{
              color: '#000000 !important',
              fontSize: '16px !important',
              fontWeight: 'bold !important'
            }}
          >
            🔧 Health Check Controls
          </h3>
          <p
            className="section-description"
            style={{
              color: '#333333 !important',
              fontSize: '14px !important'
            }}
          >
            This is a simplified version of the Health Check panel for debugging.
          </p>

          <div
            className="format-option"
            style={{
              background: '#e0e0e0 !important',
              color: '#000000 !important',
              border: '1px solid #666666 !important',
              padding: '10px !important',
              margin: '10px 0 !important'
            }}
          >
            <CheckCircle size={20} />
            <div>
              <span style={{ color: '#000000 !important' }}>Health Check Ready</span>
              <small style={{ color: '#666666 !important' }}>Found {bookmarks.length} bookmarks to analyze</small>
            </div>
          </div>
        </div>

        {/* Bookmark List */}
        <div className="import-section">
          <h3 className="section-title">
            📋 Bookmark List
          </h3>
          <p className="section-description">
            Showing first 5 bookmarks for testing:
          </p>

          <div style={{ maxHeight: '200px', overflow: 'auto' }}>
            {bookmarks.slice(0, 5).map((bookmark, index) => (
              <div key={bookmark.id} className="format-option" style={{ marginBottom: '8px' }}>
                <CheckCircle size={16} />
                <div>
                  <span>{bookmark.title || 'Untitled'}</span>
                  <small>{bookmark.url}</small>
                </div>
              </div>
            ))}
            {bookmarks.length === 0 && (
              <div className="format-option">
                <span>No bookmarks found</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
