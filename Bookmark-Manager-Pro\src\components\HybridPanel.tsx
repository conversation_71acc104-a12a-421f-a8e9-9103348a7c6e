import { AlertCircle, Brain, CheckCircle, Eye, FileText, Globe, Layers, RefreshCw, Settings, X, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useTheme } from '../contexts/ThemeContext'

interface OrganizeState {
  isProcessing: boolean
  progress: number
  completed: number
  total: number
  currentBookmark?: string
  errors: string[]
}

interface PreviewResult {
  foldersToCreate: string[]
  bookmarksToMove: { id: string; currentFolder: string; newFolder: string; title: string }[]
  summary: string
}

interface OrganizationResult {
  success: boolean
  summary: string
  foldersCreated: string[]
  bookmarksMoved: number
  // Enhanced result details
  phases: {
    aiAnalysis?: {
      processed: number
      categorized: number
      failed: number
      newCategories: string[]
    }
    domainAnalysis?: {
      processed: number
      categorized: number
      platformsRecognized: string[]
    }
    contentAnalysis?: {
      processed: number
      categorized: number
      patternsFound: string[]
    }
    optimization?: {
      collectionsOptimized: number
      bookmarksMerged: number
      finalCollections: string[]
    }
  }
  bookmarkChanges: {
    id: string
    title: string
    url: string
    oldCollection: string
    newCollection: string
    reason: string
    phase: 'ai' | 'domain' | 'content' | 'optimization'
  }[]
  errors: string[]
  processingTime: number
}

interface HybridPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const HybridPanel: React.FC<HybridPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, autoOrganizeBookmarks, previewAutoOrganize } = useBookmarks()
  const { currentTheme } = useTheme()

  // Check if there are existing collections
  const existingCollections = [...new Set(bookmarks
    .map(b => b.collection || b.folder)
    .filter(c => c && c.trim() !== '' && c !== 'Quick Add')
  )]
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [useAI, setUseAI] = useState(true)
  const [state, setState] = useState<OrganizeState>({
    isProcessing: false,
    progress: 0,
    completed: 0,
    total: 0,
    errors: []
  })
  const [showPreview, setShowPreview] = useState(false)
  const [preview, setPreview] = useState<PreviewResult | null>(null)
  const [result, setResult] = useState<OrganizationResult | null>(null)
  const [activeButton, setActiveButton] = useState<string | null>(null)

  const generatePreview = async () => {
    setState(prev => ({ ...prev, isProcessing: true, errors: [] }))
    setShowPreview(true)

    try {
      const previewResult = await previewAutoOrganize({
        strategy: 'hybrid',
        preserveExistingFolders,
        useAI
      })
      setPreview(previewResult)
    } catch (error) {
      setState(prev => ({
        ...prev,
        errors: ['Failed to generate preview. Please try again.']
      }))
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }))
    }
  }

  const handleOrganize = async () => {
    setState(prev => ({
      ...prev,
      isProcessing: true,
      total: bookmarks.length,
      completed: 0,
      progress: 0,
      errors: []
    }))

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setState(prev => {
          const newCompleted = Math.min(prev.completed + Math.random() * 10, prev.total)
          return {
            ...prev,
            completed: newCompleted,
            progress: (newCompleted / prev.total) * 100,
            currentBookmark: bookmarks[Math.floor(newCompleted)]?.title
          }
        })
      }, 200)

      const organizeResult = await autoOrganizeBookmarks({
        strategy: 'hybrid',
        preserveExistingFolders,
        useAI
      })

      clearInterval(progressInterval)
      setResult(organizeResult)
      setShowPreview(false)
      setState(prev => ({
        ...prev,
        completed: prev.total,
        progress: 100,
        currentBookmark: undefined
      }))

      // Auto-close panel after successful completion
      if (organizeResult.success) {
        setTimeout(() => {
          console.log('🧠 HYBRID PANEL: Auto-closing after successful completion');
          onClose();
        }, 3000); // Close after 3 seconds to let user see the results
      }
    } catch (error: any) {
      console.error('Organize error:', error)

      // Handle specific error for preserve folders with no collections
      if (error.message?.includes('PRESERVE_FOLDERS_NO_COLLECTIONS')) {
        setResult({
          success: false,
          summary: 'No existing collections found to preserve. Please disable "preserve existing folder structure" to create new collections, or import bookmarks with existing folder structures first.',
          foldersCreated: [],
          bookmarksMoved: 0,
          phases: {},
          bookmarkChanges: [],
          errors: ['No existing collections found. Disable "preserve existing folder structure" to proceed.'],
          processingTime: 0
        })
        setState(prev => ({
          ...prev,
          errors: ['No existing collections found. Disable "preserve existing folder structure" to proceed.']
        }))
      } else {
        setResult({
          success: false,
          summary: 'An error occurred while organizing bookmarks',
          foldersCreated: [],
          bookmarksMoved: 0,
          phases: {},
          bookmarkChanges: [],
          errors: ['Organization failed. Please try again.'],
          processingTime: 0
        })
        setState(prev => ({
          ...prev,
          errors: ['Organization failed. Please try again.']
        }))
      }
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }))
    }
  }

  // Remove theme conditionals - always use modern design

  if (!isOpen) return null

  return (
    <div className="import-panel organization-panel">
      <div className="import-header">
        <h2 className="import-title">🔄 Hybrid Organization</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close hybrid organization panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{
        maxHeight: 'calc(85vh - 120px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollBehavior: 'smooth'
      }}>
        {/* Strategy Description */}
        <div className="import-section">
          <h3 className="section-title">
            <Layers size={16} />
            Hybrid Strategy
          </h3>
          <p className="section-description">
            Combines AI analysis, domain grouping, and content analysis for the best organization results.
            This approach first analyzes content patterns, then organizes remaining bookmarks by domain.
          </p>

          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>AI-powered content analysis</span>
              <small>Intelligent categorization based on content patterns</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Domain-based grouping</span>
              <small>Organize by website domains and sources</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Smart folder creation</span>
              <small>Automatically create logical folder structures</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Duplicate detection</span>
              <small>Identify and handle duplicate bookmarks</small>
            </div>
          </div>
        </div>

        {/* Configuration Options */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            Configuration Options
          </h3>

          <div className="format-options">
            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
                style={{ marginRight: '8px' }}
                disabled={existingCollections.length === 0}
              />
              <div>
                <span>Preserve existing folder structure</span>
                <small>Keep bookmarks in their current folders and only organize uncategorized ones</small>
              </div>
            </label>

            {preserveExistingFolders && existingCollections.length === 0 && (
              <div className="upload-area" style={{
                cursor: 'default',
                border: '2px solid var(--warning-color)',
                backgroundColor: 'rgba(251, 191, 36, 0.05)',
                marginTop: '8px'
              }}>
                <AlertCircle size={24} className="text-yellow-500" />
                <p className="upload-text" style={{ color: 'var(--warning-color)' }}>
                  No Existing Collections Found
                </p>
                <p className="upload-hint">
                  You have {bookmarks.length} bookmarks but no existing collections to preserve.
                  Import bookmarks with folder structures first, or disable this option to create new collections.
                </p>
              </div>
            )}

            {existingCollections.length > 0 && (
              <div className="format-options" style={{ marginTop: '8px' }}>
                <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                  <CheckCircle size={20} className="text-white" />
                  <div>
                    <span>Found {existingCollections.length} existing collections</span>
                    <small>{existingCollections.slice(0, 3).join(', ')}{existingCollections.length > 3 ? ` +${existingCollections.length - 3} more` : ''}</small>
                  </div>
                </div>
              </div>
            )}

            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={useAI}
                onChange={(e) => setUseAI(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Use AI for intelligent categorization</span>
                <small>Enable AI-powered analysis for smarter content-based organization</small>
              </div>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="import-section">
          <h3 className="section-title">Organization Actions</h3>
          <div className="format-options">
            <button
              onClick={() => {
                if (!state.isProcessing) {
                  setActiveButton('preview');
                  setTimeout(() => setActiveButton(null), 200);
                  generatePreview();
                }
              }}
              className={`format-option ${activeButton === 'preview' ? 'active' : ''}`}
              disabled={state.isProcessing}
            >
              <Eye size={20} />
              <div>
                <span>Preview Organization</span>
                <small>See what changes will be made before organizing</small>
              </div>
            </button>

            <button
              onClick={() => {
                if (!state.isProcessing && bookmarks.length > 0) {
                  setActiveButton('organize');
                  setTimeout(() => setActiveButton(null), 200);
                  handleOrganize();
                }
              }}
              className={`format-option active ${activeButton === 'organize' ? 'active' : ''}`}
              disabled={state.isProcessing || bookmarks.length === 0}
            >
              {state.isProcessing ? (
                <>
                  <RefreshCw size={20} className="animate-spin" />
                  <div>
                    <span>Organizing... ({state.completed}/{state.total})</span>
                    <small>Processing bookmarks, please wait...</small>
                  </div>
                </>
              ) : (
                <>
                  <Zap size={20} />
                  <div>
                    <span>Organize Bookmarks</span>
                    <small>Start hybrid organization of {bookmarks.length} bookmarks</small>
                  </div>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Progress */}
        {state.isProcessing && (
          <div className="import-section">
            <h3 className="section-title">Organization Progress</h3>
            <div className="upload-area processing">
              <RefreshCw size={32} className="animate-spin processing-icon" />
              <p className="upload-text">
                Processing your bookmarks...
              </p>
              <p className="upload-hint">
                {state.completed} of {state.total} processed ({Math.round(state.progress)}%)
              </p>
              {state.currentBookmark && (
                <p className="upload-hint" style={{ fontSize: '12px', opacity: 0.8 }}>
                  Current: {state.currentBookmark}
                </p>
              )}
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${state.progress}%` }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Preview Results */}
        {showPreview && preview && (
          <div className="import-section">
            <h3 className="section-title">Preview Results</h3>
            <div className="upload-area" style={{ cursor: 'default', border: '2px solid var(--accent-color)' }}>
              <Eye size={32} className="text-white" />
              <p className="upload-text">
                Organization Preview
              </p>
              <p className="upload-hint">
                {preview.summary}
              </p>
            </div>

            {preview.foldersToCreate.length > 0 && (
              <div className="format-options" style={{ marginTop: '16px' }}>
                <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                  <Settings size={20} />
                  <div>
                    <span>Folders to Create ({preview.foldersToCreate.length})</span>
                    <small>{preview.foldersToCreate.slice(0, 3).join(', ')}{preview.foldersToCreate.length > 3 ? ` +${preview.foldersToCreate.length - 3} more` : ''}</small>
                  </div>
                </div>
              </div>
            )}

            {preview.bookmarksToMove.length > 0 && (
              <div className="format-options" style={{ marginTop: '8px' }}>
                <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                  <Zap size={20} />
                  <div>
                    <span>Bookmarks to Move ({preview.bookmarksToMove.length})</span>
                    <small>
                      {preview.bookmarksToMove.slice(0, 2).map(move => `${move.title} → ${move.newFolder}`).join(', ')}
                      {preview.bookmarksToMove.length > 2 ? ` +${preview.bookmarksToMove.length - 2} more` : ''}
                    </small>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Compact Organization Results */}
        {result && (
          <div className="import-section">
            <h3 className="section-title">
              {result.success ? (
                <CheckCircle size={16} className="text-white" />
              ) : (
                <AlertCircle size={16} className="text-red-500" />
              )}
              Organization Results
            </h3>

            {/* Compact Summary */}
            <div className="upload-area" style={{
              cursor: 'default',
              border: result.success ? '2px solid var(--success-color)' : '2px solid var(--error-color)',
              backgroundColor: result.success ? 'rgba(34, 197, 94, 0.05)' : 'rgba(239, 68, 68, 0.05)',
              padding: '16px'
            }}>
              {result.success ? (
                <CheckCircle size={24} className="text-white" />
              ) : (
                <AlertCircle size={24} className="text-red-500" />
              )}
              <p className="upload-text" style={{ fontSize: '16px', margin: '8px 0' }}>
                {result.success ? 'Hybrid Organization Complete!' : 'Organization Failed'}
              </p>

              {result.success && (
                <div className="format-options" style={{ marginTop: '12px', gap: '6px' }}>
                  <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 10px', fontSize: '12px' }}>
                    <span style={{ fontWeight: 'bold', color: 'var(--success-color)' }}>{result.foldersCreated?.length || 0}</span>
                    <small>Collections</small>
                  </div>
                  <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 10px', fontSize: '12px' }}>
                    <span style={{ fontWeight: 'bold', color: 'var(--accent-color)' }}>{result.bookmarksMoved}</span>
                    <small>Organized</small>
                  </div>
                  <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 10px', fontSize: '12px' }}>
                    <span style={{ fontWeight: 'bold', color: 'var(--warning-color)' }}>{result.bookmarkChanges?.length || 0}</span>
                    <small>Changes</small>
                  </div>
                  {result.processingTime > 0 && (
                    <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 10px', fontSize: '12px' }}>
                      <span style={{ fontWeight: 'bold', color: 'var(--info-color)' }}>{(result.processingTime / 1000).toFixed(1)}s</span>
                      <small>Time</small>
                    </div>
                  )}
                </div>
              )}

              <p className="upload-hint" style={{ fontSize: '12px', margin: '8px 0 0 0' }}>
                {result.summary}
              </p>
            </div>

            {/* Quick Phase Summary */}
            {result.success && result.phases && (
              <div className="format-options" style={{ marginTop: '12px' }}>
                <h4 style={{ margin: '0 0 8px 0', color: 'var(--text-color)', fontSize: '14px' }}>Processing Summary</h4>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '6px', fontSize: '11px' }}>
                  {result.phases.aiAnalysis && (
                    <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 8px' }}>
                      <Brain size={14} className="text-white" />
                      <div>
                        <span style={{ fontSize: '11px' }}>AI Analysis</span>
                        <small>{result.phases.aiAnalysis.categorized}/{result.phases.aiAnalysis.processed}</small>
                      </div>
                    </div>
                  )}

                  {result.phases.domainAnalysis && (
                    <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 8px' }}>
                      <Globe size={14} className="text-white" />
                      <div>
                        <span style={{ fontSize: '11px' }}>Domain Intel</span>
                        <small>{result.phases.domainAnalysis.categorized} categorized</small>
                      </div>
                    </div>
                  )}

                  {result.phases.contentAnalysis && (
                    <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 8px' }}>
                      <FileText size={14} className="text-white" />
                      <div>
                        <span style={{ fontSize: '11px' }}>Content Analysis</span>
                        <small>{result.phases.contentAnalysis.categorized} categorized</small>
                      </div>
                    </div>
                  )}

                  {result.phases.optimization && (
                    <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '6px 8px' }}>
                      <Zap size={14} className="text-white" />
                      <div>
                        <span style={{ fontSize: '11px' }}>Optimization</span>
                        <small>{result.phases.optimization.finalCollections?.length || 0} final</small>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Collections Created */}
            {result.success && result.foldersCreated && result.foldersCreated.length > 0 && (
              <div className="format-options" style={{ marginTop: '12px' }}>
                <h4 style={{ margin: '0 0 8px 0', color: 'var(--text-color)', fontSize: '14px' }}>
                  Collections Created ({result.foldersCreated.length})
                </h4>
                <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)', padding: '8px 12px' }}>
                  <div style={{ fontSize: '12px', lineHeight: '1.4' }}>
                    {result.foldersCreated.slice(0, 6).join(', ')}
                    {result.foldersCreated.length > 6 && ` +${result.foldersCreated.length - 6} more`}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {result.success && (
              <div className="format-options" style={{ marginTop: '12px' }}>
                <button
                  onClick={() => {
                    setResult(null)
                    setShowPreview(false)
                    setState({
                      isProcessing: false,
                      progress: 0,
                      completed: 0,
                      total: 0,
                      errors: []
                    })
                  }}
                  className="format-option"
                  style={{ padding: '8px 12px' }}
                >
                  <RefreshCw size={16} />
                  <div>
                    <span style={{ fontSize: '14px' }}>Organize Again</span>
                    <small>Run with different settings</small>
                  </div>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Errors */}
        {state.errors.length > 0 && (
          <div className="import-section">
            <h3 className="section-title">
              <AlertCircle size={16} />
              Errors ({state.errors.length})
            </h3>
            <div className="upload-area" style={{
              cursor: 'default',
              border: '2px solid var(--error-color)',
              backgroundColor: 'rgba(239, 68, 68, 0.05)'
            }}>
              <AlertCircle size={32} className="text-red-500" />
              <p className="upload-text">Organization Errors</p>
              <div className="format-options" style={{ marginTop: '16px' }}>
                {state.errors.map((error, index) => (
                  <div key={index} className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                    <AlertCircle size={16} className="text-red-500" />
                    <div>
                      <span>Error {index + 1}</span>
                      <small>{error}</small>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="import-section">
          <h3 className="section-title">Current Statistics</h3>
          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <span style={{ fontWeight: 'bold', color: 'var(--accent-color)', fontSize: '18px' }}>{bookmarks.length}</span>
              <div>
                <span>Total Bookmarks</span>
                <small>All bookmarks in your collection</small>
              </div>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <span style={{ fontWeight: 'bold', color: 'var(--success-color)', fontSize: '18px' }}>{bookmarks.filter(b => b.collection || b.folder).length}</span>
              <div>
                <span>Organized</span>
                <small>Bookmarks already in folders</small>
              </div>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <span style={{ fontWeight: 'bold', color: 'var(--warning-color)', fontSize: '18px' }}>{bookmarks.filter(b => !b.collection && !b.folder).length}</span>
              <div>
                <span>Unorganized</span>
                <small>Bookmarks ready for organization</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
