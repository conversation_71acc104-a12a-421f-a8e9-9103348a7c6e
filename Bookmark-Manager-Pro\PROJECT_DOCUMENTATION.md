# Bookmark Studio - Project Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Quick Start Guide](#quick-start-guide)
3. [Core Development Rules](#core-development-rules)
4. [Emergency Protocols](#emergency-protocols)
5. [Build System](#build-system)
6. [Testing Strategy](#testing-strategy)
7. [Code Quality Standards](#code-quality-standards)
8. [Project Structure](#project-structure)
9. [Development Workflow](#development-workflow)
10. [Troubleshooting Guide](#troubleshooting-guide)

## Project Overview

Bookmark Studio is a modern React/TypeScript application for advanced bookmark management with AI-powered features using Google's Gemini API.

### Technology Stack
- **Frontend**: React 19.1.0, TypeScript
- **Build Tool**: Vite (with esbuild)
- **Testing**: Vitest, Cypress, Testing Library
- **AI Integration**: Google Gemini API
- **State Management**: React Query, Context API
- **Styling**: CSS Modules, Design System

## Quick Start Guide

### Prerequisites
- Node.js (latest LTS)
- npm or yarn
- Google Gemini API key

#### Node.js Installation (If Required)

**For Linux/Unix Systems (Ubuntu/Debian)**

If you encounter PATH issues or need to install Node.js:

1. **Fix PATH Issues**
   ```bash
   export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
   ```

2. **Verify sudo works**
   ```bash
   sudo --version
   ```

3. **Install Node.js via snap (recommended)**
   ```bash
   sudo snap install node --channel=20/stable --classic
   ```

4. **Verify installation**
   ```bash
   node --version
   npm --version
   ```

5. **Make PATH permanent**
   ```bash
   # Add to bashrc for permanent fix
   echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```

**For Windows/macOS**
- Download from [nodejs.org](https://nodejs.org/) (LTS version recommended)
- Follow the installer instructions

### Installation
```bash
# 1. Install dependencies
npm install

# 2. Set up environment
# Create .env.local and add:
# GEMINI_API_KEY=your_api_key_here

# 3. Start development server
npm run dev
```

### Available Scripts
```bash
# Development
npm run dev              # Start dev server
npm run build            # Production build
npm run build:check      # Development build for checking
npm run preview          # Preview production build

# Testing
npm run test             # Unit tests with Vitest
npm run test:watch       # Watch mode
npm run test:coverage    # Coverage report
npm run test:e2e         # End-to-end tests
npm run test:component   # Component tests
npm run test:all         # All tests

# Quality
npm run lint             # ESLint check
npm run type-check       # TypeScript info (disabled)
```

## Core Development Rules

### 🚫 CRITICAL: NO TSC RULE
**NEVER use `tsc` command directly for building or type checking in this project.**

#### Why This Rule Exists
- Performance: esbuild (via Vite) is 10-100x faster than tsc
- Reliability: Fewer build failures and hanging processes
- Modern Workflow: Aligned with current frontend best practices
- Unified Tooling: Single tool (Vite) for all build needs

#### Alternatives to TSC
- **Type Checking**: VS Code TypeScript Language Server
- **Building**: Vite with esbuild
- **Linting**: ESLint with TypeScript rules
- **Testing**: Vitest (handles TypeScript natively)

### ⏱️ 20-Second Timeout Rule
**If any build command shows no activity for 20 seconds, immediately stop and escalate.**

#### Timeout Detection Signs
- No output for 20+ seconds
- CPU usage drops to near zero
- No file changes or progress indicators
- Terminal appears frozen

## Emergency Protocols

### Emergency Response Protocol
1. **STOP** hanging process (Ctrl+C)
2. **IMMEDIATELY** proceed to Tier 1 diagnostics
3. **DO NOT** wait longer than 20 seconds for any command
4. **ESCALATE** through tiers if issues persist
5. **DOCUMENT** which tier resolved the issue

### Tier-Based Debugging

#### Tier 1: Quick Diagnostics (0-30 seconds)
```bash
npm run lint         # Check syntax errors
npm run build:check  # Development build (faster)
npm run dev          # Real-time error detection
```

#### Tier 2: Cache and Dependencies (30-60 seconds)
```bash
# Clear Vite cache
rmdir /s /q node_modules\.vite

# Full clean install
rmdir /s /q node_modules
npm install

# Retry build
npm run build
```

#### Tier 3: Advanced Debugging (60-90 seconds)
```bash
# Verbose build with debug info
npm run build -- --debug
npm run build -- --logLevel info

# Memory analysis
npm run build:check
```

#### Tier 4: Component-Level Testing (90-120 seconds)
```bash
# Run tests to isolate issues
npm run test
npm run test:component

# Check individual files in VS Code for red squiggles
```

#### Tier 5: Manual Review (120+ seconds)
1. Check recent Git changes
2. Verify import paths
3. Check type definitions
4. Review dependencies
5. Restart IDE and TypeScript language server

## Build System

### Vite Configuration
- **Compiler**: esbuild (not tsc)
- **Hot Reload**: Instant TypeScript compilation
- **Source Maps**: Enabled for debugging
- **Optimization**: Tree shaking, code splitting

### Build Modes
- **Development**: `npm run dev` - Fast builds, source maps
- **Development Check**: `npm run build:check` - Verify without optimization
- **Production**: `npm run build` - Optimized, minified

### Type Safety Without TSC
- **IDE Integration**: VS Code TypeScript Language Server
- **ESLint Rules**: Type-aware linting
- **Build-time Errors**: Vite/esbuild catches type errors
- **Hot Reload**: Instant feedback during development

## Testing Strategy

### Test Types
1. **Unit Tests**: Vitest for components and utilities
2. **Integration Tests**: Testing Library for user interactions
3. **Component Tests**: Cypress for isolated component testing
4. **E2E Tests**: Cypress for full user workflows
5. **Visual Tests**: Cypress for UI regression testing

### Test Structure
```
src/test/
├── components/          # Component tests
├── fixtures/           # Test data
├── mocks/             # Mock implementations
├── setup.ts           # Test configuration
└── utils.tsx          # Test utilities
```

### Coverage Requirements
- **Minimum**: 80% line coverage
- **Components**: 90% coverage for critical components
- **Services**: 95% coverage for business logic
- **Utilities**: 100% coverage for pure functions

## Code Quality Standards

### TypeScript Guidelines
- **Strict Mode**: Enabled in tsconfig.json
- **No Any**: Avoid `any` type, use proper typing
- **Interfaces**: Prefer interfaces over types for objects
- **Enums**: Use const assertions instead of enums

### React Guidelines
- **Functional Components**: Use function components with hooks
- **Props Interface**: Define props interface for each component
- **Error Boundaries**: Wrap components that might fail
- **Memoization**: Use React.memo for expensive components

### Import/Export Rules
- **Absolute Imports**: Use absolute paths from src/
- **Index Files**: Use index.ts for clean imports
- **Named Exports**: Prefer named exports over default
- **Barrel Exports**: Group related exports in index files

### File Naming Conventions
- **Components**: PascalCase (e.g., `BookmarkItem.tsx`)
- **Utilities**: camelCase (e.g., `exportUtils.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)
- **Types**: PascalCase with .types.ts suffix

## Project Structure

```
Bookmark-Manager-Pro/
├── src/
│   ├── components/          # Reusable UI components
│   ├── contexts/           # React contexts
│   ├── styles/            # Global styles
│   ├── test/              # Test utilities and setup
│   └── types/             # TypeScript type definitions
├── components/             # Legacy component location
├── services/              # Business logic and API calls
├── utils/                 # Utility functions
├── styles/               # Global stylesheets
├── docs/                 # Documentation
├── cypress/              # E2E and component tests
├── coverage/             # Test coverage reports
└── dist/                 # Build output
```

### Component Organization
- **Atomic Design**: Atoms, molecules, organisms pattern
- **Feature-based**: Group related components
- **Shared Components**: Reusable across features
- **Page Components**: Top-level route components

## Development Workflow

### Git Workflow
1. **Feature Branches**: Create from main
2. **Commit Messages**: Conventional commits format
3. **Pre-commit Hooks**: Lint and test before commit
4. **Pull Requests**: Required for main branch

### Code Review Checklist
- [ ] No `tsc` commands in scripts
- [ ] TypeScript errors resolved in IDE
- [ ] Tests pass and coverage maintained
- [ ] ESLint rules followed
- [ ] Performance considerations addressed
- [ ] Accessibility standards met

### Release Process
1. **Version Bump**: Update package.json version
2. **Build Verification**: `npm run build` succeeds
3. **Test Suite**: All tests pass
4. **Documentation**: Update relevant docs
5. **Deploy**: Production deployment

## Troubleshooting Guide

### Common Issues

#### Build Hangs or Fails
1. **Check for TSC usage**: Ensure no `tsc` commands
2. **Clear caches**: Delete node_modules/.vite
3. **Verify imports**: Check for circular dependencies
4. **IDE restart**: Restart VS Code and TypeScript server

#### Type Errors
1. **IDE first**: Check VS Code for red squiggles
2. **ESLint**: Run `npm run lint`
3. **Build check**: Use `npm run build:check`
4. **Manual review**: Check type definitions

#### Test Failures
1. **Isolation**: Run individual test files
2. **Mocks**: Verify mock implementations
3. **Environment**: Check test environment setup
4. **Dependencies**: Ensure test dependencies installed

#### Performance Issues
1. **Bundle analysis**: Check build output sizes
2. **Component profiling**: Use React DevTools
3. **Memory leaks**: Check for cleanup in useEffect
4. **Lazy loading**: Implement code splitting

### Emergency Contacts
- **Build Issues**: Check Vite documentation
- **TypeScript Issues**: VS Code TypeScript troubleshooting
- **Test Issues**: Vitest and Cypress documentation
- **Performance**: React DevTools and browser profiling

### Useful Commands
```bash
# Clear all caches
rmdir /s /q node_modules\.vite
rmdir /s /q node_modules
npm install

# Debug build
npm run build -- --debug --logLevel info

# Test specific file
npm run test -- BookmarkItem.test.tsx

# Coverage for specific file
npm run test:coverage -- --reporter=text
```

## Success Metrics

### Build Performance
- **Development build**: < 5 seconds
- **Production build**: < 20 seconds
- **Hot reload**: < 1 second
- **Test execution**: < 30 seconds

### Code Quality
- **TypeScript errors**: 0
- **ESLint errors**: 0
- **Test coverage**: > 80%
- **Bundle size**: < 1MB gzipped

### Developer Experience
- **Setup time**: < 5 minutes
- **Build reliability**: > 99%
- **IDE responsiveness**: Real-time error detection
- **Documentation coverage**: 100% of public APIs

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: Development Team

> 🚨 **Remember**: Never use `tsc` directly, always follow the 20-second rule, and escalate through debugging tiers when issues persist!