import { Brain, Download, FileText, Globe, Layers, ListMusic, Play, Shield, SplitSquareVertical, Upload, Video, X } from 'lucide-react'
import React from 'react'
import { ContentPanel } from './ContentPanel'
import { DomainPanel } from './DomainPanel'
import { ExportPanel } from './ExportPanel'
import { HealthCheckPanel } from './HealthCheckPanel'
import { HybridPanel } from './HybridPanel'
import { ImportPanel } from './ImportPanel'
import { MultimediaOrganizationPanel } from './MultimediaOrganizationPanel'
import { MultimediaPlaybackPanel } from './MultimediaPlaybackPanel'
import { PlaylistPanel } from './PlaylistPanel'
import { SmartAIPanel } from './SmartAIPanel'
import { SplitPanel } from './SplitPanel'
import { SummaryPanel } from './SummaryPanel'

export type PanelType = 'health-check' | 'summaries' | 'hybrid' | 'smart-ai' | 'domain' | 'content' | 'import' | 'export' | 'split' | 'playlist' | 'multimedia' | 'multimedia-player'

interface Tab {
  id: PanelType
  label: string
  icon: React.ComponentType<{ size?: number }>
  component: React.ComponentType<{ isOpen: boolean; onClose: () => void }>
}

interface TabbedRightPanelProps {
  activePanels: PanelType[]
  activeTab: PanelType | null
  onTabChange: (tab: PanelType) => void
  onCloseTab: (tab: PanelType) => void
  onCloseAll: () => void
  onOpenPanel?: (panelType: PanelType) => void
}



const tabs: Tab[] = [
  {
    id: 'health-check',
    label: 'Health Check',
    icon: Shield,
    component: HealthCheckPanel
  },
  {
    id: 'summaries',
    label: 'Summaries',
    icon: FileText,
    component: SummaryPanel
  },
  {
    id: 'hybrid',
    label: 'Hybrid',
    icon: Layers,
    component: HybridPanel
  },
  {
    id: 'smart-ai',
    label: 'Smart AI',
    icon: Brain,
    component: SmartAIPanel
  },
  {
    id: 'domain',
    label: 'Domain',
    icon: Globe,
    component: DomainPanel
  },
  {
    id: 'content',
    label: 'Content',
    icon: FileText,
    component: ContentPanel
  },
  {
    id: 'import',
    label: 'Import',
    icon: Upload,
    component: ImportPanel
  },
  {
    id: 'export',
    label: 'Export',
    icon: Download,
    component: ExportPanel
  },
  {
    id: 'split',
    label: 'Split',
    icon: SplitSquareVertical,
    component: SplitPanel
  },
  {
    id: 'playlist',
    label: 'Playlist',
    icon: ListMusic,
    component: PlaylistPanel
  },
  {
    id: 'multimedia',
    label: 'Multimedia',
    icon: Video,
    component: MultimediaOrganizationPanel
  },
  {
    id: 'multimedia-player',
    label: 'Player',
    icon: Play,
    component: MultimediaPlaybackPanel
  }
]

export const TabbedRightPanel: React.FC<TabbedRightPanelProps> = ({
  activePanels,
  activeTab,
  onTabChange,
  onCloseTab,
  onCloseAll,
  onOpenPanel
}) => {
  if (activePanels.length === 0) return null

  const activeTabData = tabs.find(tab => tab.id === activeTab)
  const ActiveComponent = activeTabData?.component

  return (
    <div className="tabbed-right-panel">
      {/* Tab Header */}
      <div className="tab-header">
        <div className="tab-list">
          {activePanels.map(panelId => {
            const tab = tabs.find(t => t.id === panelId)
            if (!tab) return null

            return (
              <button
                key={panelId}
                className={`tab-button ${activeTab === panelId ? 'active' : ''}`}
                onClick={() => onTabChange(panelId)}
                title={tab.label}
              >
                <tab.icon size={16} />
                <span className="tab-label">{tab.label}</span>
                <button
                  className="tab-close"
                  onClick={(e) => {
                    e.stopPropagation()
                    onCloseTab(panelId)
                  }}
                  title={`Close ${tab.label}`}
                >
                  <X size={12} />
                </button>
              </button>
            )
          })}
        </div>

        {activePanels.length > 1 && (
          <button
            className="close-all-btn"
            onClick={onCloseAll}
            title="Close All Panels"
          >
            <X size={16} />
          </button>
        )}
      </div>

      {/* Panel Content */}
      <div className="panel-content">
        {ActiveComponent && (
          <ActiveComponent
            isOpen={true}
            onClose={() => activeTab && onCloseTab(activeTab)}
            {...(onOpenPanel && { onOpenPanel })}
          />
        )}
      </div>
    </div>
  )
}
