import { Play, X } from 'lucide-react'
import React, { useState } from 'react'

// Mock playlist data for demonstration
const mockPlaylist = {
  id: 'demo-playlist',
  name: 'Demo Multimedia Playlist',
  items: [
    {
      id: '1',
      title: 'Sample Video',
      url: 'https://www.w3schools.com/html/mov_bbb.mp4',
      type: 'video' as const,
      duration: 120,
      thumbnail: ''
    },
    {
      id: '2',
      title: 'Sample Audio',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      type: 'audio' as const,
      duration: 5,
      thumbnail: ''
    },
    {
      id: '3',
      title: 'Sample Article',
      url: 'https://example.com/article',
      type: 'article' as const,
      duration: undefined,
      thumbnail: ''
    }
  ],
  playbackSettings: {
    shuffle: false,
    repeat: 'none' as const,
    autoPlay: false
  }
}

interface MultimediaPlaybackPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const MultimediaPlaybackPanel: React.FC<MultimediaPlaybackPanelProps> = ({
  isOpen,
  onClose
}) => {
  const [selectedPlaylist] = useState(mockPlaylist)
  const [isPlayerOpen, setIsPlayerOpen] = useState(false)

  if (!isOpen) return null

  return (
    <div className="import-panel organization-panel multimedia-playback-panel">
      {/* UNIFIED HEADER DESIGN */}
      <div className="import-header">
        <h2 className="import-title">
          <Play size={20} />
          Multimedia Player
        </h2>
        <button
          onClick={onClose}
          className="close-btn"
          aria-label="Close multimedia player panel"
        >
          <X size={20} />
        </button>
      </div>

      {/* UNIFIED CONTENT DESIGN */}
      <div className="import-content">
        {/* Player Status Section */}
        <div className="import-section">
          <h3 className="section-title">🎵 Player Status</h3>
          <p className="section-description">
            Multimedia playback panel integrated with the tabbed interface system.
          </p>

          <div className="format-option">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-blue-500/20 flex items-center justify-center">
                <Play className="w-6 h-6 text-blue-400" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-white/90">Ready to Play</h4>
                <p className="text-sm text-white/70">
                  {selectedPlaylist.items.length} items in playlist
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Current Playlist Section */}
        <div className="import-section">
          <h3 className="section-title">📋 Current Playlist</h3>
          <p className="section-description">
            {selectedPlaylist.name} - {selectedPlaylist.items.length} items
          </p>

          <div className="space-y-2">
            {selectedPlaylist.items.map((item, index) => (
              <div key={item.id} className="format-option">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded flex items-center justify-center text-xs font-medium bg-white/10 text-gray-300">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="font-medium block truncate text-white/90">
                      {item.title}
                    </span>
                    <small className="block mt-1 text-gray-400">
                      {item.type} • {item.duration ? `${Math.floor(item.duration / 60)}:${(item.duration % 60).toString().padStart(2, '0')}` : 'Unknown duration'}
                    </small>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Player Controls Section */}
        <div className="import-section">
          <h3 className="section-title">🎛️ Player Controls</h3>
          <p className="section-description">
            Launch the multimedia player in fullscreen mode for the best experience.
          </p>

          <button
            onClick={() => setIsPlayerOpen(true)}
            className="format-option w-full flex items-center justify-center gap-2 hover:bg-blue-500/20 hover:border-blue-400"
          >
            <Play className="w-5 h-5" />
            Launch Multimedia Player
          </button>
        </div>

        {/* Integration Info Section */}
        <div className="import-section">
          <h3 className="section-title">ℹ️ Integration Status</h3>
          <p className="section-description">
            This multimedia player is now fully integrated with the tabbed panel system.
          </p>

          <div className="space-y-2">
            <div className="format-option">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <span className="text-white/90">Unified Design System Applied</span>
              </div>
            </div>
            <div className="format-option">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <span className="text-white/90">Tabbed Interface Integration</span>
              </div>
            </div>
            <div className="format-option">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <span className="text-white/90">Cross-Theme Compatibility</span>
              </div>
            </div>
            <div className="format-option">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <span className="text-white/90">Modern Glass Morphism Styling</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fullscreen Player Modal */}
      {isPlayerOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md">
          <div className="import-panel multimedia-modal w-[90vw] h-[90vh] max-w-6xl">
            {/* Modal Header */}
            <div className="import-header">
              <h2 className="import-title">
                <Play size={20} />
                🎵 {selectedPlaylist.name}
              </h2>
              <button
                onClick={() => setIsPlayerOpen(false)}
                className="close-btn"
                aria-label="Close multimedia player"
              >
                <X size={20} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="import-content">
              <div className="import-section">
                <h3 className="section-title">🎬 Media Player</h3>
                <p className="section-description">
                  Multimedia player with unified design system integration.
                </p>

                <div className="relative overflow-hidden rounded-lg border-2 border-white/10 bg-black/80 flex items-center justify-center" style={{ minHeight: '400px' }}>
                  <div className="text-center text-white">
                    <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Play className="w-12 h-12" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">Multimedia Player Ready</h3>
                    <p className="text-white/70 mb-4">
                      The multimedia player is now fully integrated with the unified design system.
                    </p>
                    <p className="text-sm text-white/60">
                      This demonstrates the successful integration of the MultimediaPlaybackModal<br />
                      with the tabbed panel system and unified design system.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
