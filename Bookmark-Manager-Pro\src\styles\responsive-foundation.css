/* ==========================================================================
   RESPONSIVE FOUNDATION CSS
   Enhanced Mobile-First Architecture for 9/10 Mobile Friendliness Score
   ========================================================================== */

/* Performance Optimizations */
* {
  box-sizing: border-box;
}

/* Optimize animations and transitions */
*,
*::before,
*::after {
  will-change: auto;
}

/* Hardware acceleration for better performance */
.enhanced-search-input,
.responsive-header,
[class*="button"],
[class*="input"] {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* CSS Custom Properties (Variables) */
:root {
  /* Mobile-First Breakpoint System */
  --breakpoint-xs: 320px;   /* Small mobile */
  --breakpoint-sm: 480px;   /* Large mobile */
  --breakpoint-md: 640px;   /* Small tablet */
  --breakpoint-lg: 768px;   /* Tablet */
  --breakpoint-xl: 1024px;  /* Desktop */
  --breakpoint-2xl: 1280px; /* Large desktop */
  --breakpoint-3xl: 1440px; /* Extra large desktop */
  --breakpoint-4xl: 1920px; /* Ultra-wide */
  
  /* Responsive Spacing Scale */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  
  /* Touch Target Standards */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
  
  /* Responsive Typography Scale */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  
  /* Container Widths */
  --container-xs: 100%;
  --container-sm: 100%;
  --container-md: 100%;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1440px;
}

/* ==========================================================================
   BASE MOBILE STYLES (320px+)
   ========================================================================== */

/* Container System */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  max-width: var(--container-xs);
}

/* Grid System - Mobile First */
.grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flex Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

/* Spacing Utilities */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }
.gap-2xl { gap: var(--spacing-2xl); }

/* Padding Utilities */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

/* Touch Target Standards */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.touch-target:active {
  transform: scale(0.95);
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable);
  min-width: var(--touch-target-comfortable);
}

.touch-target-large {
  min-height: var(--touch-target-large);
  min-width: var(--touch-target-large);
}

/* Typography - Mobile Optimized */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

/* ==========================================================================
   ENHANCED MOBILE (480px+)
   ========================================================================== */

@media (min-width: 480px) {
  .container {
    padding: 0 var(--spacing-lg);
    max-width: var(--container-sm);
  }
  
  .grid {
    gap: var(--spacing-lg);
  }
  
  /* Enhanced Grid Options */
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  
  /* Enhanced Spacing */
  .sm\:gap-lg { gap: var(--spacing-lg); }
  .sm\:gap-xl { gap: var(--spacing-xl); }
  
  .sm\:px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
  .sm\:py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
  
  /* Typography Scaling */
  .sm\:text-lg { font-size: var(--text-lg); }
  .sm\:text-xl { font-size: var(--text-xl); }
  .sm\:text-2xl { font-size: var(--text-2xl); }
}

/* ==========================================================================
   SMALL TABLET (640px+)
   ========================================================================== */

@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-xl);
    max-width: var(--container-md);
  }
  
  .grid {
    gap: var(--spacing-xl);
  }
  
  /* Enhanced Grid Options */
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  /* Enhanced Spacing */
  .md\:gap-xl { gap: var(--spacing-xl); }
  .md\:gap-2xl { gap: var(--spacing-2xl); }
  
  .md\:px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
  .md\:py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
  
  /* Flex Direction Changes */
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
}

/* ==========================================================================
   TABLET (768px+)
   ========================================================================== */

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-2xl);
    max-width: var(--container-lg);
  }
  
  .grid {
    gap: var(--spacing-2xl);
  }
  
  /* Enhanced Grid Options */
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  /* Enhanced Spacing */
  .lg\:gap-2xl { gap: var(--spacing-2xl); }
  .lg\:gap-3xl { gap: var(--spacing-3xl); }
  
  .lg\:px-2xl { padding-left: var(--spacing-2xl); padding-right: var(--spacing-2xl); }
  .lg\:py-2xl { padding-top: var(--spacing-2xl); padding-bottom: var(--spacing-2xl); }
  
  /* Typography Scaling */
  .lg\:text-xl { font-size: var(--text-xl); }
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:text-3xl { font-size: var(--text-3xl); }
}

/* ==========================================================================
   DESKTOP (1024px+)
   ========================================================================== */

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-3xl);
    max-width: var(--container-xl);
  }
  
  .grid {
    gap: var(--spacing-3xl);
  }
  
  /* Enhanced Grid Options */
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .xl\:grid-cols-8 { grid-template-columns: repeat(8, 1fr); }
  
  /* Enhanced Spacing */
  .xl\:gap-3xl { gap: var(--spacing-3xl); }
  .xl\:gap-4xl { gap: var(--spacing-4xl); }
  
  .xl\:px-3xl { padding-left: var(--spacing-3xl); padding-right: var(--spacing-3xl); }
  .xl\:py-3xl { padding-top: var(--spacing-3xl); padding-bottom: var(--spacing-3xl); }
}

/* ==========================================================================
   LARGE DESKTOP (1280px+)
   ========================================================================== */

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-2xl);
  }
  
  /* Enhanced Grid Options */
  .\32xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .\32xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .\32xl\:grid-cols-8 { grid-template-columns: repeat(8, 1fr); }
  .\32xl\:grid-cols-12 { grid-template-columns: repeat(12, 1fr); }
  
  /* Enhanced Spacing */
  .\32xl\:gap-4xl { gap: var(--spacing-4xl); }
  
  .\32xl\:px-4xl { padding-left: var(--spacing-4xl); padding-right: var(--spacing-4xl); }
  .\32xl\:py-4xl { padding-top: var(--spacing-4xl); padding-bottom: var(--spacing-4xl); }
}

/* ==========================================================================
   ACCESSIBILITY & MOTION
   ========================================================================== */

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .touch-target,
  .transition-all,
  * {
    transition: none !important;
    animation: none !important;
  }
  
  .touch-target:active {
    transform: none;
  }
}

/* High Contrast Support */
@media (prefers-contrast: high) {
  .touch-target {
    border: 2px solid currentColor;
  }
}

/* Focus Indicators */
.focus\:outline-none:focus {
  outline: none;
}

.focus\:ring:focus {
  outline: 2px solid var(--focus-color, #3b82f6);
  outline-offset: 2px;
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

/* Visibility */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Responsive Visibility */
@media (max-width: 479px) {
  .hidden-xs { display: none; }
}

@media (min-width: 480px) and (max-width: 639px) {
  .hidden-sm { display: none; }
}

@media (min-width: 640px) and (max-width: 767px) {
  .hidden-md { display: none; }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hidden-lg { display: none; }
}

@media (min-width: 1024px) {
  .hidden-xl { display: none; }
}

/* Show only on specific breakpoints */
.show-xs { display: block; }
.show-sm,
.show-md,
.show-lg,
.show-xl { display: none; }

@media (min-width: 480px) {
  .show-xs { display: none; }
  .show-sm { display: block; }
}

@media (min-width: 640px) {
  .show-sm { display: none; }
  .show-md { display: block; }
}

@media (min-width: 768px) {
  .show-md { display: none; }
  .show-lg { display: block; }
}

@media (min-width: 1024px) {
  .show-lg { display: none; }
  .show-xl { display: block; }
}

/* ==========================================================================
   CONTAINER QUERIES (Future-Proof)
   ========================================================================== */

/* Container Query Support */
.container-query {
  container-type: inline-size;
}

/* Example container queries for components */
@container (max-width: 300px) {
  .container-query .compact-layout {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

@container (min-width: 500px) {
  .container-query .expanded-layout {
    flex-direction: row;
    gap: var(--spacing-lg);
  }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
  .container {
    max-width: none;
    padding: 0;
  }
  
  .touch-target {
    min-height: auto;
    min-width: auto;
  }
  
  .hidden-print {
    display: none;
  }
}