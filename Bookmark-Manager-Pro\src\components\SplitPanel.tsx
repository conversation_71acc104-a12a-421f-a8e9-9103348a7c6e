import {
  Check,
  CheckCircle2,
  FileOutput,
  Filter,
  Folder,
  SplitSquareVertical,
  Tag as TagIcon,
  X
} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import type { Bookmark, ExportFormat, SplitCriteria } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'

interface SplitPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SplitPanel: React.FC<SplitPanelProps> = ({ isOpen, onClose }) => {
  const {
    splitBookmarks,
    exportBookmarks,
    selectedBookmarks,
    filteredBookmarks,
    collections,
    tags,
    toggleSelectMode
  } = useBookmarks()

  const [splitCriteria, setSplitCriteria] = useState<SplitCriteria>('collection')
  const [splitResults, setSplitResults] = useState<{ [key: string]: Bookmark[] } | null>(null)
  const [selectedTag, setSelectedTag] = useState<string | null>(null)
  const [exportFormat, setExportFormat] = useState<ExportFormat>('json')
  const [baseFilename, setBaseFilename] = useState('bookmarks')
  const [showPreview, setShowPreview] = useState(false)

  // Reset state when panel is closed
  useEffect(() => {
    if (!isOpen) {
      setSplitResults(null)
      setShowPreview(false)
    }
  }, [isOpen])

  // When criteria changes, reset selected tag
  useEffect(() => {
    setSelectedTag(null)
    setSplitResults(null)
  }, [splitCriteria])

  const handleSplit = () => {
    let results

    if (splitCriteria === 'tag' && selectedTag) {
      results = splitBookmarks('tag', selectedTag)
    } else {
      results = splitBookmarks(splitCriteria)
    }

    setSplitResults(results)
    setShowPreview(true)
  }

  const handleExportSplitResults = () => {
    if (!splitResults) return

    // Export each group as a separate file
    Object.entries(splitResults).forEach(([groupName, bookmarks]) => {
      const filename = `${baseFilename}_${groupName.toLowerCase().replace(/\s+/g, '_')}`
      exportBookmarks(exportFormat, filename, bookmarks)
    })
  }

  const getBookmarksToSplit = () => {
    return selectedBookmarks.length > 0 ? selectedBookmarks : filteredBookmarks
  }

  const handleEnterSelectMode = () => {
    toggleSelectMode()
    // Don't close panel in tabbed mode - let user select bookmarks while keeping panel open
  }

  if (!isOpen) return null

  return (
    <div className="import-panel organization-panel"> {/* Using unified panel design system */}
      <div className="import-header">
        <h2 className="import-title">Split Bookmarks</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close split panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content">
        {!showPreview ? (
          <>
            {/* Compact Split Configuration */}
            <div className="import-section">
              <h3 className="section-title">Split Configuration</h3>
              <p className="section-description">
                {selectedBookmarks.length > 0
                  ? `Split ${selectedBookmarks.length} selected bookmarks`
                  : `Split ${filteredBookmarks.length} filtered bookmarks`}
                {selectedBookmarks.length === 0 && (
                  <button
                    onClick={handleEnterSelectMode}
                    className="template-btn"
                    style={{ marginLeft: '8px', padding: '4px 8px', fontSize: '12px' }}
                  >
                    Select Bookmarks
                  </button>
                )}
              </p>

              {/* Split Method - Compact */}
              <div className="format-options">
                <button
                  onClick={() => setSplitCriteria('collection')}
                  className={`format-option ${splitCriteria === 'collection' ? 'active' : ''}`}
                >
                  <Folder size={20} />
                  <span>Collection</span>
                  <small>By collections</small>
                </button>
                <button
                  onClick={() => setSplitCriteria('tag')}
                  className={`format-option ${splitCriteria === 'tag' ? 'active' : ''}`}
                >
                  <TagIcon size={20} />
                  <span>Tag</span>
                  <small>By specific tag</small>
                </button>
                <button
                  onClick={() => setSplitCriteria('manual')}
                  className={`format-option ${splitCriteria === 'manual' ? 'active' : ''}`}
                >
                  <CheckCircle2 size={20} />
                  <span>Manual</span>
                  <small>Split selection</small>
                </button>
              </div>

              {/* Tag Selection - Inline when tag method selected */}
              {splitCriteria === 'tag' && (
                <div style={{ marginTop: '16px' }}>
                  <label className="upload-text">Select Tag</label>
                  <div className="format-options" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))' }}>
                    {tags.slice(0, 8).map(tag => (
                      <button
                        key={tag}
                        onClick={() => setSelectedTag(tag)}
                        className={`format-option ${selectedTag === tag ? 'active' : ''}`}
                        style={{ padding: '8px 4px', minHeight: '60px' }}
                      >
                        <TagIcon size={16} />
                        <span style={{ fontSize: '12px' }}>{tag}</span>
                      </button>
                    ))}
                  </div>
                  {tags.length > 8 && (
                    <p className="upload-hint" style={{ fontSize: '11px', marginTop: '4px' }}>
                      +{tags.length - 8} more tags available
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Export Options - Compact when not manual */}
            {splitCriteria !== 'manual' && (
              <div style={{ marginTop: '16px' }}>
                <label className="upload-text">Export Format & Filename</label>
                <div style={{ display: 'flex', gap: '8px', alignItems: 'end', marginTop: '8px' }}>
                  <div style={{ flex: '0 0 auto' }}>
                    <select
                      value={exportFormat}
                      onChange={(e) => setExportFormat(e.target.value as 'json' | 'html' | 'csv')}
                      className="filename-field"
                      style={{ width: '80px', padding: '8px', fontSize: '13px' }}
                    >
                      <option value="json">JSON</option>
                      <option value="html">HTML</option>
                      <option value="csv">CSV</option>
                    </select>
                  </div>
                  <div style={{ flex: 1 }}>
                    <input
                      type="text"
                      value={baseFilename}
                      onChange={(e) => setBaseFilename(e.target.value)}
                      className="filename-field"
                      placeholder="base-filename"
                      style={{ padding: '8px', fontSize: '13px' }}
                    />
                  </div>
                </div>
                <p className="upload-hint" style={{ fontSize: '11px', marginTop: '4px' }}>
                  Files will be saved as: {baseFilename}_[group].{exportFormat}
                </p>
              </div>
            )}

            {/* Split Action - Compact */}
            <div className="import-section">
              <div className="export-area" style={{ minHeight: '100px', padding: '16px' }}>
                <SplitSquareVertical size={24} className="upload-icon" />
                <p className="upload-text" style={{ fontSize: '14px', margin: '8px 0 4px 0' }}>Ready to split bookmarks</p>
                <p className="upload-hint" style={{ fontSize: '12px', margin: '0 0 12px 0' }}>
                  {splitCriteria === 'collection' && 'Split by collections'}
                  {splitCriteria === 'tag' && selectedTag && `Split by "${selectedTag}" tag`}
                  {splitCriteria === 'tag' && !selectedTag && 'Select a tag to continue'}
                  {splitCriteria === 'manual' && 'Split by selection'}
                </p>
                <button
                  onClick={handleSplit}
                  className="import-another-btn"
                  disabled={splitCriteria === 'tag' && !selectedTag}
                  style={{ padding: '8px 16px', fontSize: '13px' }}
                >
                  <SplitSquareVertical size={16} />
                  Split Bookmarks
                </button>
              </div>
            </div>
          </>
        ) : (
          // Preview Results
          <>
            <div className="import-section">
              <h3 className="section-title">Split Results</h3>
              <p className="section-description">
                Successfully split into {Object.keys(splitResults || {}).length} groups.
                <button
                  onClick={() => setShowPreview(false)}
                  className="template-btn"
                  style={{ marginLeft: '8px', padding: '2px 8px', fontSize: '12px' }}
                >
                  <Filter size={12} />
                  Change
                </button>
              </p>

              <div className="browser-instructions" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {splitResults && Object.entries(splitResults).map(([groupName, bookmarks]) => (
                  <div key={groupName} className="instruction-item" style={{ padding: '8px 12px' }}>
                    {splitCriteria === 'collection' ? (
                      <Folder size={14} />
                    ) : splitCriteria === 'tag' ? (
                      <TagIcon size={14} />
                    ) : (
                      <CheckCircle2 size={14} />
                    )}
                    <span style={{ fontSize: '13px' }}>
                      {groupName} ({bookmarks.length} bookmarks)
                      {bookmarks.length > 0 && (
                        <span style={{ color: 'var(--text-muted)', fontSize: '11px', marginLeft: '4px' }}>
                          - {bookmarks.slice(0, 2).map(b => b.title).join(', ')}
                          {bookmarks.length > 2 && ` +${bookmarks.length - 2} more`}
                        </span>
                      )}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Export Actions - Compact */}
            <div className="import-section">
              {splitCriteria !== 'manual' ? (
                <div className="export-area" style={{ minHeight: '80px', padding: '12px' }}>
                  <FileOutput size={20} className="upload-icon" />
                  <p className="upload-text" style={{ fontSize: '13px', margin: '4px 0' }}>
                    Export {Object.keys(splitResults || {}).length} split files
                  </p>
                  <button
                    onClick={handleExportSplitResults}
                    className="import-another-btn"
                    style={{ padding: '6px 12px', fontSize: '12px' }}
                  >
                    <FileOutput size={14} />
                    Export Files
                  </button>
                </div>
              ) : (
                <div className="export-area" style={{ minHeight: '80px', padding: '12px' }}>
                  <Check size={20} className="success-icon" />
                  <p className="upload-text" style={{ fontSize: '13px', margin: '4px 0' }}>
                    Split completed successfully!
                  </p>
                  <button
                    onClick={onClose}
                    className="import-another-btn"
                    style={{ padding: '6px 12px', fontSize: '12px' }}
                  >
                    <Check size={14} />
                    Done
                  </button>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}