// Smart AI Organization Confidence Tests
// Tests the emotional response and trust-building aspects of AI-powered bookmark organization

import { expect, test } from '@playwright/test';
import { PerformanceMonitor } from '../utils/performance-monitor.js';
import { VibeMetrics } from '../utils/vibe-metrics.js';

const SELECTORS = {
    aiOrganizeButton: '[data-testid="ai-organize-btn"], .ai-organize-button, .smart-organize',
    progressIndicator: '.progress-bar, .loading-progress, [role="progressbar"]',
    organizationResults: '.organization-results, .ai-results, .categorization-output',
    categoryCard: '.category-card, .folder-card, .collection-item',
    confidenceScore: '.confidence-score, .ai-confidence, .accuracy-indicator',
    undoButton: '[data-testid="undo-organize"], .undo-btn, .revert-organization',
    errorAlert: '[role="alert"], .error, .error-message'
};

test.describe('Smart AI Organization Confidence - Vibe Testing', () => {

    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('domcontentloaded');

        // Wait for bookmark data to load
        try {
            await page.waitForSelector('[data-testid="bookmark-item"], .bookmark-card', { timeout: 10000 });
            await page.waitForTimeout(1000);
        } catch (error) {
            console.log('No bookmark elements found - may be expected for some test scenarios');
        }
    });

    test('AI organization feels trustworthy and transparent', async ({ page }) => {
        const session = await PerformanceMonitor.startMonitoring(page, 'ai-organization-trust');

        // Check if AI organize feature is available
        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();
        const buttonExists = await aiButton.count() > 0;

        if (!buttonExists) {
            console.log('AI organize button not found - feature may not be implemented yet');
            await test.info().attach('vibe-metrics', {
                body: JSON.stringify({
                    testType: 'ai-organization-trust',
                    status: 'skipped',
                    reason: 'AI organize feature not available'
                }),
                contentType: 'application/json'
            });
            await PerformanceMonitor.endMonitoring(session);
            return;
        }

        // Test initial confidence building
        const buttonText = await aiButton.textContent();
        const hasConfidentLanguage = buttonText?.includes('Smart') ||
            buttonText?.includes('AI') ||
            buttonText?.includes('Organize');

        expect(hasConfidentLanguage).toBe(true);

        // Measure trust-building response time
        const trustMetrics = await VibeMetrics.measureResponseTime(page, async () => {
            await aiButton.click();
        }, 1000); // AI operations can take longer

        // Check for transparency indicators
        await page.waitForTimeout(500);

        const transparencyIndicators = await page.evaluate((selectors) => {
            return {
                hasProgressIndicator: !!document.querySelector(selectors.progressIndicator),
                hasStatusMessages: !!document.querySelector('.status-message, .ai-status, .processing-info'),
                hasConfidenceScores: !!document.querySelector(selectors.confidenceScore),
                hasExplanations: !!document.querySelector('.explanation, .ai-reasoning, .why-organized'),
                noBlackBox: !document.querySelector('.mysterious-processing, .unknown-status')
            };
        }, SELECTORS);

        // Trust requires transparency
        expect(transparencyIndicators.hasProgressIndicator || transparencyIndicators.hasStatusMessages).toBe(true);
        expect(transparencyIndicators.noBlackBox).toBe(true);

        // Wait for processing to complete or timeout gracefully
        try {
            await page.waitForSelector(SELECTORS.organizationResults, { timeout: 30000 });
        } catch (error) {
            console.log('AI organization may still be processing - checking for progress indicators');
        }

        // Check for trust-building elements in results
        const trustElements = await page.evaluate(() => {
            return {
                hasUndoOption: !!document.querySelector('[data-testid="undo-organize"], .undo-btn, .revert-organization'),
                hasConfidenceScores: !!document.querySelector('.confidence-score, .ai-confidence'),
                hasExplanations: !!document.querySelector('.explanation, .reasoning, .why-section'),
                allowsManualOverride: !!document.querySelector('.manual-override, .edit-categories, .customize-organization')
            };
        });

        // Trust requires user control and transparency
        expect(trustElements.hasUndoOption || trustElements.allowsManualOverride).toBe(true);

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                responseTime: trustMetrics,
                transparencyIndicators,
                trustElements,
                testType: 'ai-organization-trust'
            }),
            contentType: 'application/json'
        });

        await PerformanceMonitor.endMonitoring(session);
    });

    test('AI organization progress feels reassuring, not anxious', async ({ page }) => {
        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();

        if (await aiButton.count() === 0) {
            test.skip('AI organize feature not available');
            return;
        }

        // Start AI organization
        await aiButton.click();
        await page.waitForTimeout(200);

        // Monitor emotional journey during processing
        const emotionalJourney = [];
        const startTime = Date.now();

        // Check for anxiety-reducing elements
        const reassuranceCheck = async () => {
            const currentTime = Date.now() - startTime;
            const reassuranceElements = await page.evaluate(() => {
                return {
                    hasProgressBar: !!document.querySelector('.progress-bar, [role="progressbar"]'),
                    hasStatusText: !!document.querySelector('.status-text, .processing-status'),
                    hasTimeEstimate: !!document.querySelector('.time-estimate, .eta, .estimated-time'),
                    hasCancelOption: !!document.querySelector('.cancel-btn, .stop-processing'),
                    showsCurrentStep: !!document.querySelector('.current-step, .processing-step'),
                    noSpinnerOnly: !document.querySelector('.spinner-only, .loading-only')
                };
            });

            emotionalJourney.push({
                timestamp: currentTime,
                emotion: reassuranceElements.hasProgressBar ? 'reassured' : 'uncertain',
                trigger: 'progress-check',
                elements: reassuranceElements
            });

            return reassuranceElements;
        };

        // Check reassurance elements multiple times during processing
        const initialCheck = await reassuranceCheck();
        await page.waitForTimeout(1000);
        const midCheck = await reassuranceCheck();
        await page.waitForTimeout(1000);
        const finalCheck = await reassuranceCheck();

        // Anxiety prevention requires informative progress
        const hasReassurance = initialCheck.hasProgressBar ||
            initialCheck.hasStatusText ||
            initialCheck.showsCurrentStep;

        expect(hasReassurance).toBe(true);

        // Check that progress doesn't feel stuck or broken
        const progressFeelsAlive = midCheck.hasStatusText ||
            midCheck.showsCurrentStep ||
            finalCheck.hasProgressBar;

        expect(progressFeelsAlive).toBe(true);

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'ai-processing-reassurance',
                    duration: Date.now() - startTime,
                    checkPoints: 3
                },
                emotionalStates: emotionalJourney,
                reassuranceFactors: [
                    { category: 'progress-visibility', present: initialCheck.hasProgressBar, importance: 9 },
                    { category: 'status-communication', present: initialCheck.hasStatusText, importance: 8 },
                    { category: 'user-control', present: initialCheck.hasCancelOption, importance: 7 }
                ]
            }),
            contentType: 'application/json'
        });
    });

    test('AI organization results feel intelligent, not random', async ({ page }) => {
        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();

        if (await aiButton.count() === 0) {
            test.skip('AI organize feature not available');
            return;
        }

        await aiButton.click();

        // Wait for results with reasonable timeout
        try {
            await page.waitForSelector(SELECTORS.organizationResults, { timeout: 30000 });
        } catch (error) {
            console.log('AI organization still processing - checking for partial results');
        }

        // Analyze intelligence indicators in results
        const intelligenceIndicators = await page.evaluate((selectors) => {
            const categories = document.querySelectorAll(selectors.categoryCard.split(', ')[0]);
            const results = {
                hasMeaningfulNames: false,
                hasLogicalGrouping: false,
                hasConfidenceScores: false,
                hasExplanations: false,
                showsThinking: false,
                categoryCount: categories.length,
                categoryNames: []
            };

            categories.forEach(category => {
                const name = category.textContent || category.querySelector('.category-name, .folder-name')?.textContent;
                if (name) {
                    results.categoryNames.push(name.trim());

                    // Check for meaningful names (not just "Category 1", "Group A", etc.)
                    if (!name.match(/^(Category|Group|Folder)\s*\d+$/i) && name.length > 3) {
                        results.hasMeaningfulNames = true;
                    }
                }

                // Check for intelligence indicators
                if (category.querySelector('.confidence-score, .ai-confidence')) {
                    results.hasConfidenceScores = true;
                }
                if (category.querySelector('.explanation, .reasoning, .why-grouped')) {
                    results.hasExplanations = true;
                }
            });

            // Check for logical grouping (categories should have reasonable sizes)
            const categorySizes = Array.from(categories).map(cat => {
                const countEl = cat.querySelector('.bookmark-count, .item-count');
                return countEl ? parseInt(countEl.textContent) || 0 : 0;
            });

            results.hasLogicalGrouping = categorySizes.some(size => size > 1 && size < 50);
            results.showsThinking = !!document.querySelector('.ai-reasoning, .analysis-summary, .organization-logic');

            return results;
        }, SELECTORS);

        // Intelligence requires meaningful categorization
        if (intelligenceIndicators.categoryCount > 0) {
            expect(intelligenceIndicators.hasMeaningfulNames).toBe(true);
            expect(intelligenceIndicators.hasLogicalGrouping).toBe(true);
        }

        // Check for signs of AI thinking, not randomness
        const showsIntelligence = intelligenceIndicators.hasConfidenceScores ||
            intelligenceIndicators.hasExplanations ||
            intelligenceIndicators.showsThinking;

        if (intelligenceIndicators.categoryCount > 0) {
            expect(showsIntelligence).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                intelligenceIndicators,
                testType: 'ai-intelligence-perception',
                categoryAnalysis: {
                    names: intelligenceIndicators.categoryNames,
                    count: intelligenceIndicators.categoryCount,
                    meaningfulNaming: intelligenceIndicators.hasMeaningfulNames
                }
            }),
            contentType: 'application/json'
        });
    });

    test('AI organization mistakes feel correctable, not permanent', async ({ page }) => {
        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();

        if (await aiButton.count() === 0) {
            test.skip('AI organize feature not available');
            return;
        }

        await aiButton.click();
        await page.waitForTimeout(2000);

        // Check for correction mechanisms
        const correctionOptions = await page.evaluate(() => {
            return {
                hasUndoButton: !!document.querySelector('[data-testid="undo-organize"], .undo-btn, .revert-organization'),
                hasEditOptions: !!document.querySelector('.edit-categories, .modify-organization, .customize-btn'),
                hasManualOverride: !!document.querySelector('.manual-override, .drag-to-reorganize'),
                hasFeedbackOption: !!document.querySelector('.feedback-btn, .improve-ai, .report-issue'),
                allowsIndividualChanges: !!document.querySelector('.move-bookmark, .change-category'),
                showsReversibility: !!document.querySelector('.reversible, .can-undo, .temporary-changes')
            };
        });

        // User confidence requires ability to correct AI mistakes
        const hasCorrectability = correctionOptions.hasUndoButton ||
            correctionOptions.hasEditOptions ||
            correctionOptions.hasManualOverride;

        expect(hasCorrectability).toBe(true);

        // Test undo functionality if available
        const undoButton = page.locator(SELECTORS.undoButton).first();
        if (await undoButton.count() > 0) {
            const undoMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await undoButton.click();
            }, 500);

            expect(['excellent', 'good', 'acceptable']).toContain(undoMetrics.quality);

            // Check that undo provides reassuring feedback
            await page.waitForTimeout(300);
            const undoFeedback = await page.evaluate(() => {
                return {
                    hasConfirmation: !!document.querySelector('.undo-confirmation, .reverted-message'),
                    hasVisualChange: !!document.querySelector('.organization-reverted, .changes-undone'),
                    noErrorState: !document.querySelector('.undo-failed, .revert-error')
                };
            });

            expect(undoFeedback.noErrorState).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                correctionOptions,
                testType: 'ai-mistake-correctability',
                userControlLevel: hasCorrectability ? 'high' : 'low'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'ai-mistake-handling',
                    duration: 3000,
                    interactions: 2
                },
                emotionalStates: [
                    { emotion: 'curious', timestamp: 0, trigger: 'start-ai-organization' },
                    { emotion: 'evaluative', timestamp: 1000, trigger: 'review-results' },
                    { emotion: 'confident', timestamp: 2000, trigger: 'see-correction-options' }
                ],
                confidenceFactors: [
                    { category: 'reversibility', trigger: 'undo-available', intensity: 9 },
                    { category: 'control', trigger: 'edit-options', intensity: 8 },
                    { category: 'learning', trigger: 'feedback-mechanism', intensity: 7 }
                ]
            }),
            contentType: 'application/json'
        });
    });

    test('AI organization learning feels collaborative, not judgmental', async ({ page }) => {
        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();

        if (await aiButton.count() === 0) {
            test.skip('AI organize feature not available');
            return;
        }

        await aiButton.click();
        await page.waitForTimeout(2000);

        // Check for collaborative learning indicators
        const collaborationIndicators = await page.evaluate(() => {
            return {
                hasThumbsUpDown: !!document.querySelector('.thumbs-up, .thumbs-down, .rating-buttons'),
                hasFeedbackForm: !!document.querySelector('.feedback-form, .improve-suggestion'),
                hasLearningLanguage: !!document.querySelector('.help-ai-learn, .teach-preferences'),
                showsAppreciation: !!document.querySelector('.thank-you, .learning-from-you'),
                noJudgmentalLanguage: !document.querySelector('.wrong-choice, .bad-organization, .incorrect'),
                hasPositiveFraming: !!document.querySelector('.help-improve, .make-better, .learn-together'),
                allowsPreferences: !!document.querySelector('.set-preferences, .customize-ai, .your-style')
            };
        });

        // Collaborative AI should feel like a partner, not a judge
        const feelsCollaborative = collaborationIndicators.hasThumbsUpDown ||
            collaborationIndicators.hasFeedbackForm ||
            collaborationIndicators.hasLearningLanguage;

        if (feelsCollaborative) {
            expect(collaborationIndicators.noJudgmentalLanguage).toBe(true);
            expect(collaborationIndicators.hasPositiveFraming || collaborationIndicators.showsAppreciation).toBe(true);
        }

        // Test feedback interaction if available
        const feedbackButton = page.locator('.feedback-btn, .thumbs-up, .improve-btn').first();
        if (await feedbackButton.count() > 0) {
            const feedbackMetrics = await VibeMetrics.measureResponseTime(page, async () => {
                await feedbackButton.click();
            }, 300);

            expect(['excellent', 'good', 'acceptable']).toContain(feedbackMetrics.quality);

            // Check feedback experience
            await page.waitForTimeout(200);
            const feedbackExperience = await page.evaluate(() => {
                return {
                    hasThankYou: !!document.querySelector('.thank-you, .feedback-received'),
                    hasEncouragement: !!document.querySelector('.helps-improve, .learning-together'),
                    noShame: !document.querySelector('.wrong, .mistake, .error-correction'),
                    hasPositiveTone: !!document.querySelector('.appreciate, .helpful, .improve-together')
                };
            });

            expect(feedbackExperience.noShame).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                collaborationIndicators,
                testType: 'ai-collaboration-feeling',
                collaborationLevel: feelsCollaborative ? 'high' : 'low'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'ai-collaboration',
                    duration: 3000,
                    interactions: 1
                },
                emotionalStates: [
                    { emotion: 'curious', timestamp: 0, trigger: 'start-ai-organization' },
                    { emotion: 'collaborative', timestamp: 1500, trigger: 'see-feedback-options' },
                    { emotion: 'appreciated', timestamp: 2500, trigger: 'positive-feedback-response' }
                ],
                collaborationPoints: [
                    { category: 'partnership', trigger: 'feedback-request', intensity: 8 },
                    { category: 'learning', trigger: 'preference-setting', intensity: 7 },
                    { category: 'appreciation', trigger: 'thank-you-message', intensity: 9 }
                ]
            }),
            contentType: 'application/json'
        });
    });

    test('AI organization handles large collections without overwhelming user', async ({ page }) => {
        // Check bookmark count to determine if this is a large collection test
        const bookmarkCount = await page.locator('[data-testid="bookmark-item"], .bookmark-card').count();

        if (bookmarkCount < 50) {
            console.log(`Small collection (${bookmarkCount} bookmarks) - testing scalability indicators`);
        }

        const aiButton = page.locator(SELECTORS.aiOrganizeButton).first();

        if (await aiButton.count() === 0) {
            test.skip('AI organize feature not available');
            return;
        }

        // Test scalability confidence
        const scalabilityMetrics = await VibeMetrics.measureResponseTime(page, async () => {
            await aiButton.click();
        }, 2000); // Allow more time for large collections

        // Check for scalability handling
        await page.waitForTimeout(1000);

        const scalabilityHandling = await page.evaluate(() => {
            return {
                hasProgressSteps: !!document.querySelector('.step-progress, .multi-step, .phase-indicator'),
                hasBatchProcessing: !!document.querySelector('.batch-processing, .processing-batch'),
                hasTimeEstimate: !!document.querySelector('.time-estimate, .eta, .estimated-completion'),
                hasMemoryOptimization: !!document.querySelector('.memory-optimized, .efficient-processing'),
                showsScaleHandling: !!document.querySelector('.large-collection, .bulk-processing'),
                noOverwhelm: !document.querySelector('.too-many, .overwhelming, .system-overload'),
                hasIncrementalResults: !!document.querySelector('.partial-results, .incremental-progress')
            };
        });

        // Large collections should feel manageable, not overwhelming
        expect(scalabilityHandling.noOverwhelm).toBe(true);

        // Should show confidence in handling scale
        const showsScaleConfidence = scalabilityHandling.hasProgressSteps ||
            scalabilityHandling.hasBatchProcessing ||
            scalabilityHandling.hasTimeEstimate;

        if (bookmarkCount > 100) {
            expect(showsScaleConfidence).toBe(true);
        }

        await test.info().attach('vibe-metrics', {
            body: JSON.stringify({
                responseTime: scalabilityMetrics,
                scalabilityHandling,
                bookmarkCount,
                testType: 'ai-scalability-confidence'
            }),
            contentType: 'application/json'
        });

        await test.info().attach('emotional-journey', {
            body: JSON.stringify({
                session: {
                    type: 'large-collection-confidence',
                    duration: 4000,
                    bookmarkCount: bookmarkCount
                },
                emotionalStates: [
                    { emotion: 'apprehensive', timestamp: 0, trigger: 'large-collection-organization' },
                    { emotion: 'reassured', timestamp: 1000, trigger: 'see-progress-handling' },
                    { emotion: 'confident', timestamp: 3000, trigger: 'smooth-processing' }
                ],
                scaleConfidenceFactors: [
                    { category: 'progress-visibility', present: scalabilityHandling.hasProgressSteps, importance: 9 },
                    { category: 'time-management', present: scalabilityHandling.hasTimeEstimate, importance: 8 },
                    { category: 'system-capability', present: scalabilityHandling.showsScaleHandling, importance: 7 }
                ]
            }),
            contentType: 'application/json'
        });
    });

});