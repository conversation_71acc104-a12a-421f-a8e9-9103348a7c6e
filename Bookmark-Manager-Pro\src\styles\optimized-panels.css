/* ==========================================================================
   OPTIMIZED PANEL SYSTEM - NO SCROLL DESIGN
   ========================================================================== */

/* Multimedia Organization Panel Styles */
.results-container {
  background-color: var(--primary-bg);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  max-height: 300px;
  overflow-y: auto;
  font-family: var(--font-mono, monospace);
  font-size: 13px;
  line-height: 1.5;
}

.result-item {
  padding: 4px 0;
  border-bottom: 1px solid var(--border-light);
}

.result-item:last-child {
  border-bottom: none;
}

.playlist-info {
  background-color: var(--primary-bg);
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

/* Stat cards for multimedia analysis */
.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background-color: var(--primary-bg);
  border-radius: 8px;
  border: 1px solid var(--border-light);
  text-align: center;
  gap: 4px;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-card small {
  font-size: 0.625rem;
  color: var(--text-tertiary);
}

/* UNIFIED MULTIMEDIA PANEL STYLES - ALWAYS MODERN */
.results-container {
  /* Always use modern glass design */
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.playlist-info {
  background-color: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.stat-card {
  background-color: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Light theme multimedia adjustments */
.light .results-container {
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.light .playlist-info {
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

.light .stat-card {
  background-color: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

/* Bookmark selector styles */
.bookmark-selector {
  background: var(--primary-bg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md, 0.375rem);
}

.bookmark-selector label:hover {
  background: var(--secondary-bg);
}

/* UNIFIED BOOKMARK SELECTOR STYLES - ALWAYS MODERN */
.bookmark-selector {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.bookmark-selector label:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Light theme bookmark selector adjustments */
.light .bookmark-selector {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.light .bookmark-selector label:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* Format option active state */
.format-option.active {
  background: var(--accent-color) !important;
  color: white;
  border-color: var(--accent-color);
}

.format-option.active small {
  color: rgba(255, 255, 255, 0.8);
}

/* Compact button styles */
.btn-compact {
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
}

/* Enhanced selection mode styling */
.selection-mode-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-3, 0.75rem);
  margin: var(--space-4, 1rem) 0;
}

.selection-mode-option {
  display: flex;
  align-items: flex-start;
  padding: var(--space-4, 1rem);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg, 0.5rem);
  background: var(--primary-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.selection-mode-option:hover {
  border-color: var(--accent-color);
  background: var(--secondary-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selection-mode-option.active {
  border-color: var(--accent-color);
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.05);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

.selection-radio {
  margin-right: var(--space-3, 0.75rem);
  margin-top: var(--space-1, 0.25rem);
  accent-color: var(--accent-color);
}

.selection-content {
  flex: 1;
}

.selection-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-2, 0.5rem);
}

.selection-icon {
  font-size: 1.25rem;
  margin-right: var(--space-2, 0.5rem);
}

.selection-title {
  font-size: var(--text-base, 1rem);
  font-weight: var(--font-weight-semibold, 600);
  color: var(--text-primary);
}

.selection-description {
  font-size: var(--text-sm, 0.875rem);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* UNIFIED SELECTION MODE STYLES - ALWAYS MODERN */
.selection-mode-option {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.selection-mode-option:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: #4A9EFF;
}

.selection-mode-option.active {
  background: rgba(74, 158, 255, 0.1);
  border-color: #4A9EFF;
}

/* Light theme selection mode adjustments */
.light .selection-mode-option {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.light .selection-mode-option:hover {
  background: rgba(255, 255, 255, 0.7);
  border-color: #4A9EFF;
}

.light .selection-mode-option.active {
  background: rgba(74, 158, 255, 0.1);
  border-color: #4A9EFF;
}

/* Enhanced bookmark selector styling */
.enhanced-bookmark-selector {
  background: var(--primary-bg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg, 0.5rem);
  overflow: hidden;
  margin-top: var(--space-3, 0.75rem);
}

.bookmark-list {
  max-height: 300px;
  overflow-y: auto;
  padding: var(--space-2, 0.5rem);
}

.bookmark-item {
  display: flex;
  align-items: center;
  padding: var(--space-3, 0.75rem);
  border-radius: var(--radius-md, 0.375rem);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: var(--space-1, 0.25rem);
}

.bookmark-item:hover {
  background: var(--secondary-bg);
  border-color: var(--border-light);
}

.bookmark-item.selected {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.05);
  border-color: var(--accent-color);
}

.bookmark-checkbox {
  margin-right: var(--space-3, 0.75rem);
  accent-color: var(--accent-color);
  transform: scale(1.1);
}

.bookmark-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-primary);
  margin-bottom: var(--space-1, 0.25rem);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-url {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: var(--space-1, 0.25rem);
}

.bookmark-collection {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-tertiary);
  font-style: italic;
}

.bookmark-indicator {
  margin-left: var(--space-2, 0.5rem);
  width: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.selected-indicator {
  color: var(--accent-color);
  font-weight: bold;
  font-size: 1rem;
}

.bookmark-limit-notice {
  padding: var(--space-3, 0.75rem);
  text-align: center;
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary);
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-light);
}

/* UNIFIED ENHANCED BOOKMARK SELECTOR STYLES - ALWAYS MODERN */
.enhanced-bookmark-selector {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.bookmark-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.bookmark-item.selected {
  background: rgba(74, 158, 255, 0.1);
  border-color: #4A9EFF;
}

.bookmark-limit-notice {
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Light theme enhanced bookmark selector adjustments */
.light .enhanced-bookmark-selector {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.light .bookmark-item:hover {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.1);
}

.light .bookmark-item.selected {
  background: rgba(74, 158, 255, 0.1);
  border-color: #4A9EFF;
}

.light .bookmark-limit-notice {
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

/* Collection grouping styles */
.collection-group {
  margin-bottom: var(--space-4, 1rem);
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3, 0.75rem);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-md, 0.375rem) var(--radius-md, 0.375rem) 0 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.collection-info {
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
}

.collection-icon {
  font-size: 1rem;
}

.collection-name {
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-weight-semibold, 600);
  color: var(--text-primary);
}

.collection-count {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary);
}

.collection-actions {
  display: flex;
  gap: var(--space-2, 0.5rem);
}

.collection-select-btn {
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm, 0.25rem);
  background: var(--primary-bg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.collection-select-btn:hover {
  background: var(--secondary-bg);
  border-color: var(--accent-color);
  color: var(--text-primary);
}

.collection-select-btn.selected {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.collection-select-btn.partial {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.collection-bookmarks {
  border-left: 3px solid var(--border-light);
  margin-left: var(--space-4, 1rem);
  padding-left: var(--space-2, 0.5rem);
}

.collection-bookmarks .bookmark-item {
  margin-left: 0;
  border-radius: 0 var(--radius-md, 0.375rem) var(--radius-md, 0.375rem) 0;
}

/* UNIFIED COLLECTION STYLES - ALWAYS MODERN */
.collection-header {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.collection-select-btn {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

.collection-select-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: #4A9EFF;
  color: rgba(255, 255, 255, 0.9);
}

.collection-bookmarks {
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Light theme collection adjustments */
.light .collection-header {
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.light .collection-select-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.7);
}

.light .collection-select-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #4A9EFF;
  color: rgba(0, 0, 0, 0.9);
}

.light .collection-bookmarks {
  border: 2px solid rgba(0, 0, 0, 0.1);
}

/* UNIFIED PANEL DESIGN SYSTEM - THEME AGNOSTIC */
/* Core panel container - always uses modern design regardless of theme mode */
.import-panel,
.export-panel,
.organization-panel,
.optimized-panel {
  /* Panel positioning and sizing */
  position: fixed;
  top: 0;
  right: 0;
  width: 360px;
  height: 100vh;
  z-index: 1000;
  /* Always use modern glass morphism design */
  background: rgba(0, 0, 0, 0.3);
  border-left: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Modern styling always applied */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Light theme adjustments */
.light .import-panel,
.light .export-panel,
.light .organization-panel,
.light .optimized-panel {
  background: rgba(255, 255, 255, 0.95);
  border-left: 2px solid rgba(0, 0, 0, 0.1);
}

/* Legacy modern-enhanced conditionals removed - now handled by unified design system in App.css */

/* Legacy modern-enhanced format option conditionals removed - now handled by unified design system in App.css */

/* Legacy format option and close button conditionals removed - now handled by unified design system in App.css */

/* Multimedia Modal Specific Styles */
.import-panel.multimedia-modal {
  width: auto;
  max-width: 1024px;
  height: auto;
  max-height: 85vh;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  background: var(--secondary-bg);
}

.import-panel.multimedia-modal.fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
  border: none;
}

.import-panel.multimedia-modal.windowed {
  width: 90vw;
  height: 85vh;
  min-width: 800px;
  min-height: 600px;
}

/* UNIFIED MULTIMEDIA MODAL STYLING - ALWAYS MODERN */
.multimedia-modal {
  /* Always use modern glass morphism design */
  background: rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* Light theme multimedia modal adjustments */
.light .multimedia-modal {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
}

/* Multimedia modal header styling */
.multimedia-modal .import-header {
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
}

/* Light theme multimedia modal header */
.light .multimedia-modal .import-header {
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
}

/* Multimedia modal content area */
.multimedia-modal .import-content {
  padding: 0;
}

/* Media player section styling */
.multimedia-modal .import-section {
  padding: var(--space-4, 1rem);
}

/* Progress bar styling for multimedia modal */
.multimedia-modal .bg-gray-200 {
  /* Always use modern styling */
  background-color: rgba(255, 255, 255, 0.2);
}

/* Light theme progress bar adjustments */
.light .multimedia-modal .bg-gray-200 {
  background-color: rgba(0, 0, 0, 0.2);
}

.multimedia-modal .bg-blue-500 {
  background-color: var(--accent-color);
}

/* Volume slider styling */
.multimedia-modal input[type="range"] {
  background: var(--border-light);
  border-radius: 4px;
  height: 8px;
  outline: none;
  -webkit-appearance: none;
}

.multimedia-modal input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.multimedia-modal input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Volume slider always uses modern styling */
.multimedia-modal input[type="range"] {
  background: rgba(255, 255, 255, 0.2);
}

/* Light theme volume slider adjustments */
.light .multimedia-modal input[type="range"] {
  background: rgba(0, 0, 0, 0.2);
}

/* Playlist item styling */
.multimedia-modal .space-y-2>button {
  transition: all 0.2s ease;
}

.multimedia-modal .space-y-2>button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Playlist item hover always uses modern styling */
.multimedia-modal .space-y-2>button:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* UNIFIED SPEED SELECTOR STYLING - ALWAYS MODERN */
.multimedia-modal select {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.multimedia-modal select option {
  background: rgba(0, 0, 0, 0.9);
  color: rgba(255, 255, 255, 0.9);
}

/* Light theme speed selector adjustments */
.light .multimedia-modal select {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.2);
  color: rgba(0, 0, 0, 0.9);
}

.light .multimedia-modal select option {
  background: rgba(255, 255, 255, 0.95);
  color: rgba(0, 0, 0, 0.9);
}

/* UNIFIED TEXT COLOR ADJUSTMENTS - ALWAYS MODERN */
.multimedia-modal .text-gray-500 {
  /* Always use modern text styling */
  color: rgba(255, 255, 255, 0.6);
}

.multimedia-modal .text-gray-600 {
  color: rgba(255, 255, 255, 0.7);
}

.multimedia-modal .text-gray-300 {
  color: rgba(255, 255, 255, 0.8);
}

/* Light theme text color adjustments */
.light .multimedia-modal .text-gray-500 {
  color: rgba(0, 0, 0, 0.6);
}

.light .multimedia-modal .text-gray-600 {
  color: rgba(0, 0, 0, 0.7);
}

.light .multimedia-modal .text-gray-300 {
  color: rgba(0, 0, 0, 0.5);
}

/* Optimized section layout - compact spacing */
.import-section {
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  border-bottom: 1px solid var(--border-light, #f1f5f9);
  /* Use flex-shrink to allow compression when needed */
  flex-shrink: 0;
}

.import-section:last-child {
  border-bottom: none;
  /* Allow last section to grow if space available */
  flex-grow: 1;
  min-height: 0;
}

/* Compact section titles */
.section-title {
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-weight-semibold, 600);
  color: var(--text-primary);
  margin: 0 0 var(--space-2, 0.5rem) 0;
  line-height: var(--leading-tight, 1.25);
}

/* Optimized section descriptions */
.section-description {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary);
  margin: 0 0 var(--space-3, 0.75rem) 0;
  line-height: var(--leading-snug, 1.375);
}

/* ==========================================================================
   MULTI-COLUMN LAYOUTS FOR DENSE CONTENT
   ========================================================================== */

/* Two-column layout for controls */
.controls-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2, 0.5rem);
  margin-bottom: var(--space-3, 0.75rem);
}

/* Three-column layout for compact buttons */
.button-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-1, 0.25rem);
  margin-bottom: var(--space-2, 0.5rem);
}

/* Compact form layout */
.form-row {
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
  margin-bottom: var(--space-2, 0.5rem);
}

.form-row label {
  font-size: var(--text-xs, 0.75rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-secondary);
  min-width: 80px;
  flex-shrink: 0;
}

/* ==========================================================================
   COLLAPSIBLE SECTIONS FOR ADVANCED FEATURES
   ========================================================================== */

.collapsible-section {
  border: 1px solid var(--border-light, #f1f5f9);
  border-radius: var(--radius-md, 0.375rem);
  margin-bottom: var(--space-2, 0.5rem);
  overflow: hidden;
}

.collapsible-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  background: var(--tertiary-bg, #f8fafc);
  cursor: pointer;
  border: none;
  width: 100%;
  text-align: left;
  font-size: var(--text-xs, 0.75rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-primary);
  transition: background-color 0.2s ease;
}

.collapsible-header:hover {
  background: var(--border-light, #f1f5f9);
}

.collapsible-content {
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  background: var(--primary-bg);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.collapsible-section.expanded .collapsible-content {
  max-height: 200px;
}

.collapsible-icon {
  transition: transform 0.3s ease;
  font-size: var(--text-xs, 0.75rem);
}

.collapsible-section.expanded .collapsible-icon {
  transform: rotate(180deg);
}

/* ==========================================================================
   COMPACT BUTTON STYLES
   ========================================================================== */

.btn-compact {
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
  line-height: 1.2;
  border-radius: var(--radius-sm, 0.25rem);
  border: 1px solid var(--border-color);
  background: var(--primary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-compact:hover {
  background: var(--tertiary-bg);
  border-color: var(--border-hover);
}

.btn-compact.primary {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.btn-compact.primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

/* ==========================================================================
   FORM STYLES - CONSISTENT WITH OTHER PANELS
   ========================================================================== */

.form-group {
  margin-bottom: var(--space-3, 0.75rem);
}

.form-label {
  display: block;
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-primary);
  margin-bottom: var(--space-1, 0.25rem);
}

.form-input {
  width: 100%;
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  font-size: var(--text-sm, 0.875rem);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md, 0.375rem);
  background: var(--primary-bg);
  color: var(--text-primary);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

.form-textarea {
  width: 100%;
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  font-size: var(--text-sm, 0.875rem);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md, 0.375rem);
  background: var(--primary-bg);
  color: var(--text-primary);
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

/* UNIFIED FORM STYLES - ALWAYS MODERN */
.form-input,
.form-textarea {
  /* Always use modern glass design */
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.form-input:focus,
.form-textarea:focus {
  border-color: #4A9EFF;
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Light theme form adjustments */
.light .form-input,
.light .form-textarea {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

.light .form-input:focus,
.light .form-textarea:focus {
  border-color: #4A9EFF;
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2);
}

.light .form-input::placeholder,
.light .form-textarea::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

/* ==========================================================================
   COMPACT INPUT STYLES
   ========================================================================== */

.input-compact {
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm, 0.25rem);
  background: var(--primary-bg);
  color: var(--text-primary);
  width: 100%;
}

.input-compact:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

.select-compact {
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm, 0.25rem);
  background: var(--primary-bg);
  color: var(--text-primary);
  cursor: pointer;
}

/* ==========================================================================
   PROGRESS AND STATUS INDICATORS - COMPACT
   ========================================================================== */

.progress-compact {
  height: 4px;
  background: var(--border-light, #f1f5f9);
  border-radius: 2px;
  overflow: hidden;
  margin: var(--space-1, 0.25rem) 0;
}

.progress-compact .progress-bar {
  height: 100%;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.status-compact {
  display: flex;
  align-items: center;
  gap: var(--space-1, 0.25rem);
  font-size: var(--text-xs, 0.75rem);
  padding: var(--space-1, 0.25rem) 0;
}

.status-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-icon.success {
  background: var(--success-color, #10b981);
}

.status-icon.error {
  background: var(--error-color, #ef4444);
}

.status-icon.warning {
  background: var(--warning-color, #f59e0b);
}

.status-icon.info {
  background: var(--info-color, #3b82f6);
}

/* ==========================================================================
   DRAG AND DROP ZONE - COMPACT
   ========================================================================== */

.drop-zone-compact {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md, 0.375rem);
  padding: var(--space-3, 0.75rem);
  text-align: center;
  background: var(--tertiary-bg, #f8fafc);
  transition: all 0.2s ease;
  margin-bottom: var(--space-2, 0.5rem);
}

.drop-zone-compact.active {
  border-color: var(--accent-color);
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.05);
}

.drop-zone-text {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary);
  margin: 0;
}

/* ==========================================================================
   RESPONSIVE PANEL HEIGHTS
   ========================================================================== */

/* Viewport height management */
@media (max-height: 800px) {
  .import-section {
    padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  }

  .section-description {
    font-size: 11px;
    line-height: 1.3;
  }

  .collapsible-section.expanded .collapsible-content {
    max-height: 150px;
  }
}

@media (max-height: 700px) {
  .import-section {
    padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem);
  }

  .section-title {
    font-size: 11px;
    margin-bottom: var(--space-1, 0.25rem);
  }

  .section-description {
    font-size: 10px;
    margin-bottom: var(--space-2, 0.5rem);
  }

  .collapsible-section.expanded .collapsible-content {
    max-height: 120px;
  }
}

/* ==========================================================================
   SCROLLABLE CONTENT AREAS (ONLY WHERE NECESSARY)
   ========================================================================== */

.scrollable-content {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid var(--border-light, #f1f5f9);
  border-radius: var(--radius-sm, 0.25rem);
  padding: var(--space-2, 0.5rem);
  background: var(--primary-bg);
}

.scrollable-content::-webkit-scrollbar {
  width: 4px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: var(--border-light, #f1f5f9);
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Spacing utilities */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: var(--space-1, 0.25rem) !important;
}

.mb-2 {
  margin-bottom: var(--space-2, 0.5rem) !important;
}

.mb-3 {
  margin-bottom: var(--space-3, 0.75rem) !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: var(--space-1, 0.25rem) !important;
}

.p-2 {
  padding: var(--space-2, 0.5rem) !important;
}

.p-3 {
  padding: var(--space-3, 0.75rem) !important;
}

/* Flexbox utilities */
.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row !important;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

/* ==========================================================================
   ANIMATIONS
   ========================================================================== */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}