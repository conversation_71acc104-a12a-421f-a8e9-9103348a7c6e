/* ==========================================================================
   ENHANCED SEARCH INPUT - Mobile-First Responsive Design
   Optimized for Touch Interactions and Accessibility
   ========================================================================== */

/* Import responsive foundation */
@import '../styles/responsive-foundation.css';

/* ==========================================================================
   BASE STYLES (320px+)
   ========================================================================== */

.enhanced-search-input {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.enhanced-search-input__form {
  width: 100%;
}

.enhanced-search-input__container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--input-bg, #ffffff);
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.enhanced-search-input--focused .enhanced-search-input__container {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.enhanced-search-input--disabled .enhanced-search-input__container {
  background: var(--disabled-bg, #f9fafb);
  border-color: var(--disabled-border, #d1d5db);
  opacity: 0.6;
  cursor: not-allowed;
}

/* ==========================================================================
   SEARCH ICON
   ========================================================================== */

.enhanced-search-input__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-sm);
  color: var(--icon-color, #6b7280);
  flex-shrink: 0;
}

.enhanced-search-input__icon svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.enhanced-search-input__spinner {
  animation: spin 1s linear infinite;
}

.enhanced-search-input__spinner svg {
  color: var(--primary-color, #3b82f6);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ==========================================================================
   INPUT FIELD
   ========================================================================== */

.enhanced-search-input__field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  padding: var(--spacing-sm) var(--spacing-xs);
  font-size: var(--text-base);
  color: var(--text-color, #374151);
  min-width: 0;
  height: var(--touch-target-comfortable);
  touch-action: manipulation;
  user-select: text;
  -webkit-tap-highlight-color: transparent;
  line-height: 1.5;
   will-change: transform, opacity;
 }

.enhanced-search-input__field::placeholder {
  color: var(--placeholder-color, #9ca3af);
}

.enhanced-search-input__field:disabled {
  cursor: not-allowed;
}

/* ==========================================================================
   CLEAR BUTTON
   ========================================================================== */

.enhanced-search-input__clear {
  background: none;
  border: none;
  color: var(--icon-color, #6b7280);
  padding: var(--spacing-xs);
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: var(--spacing-xs);
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: var(--touch-target-minimum);
  min-height: var(--touch-target-minimum);
}

.enhanced-search-input__clear:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--text-color, #374151);
}

.enhanced-search-input__clear:active {
  background: var(--active-bg, #e5e7eb);
  transform: scale(0.95);
}

.enhanced-search-input__clear svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* ==========================================================================
   SUBMIT BUTTON
   ========================================================================== */

.enhanced-search-input__submit {
  background: var(--primary-color, #3b82f6);
  color: white;
  border: none;
  padding: 0 var(--spacing-sm);
  height: calc(var(--touch-target-comfortable) - 4px);
  margin: 2px;
  border-radius: 6px;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  flex-shrink: 0;
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-height: var(--touch-target-minimum);
  line-height: 1.5;
}

.enhanced-search-input__submit:hover:not(:disabled) {
  background: var(--primary-hover, #2563eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.enhanced-search-input__submit:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.enhanced-search-input__submit:disabled {
  background: var(--disabled-bg, #d1d5db);
  color: var(--disabled-text, #9ca3af);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.enhanced-search-input__submit-text {
  display: none;
}

.enhanced-search-input__submit-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* ==========================================================================
   DROPDOWN SUGGESTIONS
   ========================================================================== */

.enhanced-search-input__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--dropdown-bg, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin-top: var(--spacing-xs);
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.enhanced-search-input__section {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.enhanced-search-input__section:last-child {
  border-bottom: none;
}

.enhanced-search-input__section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-secondary, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.enhanced-search-input__clear-recent {
  background: none;
  border: none;
  color: var(--primary-color, #3b82f6);
  font-size: var(--text-xs);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.enhanced-search-input__clear-recent:hover {
  background: var(--hover-bg, #f3f4f6);
}

/* ==========================================================================
   DROPDOWN ITEMS
   ========================================================================== */

.enhanced-search-input__item {
  width: 100%;
  background: none;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-height: var(--touch-target-min);
  font-size: var(--text-sm);
  color: var(--text-color, #374151);
}

.enhanced-search-input__item:hover,
.enhanced-search-input__item--selected {
  background: var(--hover-bg, #f3f4f6);
  color: var(--primary-color, #3b82f6);
}

.enhanced-search-input__item:active {
  background: var(--active-bg, #e5e7eb);
  transform: scale(0.98);
}

.enhanced-search-input__item-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.enhanced-search-input__item-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.enhanced-search-input__item-label {
  font-size: var(--text-xs);
  color: var(--text-secondary, #6b7280);
  background: var(--label-bg, #f3f4f6);
  padding: 2px var(--spacing-xs);
  border-radius: 4px;
  flex-shrink: 0;
}

.enhanced-search-input__item--recent .enhanced-search-input__item-icon {
  opacity: 0.7;
}

.enhanced-search-input__item--suggestion .enhanced-search-input__item-icon {
  color: var(--primary-color, #3b82f6);
}

.enhanced-search-input__item--bookmark .enhanced-search-input__item-icon {
  color: var(--success-color, #10b981);
}

/* No Results */
.enhanced-search-input__no-results {
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;
  color: var(--text-secondary, #6b7280);
  font-size: var(--text-sm);
  font-style: italic;
}

/* ==========================================================================
   SIZE VARIANTS
   ========================================================================== */

/* Small Size */
.enhanced-search-input--small .enhanced-search-input__field {
  height: var(--touch-target-min);
  font-size: var(--text-sm);
  padding: var(--spacing-xs);
}

.enhanced-search-input--small .enhanced-search-input__submit {
  height: calc(var(--touch-target-min) - 4px);
  padding: 0 var(--spacing-sm);
  font-size: var(--text-xs);
}

.enhanced-search-input--small .enhanced-search-input__icon svg {
  width: 16px;
  height: 16px;
}

.enhanced-search-input--small .enhanced-search-input__clear svg {
  width: 14px;
  height: 14px;
}

/* Large Size */
.enhanced-search-input--large .enhanced-search-input__field {
  height: var(--touch-target-large);
  font-size: var(--text-lg);
  padding: var(--spacing-md) var(--spacing-sm);
}

.enhanced-search-input--large .enhanced-search-input__submit {
  height: calc(var(--touch-target-large) - 4px);
  padding: 0 var(--spacing-lg);
  font-size: var(--text-base);
}

.enhanced-search-input--large .enhanced-search-input__icon svg {
  width: 20px;
  height: 20px;
}

.enhanced-search-input--large .enhanced-search-input__clear svg {
  width: 18px;
  height: 18px;
}

/* ==========================================================================
   VARIANT STYLES
   ========================================================================== */

/* Compact Variant */
.enhanced-search-input--compact .enhanced-search-input__container {
  border-radius: 20px;
}

.enhanced-search-input--compact .enhanced-search-input__submit {
  border-radius: 16px;
}

/* Expanded Variant */
.enhanced-search-input--expanded .enhanced-search-input__container {
  border-radius: 12px;
  padding: var(--spacing-xs);
}

.enhanced-search-input--expanded .enhanced-search-input__field {
  padding: var(--spacing-md);
}

.enhanced-search-input--expanded .enhanced-search-input__submit {
  padding: 0 var(--spacing-lg);
  border-radius: 8px;
}

/* ==========================================================================
   ENHANCED MOBILE (480px+)
   ========================================================================== */

@media (min-width: 480px) {
  .enhanced-search-input__field {
    height: var(--touch-target-large);
    font-size: var(--text-lg);
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .enhanced-search-input__submit {
    height: calc(var(--touch-target-large) - 4px);
    padding: 0 var(--spacing-md);
  }

  .enhanced-search-input__submit-text {
    display: inline;
  }

  .enhanced-search-input__icon svg {
    width: 20px;
    height: 20px;
  }

  .enhanced-search-input__clear svg {
    width: 18px;
    height: 18px;
  }

  .enhanced-search-input__dropdown {
    max-height: 400px;
  }

  .enhanced-search-input__item {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--text-base);
  }
}

/* ==========================================================================
   SMALL TABLET (640px+)
   ========================================================================== */

@media (min-width: 640px) {
  .enhanced-search-input__container {
    border-radius: 10px;
  }

  .enhanced-search-input__submit {
    padding: 0 var(--spacing-lg);
    font-size: var(--text-base);
  }

  .enhanced-search-input__dropdown {
    border-radius: 10px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

/* ==========================================================================
   TABLET (768px+)
   ========================================================================== */

@media (min-width: 768px) {
  .enhanced-search-input__field {
    font-size: var(--text-lg);
  }

  .enhanced-search-input__submit {
    padding: 0 var(--spacing-xl);
  }

  .enhanced-search-input__dropdown {
    max-height: 500px;
  }

  .enhanced-search-input__item {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

/* ==========================================================================
   DESKTOP (1024px+)
   ========================================================================== */

@media (min-width: 1024px) {
  .enhanced-search-input__container {
    border-radius: 12px;
  }

  .enhanced-search-input__field {
    font-size: var(--text-xl);
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .enhanced-search-input__submit {
    padding: 0 var(--spacing-2xl);
    font-size: var(--text-lg);
  }

  .enhanced-search-input__icon svg {
    width: 22px;
    height: 22px;
  }

  .enhanced-search-input__clear svg {
    width: 20px;
    height: 20px;
  }

  .enhanced-search-input__dropdown {
    border-radius: 12px;
  }
}

/* ==========================================================================
   DARK MODE SUPPORT
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  .enhanced-search-input {
    --input-bg: #374151;
    --border-color: #4b5563;
    --text-color: #f9fafb;
    --placeholder-color: #9ca3af;
    --icon-color: #9ca3af;
    --hover-bg: #4b5563;
    --active-bg: #6b7280;
    --dropdown-bg: #374151;
    --border-light: #4b5563;
    --text-secondary: #d1d5db;
    --label-bg: #4b5563;
    --disabled-bg: #4b5563;
    --disabled-border: #6b7280;
    --disabled-text: #9ca3af;
  }
}

/* ==========================================================================
   HIGH CONTRAST MODE
   ========================================================================== */

@media (prefers-contrast: high) {
  .enhanced-search-input__container {
    border-width: 3px;
  }

  .enhanced-search-input__submit,
  .enhanced-search-input__clear {
    border: 2px solid currentColor;
  }

  .enhanced-search-input__item {
    border-bottom: 1px solid var(--border-color);
  }

  .enhanced-search-input__dropdown {
    border-width: 2px;
  }
}

/* ==========================================================================
   REDUCED MOTION
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .enhanced-search-input__container,
  .enhanced-search-input__submit,
  .enhanced-search-input__clear,
  .enhanced-search-input__item,
  .enhanced-search-input__clear-recent {
    transition: none;
  }

  .enhanced-search-input__submit:hover,
  .enhanced-search-input__submit:active,
  .enhanced-search-input__clear:active,
  .enhanced-search-input__item:active {
    transform: none;
  }

  .enhanced-search-input__spinner {
    animation: none;
  }
}

/* ==========================================================================
   FOCUS INDICATORS
   ========================================================================== */

.enhanced-search-input__field:focus {
  outline: none;
}

.enhanced-search-input__submit:focus,
.enhanced-search-input__clear:focus,
.enhanced-search-input__item:focus,
.enhanced-search-input__clear-recent:focus {
  outline: 2px solid var(--focus-color, #3b82f6);
  outline-offset: 2px;
}

/* ==========================================================================
   ACCESSIBILITY ENHANCEMENTS
   ========================================================================== */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading state for async suggestions */
.enhanced-search-input__loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  color: var(--text-secondary, #6b7280);
  font-size: 14px;
  border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.enhanced-search-input__spinner {
  width: 16px;
  height: 16px;
  color: var(--primary-color, #3b82f6);
}

.enhanced-search-input__spinner svg {
  width: 100%;
  height: 100%;
}

/* Keyboard shortcuts help dialog */
.enhanced-search-input__shortcuts-help {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--modal-bg, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 1000;
  max-width: 400px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.enhanced-search-input__shortcuts-content {
  padding: 24px;
  position: relative;
}

.enhanced-search-input__shortcuts-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

.enhanced-search-input__shortcuts-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.enhanced-search-input__shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.enhanced-search-input__shortcut-key {
  background: var(--kbd-bg, #f3f4f6);
  border: 1px solid var(--kbd-border, #d1d5db);
  border-radius: 4px;
  padding: 4px 8px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  font-weight: 500;
  color: var(--kbd-text, #374151);
  white-space: nowrap;
  min-width: fit-content;
}

.enhanced-search-input__shortcut-desc {
  flex: 1;
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  text-align: right;
}

.enhanced-search-input__shortcuts-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.enhanced-search-input__shortcuts-close:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--text-primary, #111827);
}

.enhanced-search-input__shortcuts-close:focus {
  outline: 2px solid var(--focus-color, #3b82f6);
  outline-offset: 2px;
}

/* Enhanced focus indicators for accessibility */
.enhanced-search-input__item[aria-selected="true"] {
  background: var(--selected-bg, #eff6ff);
  border-left: 3px solid var(--primary-color, #3b82f6);
}

.enhanced-search-input__item:focus-visible {
  outline: 2px solid var(--focus-color, #3b82f6);
  outline-offset: -2px;
  background: var(--focus-bg, #f0f9ff);
}

/* ... existing code ... */