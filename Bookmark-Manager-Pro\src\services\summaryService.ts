import { Bookmark } from '../../types';
import { geminiService } from './geminiService'; // Assuming existing

export interface SummaryResult {
  quickSummary: string
  detailedSummary?: string
  keyPoints?: string[]
  contentType: 'webpage' | 'youtube' | 'github' | 'documentation' | 'article' | 'tutorial' | 'ai-tool' | 'design-tool' | 'dev-tool' | 'productivity-tool' | 'community' | 'unknown'
  extractedData?: {
    title?: string
    author?: string
    publishDate?: string
    readingTime?: string
    wordCount?: number
    language?: string
  }
  youtubeData?: {
    videoId: string
    title: string
    channelTitle: string
    duration: string
    viewCount: string
    publishedAt: string
    thumbnailUrl: string
    transcript?: string
  }
  error?: string
}

export class SummaryService {
  private static instance: SummaryService
  private cache = new Map<string, SummaryResult>()

  static getInstance(): SummaryService {
    if (!SummaryService.instance) {
      SummaryService.instance = new SummaryService()
    }
    return SummaryService.instance
  }

  async generateSummary(bookmark: Bookmark): Promise<SummaryResult> {
    // Check cache first
    const cached = this.cache.get(bookmark.url)
    if (cached) {
      return cached
    }

    try {
      const contentType = this.detectContentType(bookmark.url)
      let result: SummaryResult

      switch (contentType) {
        case 'youtube':
          result = await this.processYouTubeContent(bookmark)
          break
        case 'github':
          result = await this.processGitHubContent(bookmark)
          break
        default:
          result = await this.processWebContent(bookmark)
      }

      // Cache the result
      this.cache.set(bookmark.url, result)
      return result

    } catch (error) {
      const errorResult: SummaryResult = {
        quickSummary: this.generateFallbackSummary(bookmark),
        contentType: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return errorResult
    }
  }

  private detectContentType(url: string): SummaryResult['contentType'] {
    const domain = new URL(url).hostname.toLowerCase()
    
    if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
      return 'youtube'
    }
    if (domain.includes('github.com')) {
      return 'github'
    }
    if (domain.includes('docs.') || url.includes('/docs/') || url.includes('/documentation/')) {
      return 'documentation'
    }
    if (domain.includes('medium.com') || domain.includes('dev.to') || domain.includes('blog.')) {
      return 'article'
    }
    
    return 'webpage'
  }

  private async processYouTubeContent(bookmark: Bookmark): Promise<SummaryResult> {
    const videoId = this.extractYouTubeVideoId(bookmark.url)
    if (!videoId) {
      throw new Error('Invalid YouTube URL')
    }

    // Simulate API call to YouTube Data API
    const youtubeData = await this.fetchYouTubeData(videoId)
    
    const quickSummary = `${youtubeData.title} by ${youtubeData.channelTitle} - ${this.formatDuration(youtubeData.duration)}`
    
    return {
      quickSummary,
      detailedSummary: await this.generateDetailedYouTubeSummary(youtubeData),
      keyPoints: await this.extractYouTubeKeyPoints(youtubeData),
      contentType: 'youtube',
      youtubeData,
      extractedData: {
        title: youtubeData.title,
        author: youtubeData.channelTitle,
        publishDate: youtubeData.publishedAt
      }
    }
  }

  private async processGitHubContent(bookmark: Bookmark): Promise<SummaryResult> {
    const repoInfo = this.parseGitHubUrl(bookmark.url)
    if (!repoInfo) {
      throw new Error('Invalid GitHub URL')
    }

    // Simulate GitHub API call
    const repoData = await this.fetchGitHubData(repoInfo)
    
    const quickSummary = `${repoData.name}: ${repoData.description || 'GitHub repository'}`
    
    return {
      quickSummary,
      detailedSummary: this.generateGitHubDetailedSummary(repoData),
      keyPoints: this.extractGitHubKeyPoints(repoData),
      contentType: 'github',
      extractedData: {
        title: repoData.name,
        author: repoData.owner,
        language: repoData.language
      }
    }
  }

  private async processWebContent(bookmark: Bookmark): Promise<SummaryResult> {
    // Simulate web scraping and content analysis
    const content = await this.fetchWebContent(bookmark.url)
    
    const quickSummary = this.generateQuickSummary(content, bookmark)
    const detailedSummary = await this.generateDetailedSummary(content)
    const keyPoints = this.extractKeyPoints(content)
    
    return {
      quickSummary,
      detailedSummary,
      keyPoints,
      contentType: this.detectContentType(bookmark.url),
      extractedData: {
        title: content.title,
        author: content.author,
        publishDate: content.publishDate,
        readingTime: content.readingTime,
        wordCount: content.wordCount,
        language: content.language
      }
    }
  }

  private generateFallbackSummary(bookmark: Bookmark): string {
    const domain = new URL(bookmark.url).hostname
    const title = bookmark.title || 'Untitled'
    
    // Generate a basic summary based on URL and title
    if (domain.includes('github.com')) {
      return `GitHub repository: ${title}`
    }
    if (domain.includes('youtube.com')) {
      return `YouTube video: ${title}`
    }
    if (domain.includes('docs.') || bookmark.url.includes('/docs/')) {
      return `Documentation: ${title}`
    }
    
    return `${title} - Content from ${domain}`
  }

  private extractYouTubeVideoId(url: string): string | null {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }
    
    return null
  }

  private async fetchYouTubeData(videoId: string) {
    // Simulate YouTube Data API response
    return {
      videoId,
      title: 'Sample YouTube Video Title',
      channelTitle: 'Channel Name',
      duration: 'PT10M30S',
      viewCount: '1000000',
      publishedAt: '2024-01-15T10:00:00Z',
      thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
      description: 'Sample video description...',
      transcript: 'Sample transcript content...'
    }
  }

  private parseGitHubUrl(url: string) {
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/)
    if (!match) return null
    
    return {
      owner: match[1],
      repo: match[2]
    }
  }

  private async fetchGitHubData(repoInfo: { owner: string; repo: string }) {
    // Simulate GitHub API response
    return {
      name: repoInfo.repo,
      description: 'Sample repository description',
      owner: repoInfo.owner,
      language: 'TypeScript',
      stars: 1000,
      forks: 100,
      topics: ['react', 'typescript', 'web'],
      readme: 'Sample README content...'
    }
  }

  private async fetchWebContent(url: string) {
    // Simulate web scraping for the given URL
    console.log(`Fetching content from: ${url}`)
    return {
      title: 'Sample Web Page Title',
      author: 'Author Name',
      publishDate: '2024-01-15',
      content: 'Sample web page content...',
      readingTime: '5 min read',
      wordCount: 1200,
      language: 'en',
      metaDescription: 'Sample meta description'
    }
  }

  private generateQuickSummary(content: any, bookmark: Bookmark): string {
    // Generate a concise 1-sentence summary
    const domain = new URL(bookmark.url).hostname
    const title = content.title || bookmark.title
    
    if (content.metaDescription) {
      return content.metaDescription.length > 100 
        ? content.metaDescription.substring(0, 97) + '...'
        : content.metaDescription
    }
    
    return `${title} - ${content.readingTime || 'Quick read'} from ${domain}`
  }

  private async generateDetailedSummary(content: any): Promise<string> {
    // Simulate AI-powered detailed summary generation using content analysis
    console.log(`Generating detailed summary for content: ${content.title || 'Untitled'}`)
    return `This is a detailed summary of the content. It provides comprehensive insights into the main topics, key arguments, and important information presented in the article. The content covers various aspects and provides valuable information for readers interested in the subject matter.`
  }

  private extractKeyPoints(content: any): string[] {
    // Simulate key point extraction based on content analysis
    console.log(`Extracting key points from content: ${content.title || 'Untitled'} (${content.wordCount || 0} words)`)
    return [
      'Key insight about the main topic',
      'Important technical detail or concept',
      'Practical application or use case',
      'Notable conclusion or recommendation'
    ]
  }

  private async generateDetailedYouTubeSummary(youtubeData: any): Promise<string> {
    return `This ${this.formatDuration(youtubeData.duration)} video by ${youtubeData.channelTitle} provides comprehensive coverage of the topic. The content includes detailed explanations, practical examples, and valuable insights. With ${this.formatViewCount(youtubeData.viewCount)} views, this video has proven valuable to the community.`
  }

  private extractYouTubeKeyPoints(youtubeData: any): string[] {
    // Extract key points based on YouTube video data
    console.log(`Extracting key points from YouTube video: ${youtubeData.title} (${youtubeData.duration})`)
    return [
      'Introduction and overview of the topic',
      'Main concepts and explanations',
      'Practical examples and demonstrations',
      'Key takeaways and conclusions'
    ]
  }

  private generateGitHubDetailedSummary(repoData: any): string {
    return `${repoData.name} is a ${repoData.language} repository by ${repoData.owner}. ${repoData.description} The project has gained ${repoData.stars} stars and ${repoData.forks} forks, indicating strong community interest and adoption.`
  }

  private extractGitHubKeyPoints(repoData: any): string[] {
    return [
      `Primary language: ${repoData.language}`,
      `Community engagement: ${repoData.stars} stars, ${repoData.forks} forks`,
      `Topics: ${repoData.topics.join(', ')}`,
      'Active development and maintenance'
    ]
  }

  private formatDuration(duration: string): string {
    // Convert PT10M30S to "10:30"
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return duration
    
    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  private formatViewCount(count: string): string {
    const num = parseInt(count)
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return count
  }

  clearCache(): void {
    this.cache.clear()
  }

  // Additional AI-powered methods
  async categorizeContent(content: string): Promise<string> {
    try {
      return await geminiService.analyzeContent(content, 'categorization')
    } catch (error) {
      console.error('Content categorization failed:', error)
      return 'Uncategorized'
    }
  }

  async generateAutoPlaylist(bookmarks: Bookmark[], preferences?: any): Promise<any> {
    try {
      const response = await geminiService.generatePlaylistRecommendations(bookmarks, preferences)
      return JSON.parse(response)
    } catch (error) {
      console.error('Auto playlist generation failed:', error)
      return {
        recommendations: ['Group by content type', 'Organize by topic'],
        categories: ['General'],
        estimatedTime: '1 hour'
      }
    }
  }

  async getSmartRecommendations(playlist: any): Promise<string> {
    try {
      const prompt = `Based on this playlist, recommend similar content: ${JSON.stringify(playlist)}`
      return await geminiService.generateContent(prompt)
    } catch (error) {
      console.error('Smart recommendations failed:', error)
      return 'No recommendations available'
    }
  }

  async optimizePlaylistOrder(playlist: any): Promise<any> {
    try {
      const prompt = `Optimize the order of items in this playlist for better flow and learning progression: ${JSON.stringify(playlist)}`
      const response = await geminiService.generateContent(prompt)
      return JSON.parse(response)
    } catch (error) {
      console.error('Playlist optimization failed:', error)
      return playlist // Return original if optimization fails
    }
  }

  async detectDuplicates(items: any[]): Promise<string[]> {
    try {
      const prompt = `Identify potential duplicates in this list: ${JSON.stringify(items)}`
      const response = await geminiService.generateContent(prompt)
      return JSON.parse(response)
    } catch (error) {
      console.error('Duplicate detection failed:', error)
      return []
    }
  }

  async summarizeContent(content: string): Promise<string> {
    try {
      return await geminiService.analyzeContent(content, 'summary')
    } catch (error) {
      console.error('Content summarization failed:', error)
      return 'Summary not available'
    }
  }
}

export const summaryService = SummaryService.getInstance()
