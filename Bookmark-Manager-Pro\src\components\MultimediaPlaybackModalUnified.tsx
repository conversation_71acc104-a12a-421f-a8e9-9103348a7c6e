import {
  Ch<PERSON>ron<PERSON>eft,
  Maximize2,
  Minimize2,
  Pause,
  Play,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  X
} from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { cn } from '../lib/utils'
import '../styles/optimized-panels.css'

// Helper functions for media type detection
const detectMediaType = (url: string): 'video' | 'audio' | 'document' | 'article' | 'image' => {
  const urlLower = url.toLowerCase()

  // Video extensions and patterns
  if (urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)$/i) ||
    urlLower.includes('youtube.com') ||
    urlLower.includes('youtu.be') ||
    urlLower.includes('vimeo.com') ||
    urlLower.includes('twitch.tv')) {
    return 'video'
  }

  // Audio extensions and patterns
  if (urlLower.match(/\.(mp3|wav|ogg|aac|flac|m4a)$/i) ||
    urlLower.includes('spotify.com') ||
    urlLower.includes('soundcloud.com')) {
    return 'audio'
  }

  // Image extensions
  if (urlLower.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i)) {
    return 'image'
  }

  // Document extensions
  if (urlLower.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/i)) {
    return 'document'
  }

  return 'article'
}

const isDirectMediaUrl = (url: string): boolean => {
  const urlLower = url.toLowerCase()
  return urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|mp3|wav|ogg|aac|flac|m4a)$/i) !== null
}

interface MultimediaPlaylistItem {
  id: string
  title: string
  url: string
  type: 'video' | 'audio' | 'document' | 'article' | 'image'
  duration?: number
  thumbnail?: string
}

interface MultimediaPlaylist {
  id: string
  name: string
  items: MultimediaPlaylistItem[]
  playbackSettings: {
    shuffle: boolean
    repeat: 'none' | 'one' | 'all'
    autoPlay: boolean
  }
}

interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  playbackRate: number
  isLoading: boolean
  error: string | null
}

interface MultimediaPlaybackModalProps {
  isOpen: boolean
  onClose: () => void
  playlist: MultimediaPlaylist
  initialItemIndex?: number
}

export const MultimediaPlaybackModal: React.FC<MultimediaPlaybackModalProps> = ({
  isOpen,
  onClose,
  playlist,
  initialItemIndex = 0
}) => {
  const modalRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const [currentItemIndex, setCurrentItemIndex] = useState(initialItemIndex)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showPlaylist, setShowPlaylist] = useState(true)

  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 100,
    isMuted: false,
    playbackRate: 1,
    isLoading: false,
    error: null
  })

  const currentItem = playlist.items[currentItemIndex]

  // Detect actual media type from URL if not properly set
  const actualMediaType = currentItem ? detectMediaType(currentItem.url) : 'article'
  const isVideo = actualMediaType === 'video'
  const isAudio = actualMediaType === 'audio'
  const isDirectMedia = currentItem ? isDirectMediaUrl(currentItem.url) : false

  const formatTime = useCallback((seconds: number): string => {
    if (!seconds || isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }, [])

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }, [onClose])

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen)
  }, [isFullscreen])

  const togglePlayPause = useCallback(async () => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (!mediaElement) return

    try {
      if (playbackState.isPlaying) {
        mediaElement.pause()
      } else {
        await mediaElement.play()
      }
    } catch (error) {
      console.error('Playback error:', error)
      setPlaybackState(prev => ({
        ...prev,
        error: 'Playback failed. Media may not be supported.',
        isPlaying: false
      }))
    }
  }, [isVideo, playbackState.isPlaying])

  const playPrevious = useCallback(() => {
    if (currentItemIndex > 0) {
      setCurrentItemIndex(currentItemIndex - 1)
    }
  }, [currentItemIndex])

  const playNext = useCallback(() => {
    if (currentItemIndex < playlist.items.length - 1) {
      setCurrentItemIndex(currentItemIndex + 1)
    }
  }, [currentItemIndex, playlist.items.length])

  const toggleMute = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      const newMuted = !playbackState.isMuted
      mediaElement.muted = newMuted
      setPlaybackState(prev => ({ ...prev, isMuted: newMuted }))
    }
  }, [isVideo, playbackState.isMuted])

  const setVolume = useCallback((volume: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.volume = volume / 100
      setPlaybackState(prev => ({ ...prev, volume, isMuted: false }))
    }
  }, [isVideo])

  const seekTo = useCallback((time: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.currentTime = time
      setPlaybackState(prev => ({ ...prev, currentTime: time }))
    }
  }, [isVideo])

  const setPlaybackRate = useCallback((rate: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.playbackRate = rate
      setPlaybackState(prev => ({ ...prev, playbackRate: rate }))
    }
  }, [isVideo])

  const handleLoadedMetadata = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      setPlaybackState(prev => ({
        ...prev,
        duration: mediaElement.duration || 0,
        isLoading: false
      }))
    }
  }, [isVideo])

  const handleTimeUpdate = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      setPlaybackState(prev => ({
        ...prev,
        currentTime: mediaElement.currentTime || 0
      }))
    }
  }, [isVideo])

  const handlePlay = useCallback(() => {
    setPlaybackState(prev => ({ ...prev, isPlaying: true }))
  }, [])

  const handlePause = useCallback(() => {
    setPlaybackState(prev => ({ ...prev, isPlaying: false }))
  }, [])

  const handleError = useCallback((e: React.SyntheticEvent<HTMLMediaElement>) => {
    console.error('Media playback error:', e)
    setPlaybackState(prev => ({
      ...prev,
      error: 'Failed to load media. The URL may not be accessible or supported.',
      isLoading: false
    }))
  }, [])

  // Load media when current item changes
  useEffect(() => {
    if (!currentItem) return

    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement && currentItem.url) {
      setPlaybackState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        currentTime: 0,
        duration: 0,
        isPlaying: false
      }))

      // Set the media source
      mediaElement.src = currentItem.url
      mediaElement.load()

      // Set initial volume and playback rate
      mediaElement.volume = playbackState.volume / 100
      mediaElement.muted = playbackState.isMuted
      mediaElement.playbackRate = playbackState.playbackRate
    }
  }, [currentItem, isVideo, isAudio, isDirectMedia, playbackState.volume, playbackState.isMuted, playbackState.playbackRate])

  if (!isOpen) return null

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md"
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          "import-panel multimedia-modal",
          isFullscreen ? "fullscreen" : "windowed"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* UNIFIED HEADER DESIGN */}
        <div className="import-header">
          <h2 className="import-title">
            <Play size={20} />
            🎵 {currentItem?.title || 'Multimedia Player'}
          </h2>
          <button
            onClick={onClose}
            className="close-btn"
            aria-label="Close multimedia player"
          >
            <X size={20} />
          </button>
        </div>

        {/* UNIFIED CONTENT DESIGN */}
        <div className="import-content">
          {/* Media Player Section */}
          <div className="import-section">
            <h3 className="section-title">🎬 Media Player</h3>
            <p className="section-description">
              {currentItem ? `Now playing: ${currentItem.title}` : 'Select a media item from the playlist below'}
            </p>

            <div className="relative overflow-hidden rounded-lg border-2 border-white/10 bg-black/80" style={{ minHeight: '300px' }}>
              {playbackState.error ? (
                <div className="text-center p-8 flex items-center justify-center h-full text-white">
                  <div>
                    <div className="text-red-400 mb-4 text-4xl">⚠️</div>
                    <p className="text-lg mb-2 font-semibold">Playback Error</p>
                    <p className="text-sm text-gray-300">{playbackState.error}</p>
                  </div>
                </div>
              ) : currentItem ? (
                <>
                  {isVideo && isDirectMedia && (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-contain"
                      onLoadedMetadata={handleLoadedMetadata}
                      onTimeUpdate={handleTimeUpdate}
                      onPlay={handlePlay}
                      onPause={handlePause}
                      onError={handleError}
                      controls={false}
                    />
                  )}

                  {isAudio && isDirectMedia && (
                    <div className="text-center text-white p-8 flex items-center justify-center h-full">
                      <div>
                        <audio
                          ref={audioRef}
                          onLoadedMetadata={handleLoadedMetadata}
                          onTimeUpdate={handleTimeUpdate}
                          onPlay={handlePlay}
                          onPause={handlePause}
                          onError={handleError}
                          className="hidden"
                        />
                        <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <Volume2 className="w-12 h-12" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{currentItem.title}</h3>
                        <p className="text-sm opacity-75 truncate max-w-xs">{currentItem.url}</p>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center p-8 flex items-center justify-center h-full text-white">
                  <div>
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-white/10 text-white/50">
                      <Play className="w-8 h-8" />
                    </div>
                    <p className="text-lg font-medium text-white/80">No media selected</p>
                    <p className="text-sm mt-2 text-white/60">Choose a media item from the playlist to start playback</p>
                  </div>
                </div>
              )}
            </div>

            {/* Media Controls Section */}
            <div className="import-section">
              <h3 className="section-title">🎛️ Playback Controls</h3>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-white/70 mb-2">
                  <span>{formatTime(playbackState.currentTime)}</span>
                  <span>{formatTime(playbackState.duration)}</span>
                </div>
                <div
                  className="w-full h-2 bg-white/20 rounded-full cursor-pointer"
                  onClick={(e) => {
                    const rect = e.currentTarget.getBoundingClientRect()
                    const percent = (e.clientX - rect.left) / rect.width
                    seekTo(percent * playbackState.duration)
                  }}>
                  <div
                    className="h-2 rounded-full transition-all bg-blue-400"
                    style={{
                      width: `${playbackState.duration ? (playbackState.currentTime / playbackState.duration) * 100 : 0}%`
                    }}
                  />
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center justify-center gap-3 mb-4">
                <button
                  onClick={playPrevious}
                  disabled={currentItemIndex === 0}
                  className="close-btn disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Previous track"
                  title="Previous track"
                >
                  <SkipBack className="w-5 h-5" />
                </button>

                <button
                  onClick={togglePlayPause}
                  className="format-option flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all bg-blue-500/20 border-blue-400/50 text-blue-400 hover:bg-blue-500/30 hover:border-blue-400"
                  aria-label={playbackState.isPlaying ? "Pause" : "Play"}
                  title={playbackState.isPlaying ? "Pause" : "Play"}
                >
                  {playbackState.isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6" />
                  )}
                </button>

                <button
                  onClick={playNext}
                  disabled={currentItemIndex === playlist.items.length - 1}
                  className="close-btn disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Next track"
                  title="Next track"
                >
                  <SkipForward className="w-5 h-5" />
                </button>
              </div>

              {/* Volume and Speed Controls */}
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <button
                    onClick={toggleMute}
                    className="close-btn"
                    aria-label={playbackState.isMuted ? "Unmute" : "Mute"}
                    title={playbackState.isMuted ? "Unmute" : "Mute"}
                  >
                    {playbackState.isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={playbackState.isMuted ? 0 : playbackState.volume}
                    onChange={(e) => setVolume(Number(e.target.value))}
                    className="w-20 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer"
                    aria-label="Volume"
                    title="Volume"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-white/70">Speed:</span>
                  <select
                    value={playbackState.playbackRate}
                    onChange={(e) => setPlaybackRate(Number(e.target.value))}
                    className="bg-black/30 border-2 border-white/20 text-white/90 px-2 py-1 rounded text-sm"
                    aria-label="Playback speed"
                    title="Playback speed"
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1}>1x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                    <option value={2}>2x</option>
                  </select>
                </div>

                {isVideo && (
                  <button
                    onClick={toggleFullscreen}
                    className="close-btn"
                    aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                    title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                  >
                    {isFullscreen ? (
                      <Minimize2 className="w-4 h-4" />
                    ) : (
                      <Maximize2 className="w-4 h-4" />
                    )}
                  </button>
                )}
              </div>
            </div>

            {/* Playlist Section */}
            {showPlaylist && (
              <div className="import-section">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="section-title">📋 Playlist</h3>
                  <button
                    onClick={() => setShowPlaylist(false)}
                    className="close-btn"
                    aria-label="Hide playlist"
                    title="Hide playlist"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {playlist.items.map((item, index) => (
                    <button
                      key={item.id}
                      onClick={() => setCurrentItemIndex(index)}
                      className={cn(
                        "w-full p-3 rounded-lg border-2 text-left transition-all",
                        index === currentItemIndex
                          ? "bg-blue-500/20 border-blue-400 text-white"
                          : "bg-black/20 border-white/10 text-white/80 hover:bg-white/10 hover:border-white/20"
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          "w-8 h-8 rounded flex items-center justify-center text-xs font-medium",
                          index === currentItemIndex
                            ? "bg-blue-500 text-white"
                            : "bg-white/10 text-gray-300"
                        )}>
                          {index + 1}
                        </div>
                        <div className="flex-1 min-w-0">
                          <span className="font-medium block truncate">
                            {item.title}
                          </span>
                          <small className="block mt-1 text-gray-400">
                            {item.type} • {item.duration ? formatTime(item.duration) : 'Unknown duration'}
                          </small>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {!showPlaylist && (
              <div className="import-section">
                <button
                  onClick={() => setShowPlaylist(true)}
                  className="format-option w-full flex items-center justify-center gap-2"
                >
                  <ChevronLeft className="w-4 h-4 rotate-180" />
                  Show Playlist ({playlist.items.length} items)
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
