# Bookmark Studio - Project Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Quick Start Guide](#quick-start-guide)
3. [Core Development Rules](#core-development-rules)
4. [Emergency Protocols](#emergency-protocols)
5. [Build System](#build-system)
6. [Testing Strategy](#testing-strategy)
7. [Code Quality Standards](#code-quality-standards)
8. [Project Structure](#project-structure)
9. [Development Workflow](#development-workflow)
10. [Troubleshooting Guide](#troubleshooting-guide)

## Project Overview

Bookmark Studio is a modern React/TypeScript application for advanced bookmark management with AI-powered features using Google's Gemini API.

### Technology Stack
- **Frontend**: React 19.1.0, TypeScript
- **Build Tool**: Vite (with esbuild)
- **Testing**: Vitest, Cypress, Testing Library
- **AI Integration**: Google Gemini API
- **State Management**: React Query, Context API
- **Styling**: CSS Modules, Design System

## Quick Start Guide

### Prerequisites
- Node.js (latest LTS)
- npm or yarn
- Google Gemini API key

#### Node.js Installation (If Required)

**For Linux/Unix Systems (Ubuntu/Debian)**

If you encounter PATH issues or need to install Node.js:

1. **Fix PATH Issues**
   ```bash
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
```

2. **Verify sudo works**
   ```bash
sudo --version
```

3. **Install Node.js via snap (recommended)**
   ```bash
sudo snap install node --channel=20/stable --classic
```

4. **Verify installation**
   ```bash
node --version
   npm --version
```

5. **Make PATH permanent**
   ```bash
# Add to bashrc for permanent fix
   echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
```

**For Windows/macOS**
- Download from [nodejs.org](https://nodejs.org/) (LTS version recommended)
- Follow the installer instructions

### Installation
```bash
# 1. Install dependencies
npm install

# 2. Set up environment
# Create .env.local and add:
# GEMINI_API_KEY=your_api_key_here

# 3. Start development server
npm run dev
```

### Available Scripts
```bash
# Development
npm run dev              # Start dev server
npm run build            # Production build
npm run build:check      # Development build for checking
npm run preview          # Preview production build

# Testing
npm run test             # Unit tests with Vitest
npm run test:watch       # Watch mode
npm run test:coverage    # Coverage report
npm run test:e2e         # End-to-end tests
npm run test:component   # Component tests
npm run test:all         # All tests

# Quality
npm run lint             # ESLint check
npm run type-check       # TypeScript info (disabled)
```

## Core Development Rules

### 🚫 CRITICAL: NO TSC RULE
**NEVER use `tsc` command directly for building or type checking in this project.**

#### Why This Rule Exists
- Performance: esbuild (via Vite) is 10-100x faster than tsc
- Reliability: Fewer build failures and hanging processes
- Modern Workflow: Aligned with current frontend best practices
- Unified Tooling: Single tool (Vite) for all build needs

#### Alternatives to TSC
- **Type Checking**: VS Code TypeScript Language Server
- **Building**: Vite with esbuild
- **Linting**: ESLint with TypeScript rules
- **Testing**: Vitest (handles TypeScript natively)

### ⏱️ 20-Second Timeout Rule
**If any build command shows no activity for 20 seconds, immediately stop and escalate.**

#### Timeout Detection Signs
- No output for 20+ seconds
- CPU usage drops to near zero
- No file changes or progress indicators
- Terminal appears frozen

## Emergency Protocols

### Emergency Response Protocol
1. **STOP** hanging process (Ctrl+C)
2. **IMMEDIATELY** proceed to Tier 1 diagnostics
3. **DO NOT** wait longer than 20 seconds for any command
4. **ESCALATE** through tiers if issues persist
5. **DOCUMENT** which tier resolved the issue

### Tier-Based Debugging

#### Tier 1: Quick Diagnostics (0-30 seconds)
```bash
npm run lint         # Check syntax errors
npm run build:check  # Development build (faster)
npm run dev          # Real-time error detection
```

#### Tier 2: Cache and Dependencies (30-60 seconds)
```bash
# Clear Vite cache
rmdir /s /q node_modules\.vite

# Full clean install
rmdir /s /q node_modules
npm install

# Retry build
npm run build
```

#### Tier 3: Advanced Debugging (60-90 seconds)
```bash
# Verbose build with debug info
npm run build -- --debug
npm run build -- --logLevel info

# Memory analysis
npm run build:check
```

#### Tier 4: Component-Level Testing (90-120 seconds)
```bash
# Run tests to isolate issues
npm run test
npm run test:component

# Check individual files in VS Code for red squiggles
```

#### Tier 5: Manual Review (120+ seconds)
1. Check recent Git changes
2. Verify import paths
3. Check type definitions
4. Review dependencies
5. Restart IDE and TypeScript language server

## Build System

### Vite Configuration
- **Compiler**: esbuild (not tsc)
- **Hot Reload**: Instant TypeScript compilation
- **Source Maps**: Enabled for debugging
- **Optimization**: Tree shaking, code splitting

### Build Modes
- **Development**: `npm run dev` - Fast builds, source maps
- **Development Check**: `npm run build:check` - Verify without optimization
- **Production**: `npm run build` - Optimized, minified

### Type Safety Without TSC
- **IDE Integration**: VS Code TypeScript Language Server
- **ESLint Rules**: Type-aware linting
- **Build-time Errors**: Vite/esbuild catches type errors
- **Hot Reload**: Instant feedback during development

## Testing Strategy

### Test Types
1. **Unit Tests**: Vitest for components and utilities
2. **Integration Tests**: Testing Library for user interactions
3. **Component Tests**: Cypress for isolated component testing
4. **E2E Tests**: Cypress for full user workflows
5. **Visual Tests**: Cypress for UI regression testing

### Test Structure
```
src/test/
├── components/          # Component tests
├── fixtures/           # Test data
├── mocks/             # Mock implementations
├── setup.ts           # Test configuration
└── utils.tsx          # Test utilities
```

### Coverage Requirements
- **Minimum**: 80% line coverage
- **Components**: 90% coverage for critical components
- **Services**: 95% coverage for business logic
- **Utilities**: 100% coverage for pure functions

## Code Quality Standards

### TypeScript Guidelines
- **Strict Mode**: Enabled in tsconfig.json
- **No Any**: Avoid `any` type, use proper typing
- **Interfaces**: Prefer interfaces over types for objects
- **Enums**: Use const assertions instead of enums

### React Guidelines
- **Functional Components**: Use function components with hooks
- **Props Interface**: Define props interface for each component
- **Error Boundaries**: Wrap components that might fail
- **Memoization**: Use React.memo for expensive components

### Import/Export Rules
- **Absolute Imports**: Use absolute paths from src/
- **Index Files**: Use index.ts for clean imports
- **Named Exports**: Prefer named exports over default
- **Barrel Exports**: Group related exports in index files

### File Naming Conventions
- **Components**: PascalCase (e.g., `BookmarkItem.tsx`)
- **Utilities**: camelCase (e.g., `exportUtils.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)
- **Types**: PascalCase with .types.ts suffix

## Project Structure

```
Bookmark-Manager-Pro/
├── src/
│   ├── components/          # Reusable UI components
│   ├── contexts/           # React contexts
│   ├── styles/            # Global styles
│   ├── test/              # Test utilities and setup
│   └── types/             # TypeScript type definitions
├── components/             # Legacy component location
├── services/              # Business logic and API calls
├── utils/                 # Utility functions
├── styles/               # Global stylesheets
├── docs/                 # Documentation
├── cypress/              # E2E and component tests
├── coverage/             # Test coverage reports
└── dist/                 # Build output
```

### Component Organization
- **Atomic Design**: Atoms, molecules, organisms pattern
- **Feature-based**: Group related components
- **Shared Components**: Reusable across features
- **Page Components**: Top-level route components

## Development Workflow

### Git Workflow
1. **Feature Branches**: Create from main
2. **Commit Messages**: Conventional commits format
3. **Pre-commit Hooks**: Lint and test before commit
4. **Pull Requests**: Required for main branch

### Code Review Checklist
- [ ] No `tsc` commands in scripts
- [ ] TypeScript errors resolved in IDE
- [ ] Tests pass and coverage maintained
- [ ] ESLint rules followed
- [ ] Performance considerations addressed
- [ ] Accessibility standards met

### Release Process
1. **Version Bump**: Update package.json version
2. **Build Verification**: `npm run build` succeeds
3. **Test Suite**: All tests pass
4. **Documentation**: Update relevant docs
5. **Deploy**: Production deployment

## Troubleshooting Guide

### Common Issues

#### Build Hangs or Fails
1. **Check for TSC usage**: Ensure no `tsc` commands
2. **Clear caches**: Delete node_modules/.vite
3. **Verify imports**: Check for circular dependencies
4. **IDE restart**: Restart VS Code and TypeScript server

#### Type Errors
1. **IDE first**: Check VS Code for red squiggles
2. **ESLint**: Run `npm run lint`
3. **Build check**: Use `npm run build:check`
4. **Manual review**: Check type definitions

#### Test Failures
1. **Isolation**: Run individual test files
2. **Mocks**: Verify mock implementations
3. **Environment**: Check test environment setup
4. **Dependencies**: Ensure test dependencies installed

#### Performance Issues
1. **Bundle analysis**: Check build output sizes
2. **Component profiling**: Use React DevTools
3. **Memory leaks**: Check for cleanup in useEffect
4. **Lazy loading**: Implement code splitting

### Emergency Contacts
- **Build Issues**: Check Vite documentation
- **TypeScript Issues**: VS Code TypeScript troubleshooting
- **Test Issues**: Vitest and Cypress documentation
- **Performance**: React DevTools and browser profiling

### Useful Commands
```bash
# Clear all caches
rmdir /s /q node_modules\.vite
rmdir /s /q node_modules
npm install

# Debug build
npm run build -- --debug --logLevel info

# Test specific file
npm run test -- BookmarkItem.test.tsx

# Coverage for specific file
npm run test:coverage -- --reporter=text
```

## Success Metrics

### Build Performance
- **Development build**: < 5 seconds
- **Production build**: < 20 seconds
- **Hot reload**: < 1 second
- **Test execution**: < 30 seconds

### Code Quality
- **TypeScript errors**: 0
- **ESLint errors**: 0
- **Test coverage**: > 80%
- **Bundle size**: < 1MB gzipped

### Developer Experience
- **Setup time**: < 5 minutes
- **Build reliability**: > 99%
- **IDE responsiveness**: Real-time error detection
- **Documentation coverage**: 100% of public APIs

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: Development Team

> 🚨 **Remember**: Never use `tsc` directly, always follow the 20-second rule, and escalate through debugging tiers when issues persist!
                <div className={cn(
                  "text-center p-8 flex items-center justify-center h-full",
                  isModernTheme ? "text-white" : "text-white"
                )}>
                  <div>
                    <div className="text-red-400 mb-4 text-4xl">⚠️</div>
                    <p className="text-lg mb-2 font-semibold">Playback Error</p>
                    <p className={cn(
                      "text-sm",
                      isModernTheme ? "text-gray-300" : "text-gray-200"
                    )}>{playbackState.error}</p>
                  </div>
                </div>
              ) : currentItem ? (
                <>
                  {isVideo && isDirectMedia && (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-contain"
                      onLoadedMetadata={handleLoadedMetadata}
                      onTimeUpdate={handleTimeUpdate}
                      onPlay={handlePlay}
                      onPause={handlePause}
                      onError={handleError}
                      controls={false}
                    />
                  )}

                  {isAudio && isDirectMedia && (
                    <div className="text-center text-white p-8 flex items-center justify-center h-full">
                      <div>
                        <audio
                          ref={audioRef}
                          onLoadedMetadata={handleLoadedMetadata}
                          onTimeUpdate={handleTimeUpdate}
                          onPlay={handlePlay}
                          onPause={handlePause}
                          onError={handleError}
                          className="hidden"
                        />
                        <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <Volume2 className="w-12 h-12" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{currentItem.title}</h3>
                        <p className="text-sm opacity-75 truncate max-w-xs">{currentItem.url}</p>
                      </div>
                    </div>
                  )}

                  {!isDirectMedia && (
                    <div className="text-center text-white p-8 flex items-center justify-center h-full">
                      <div>
                        <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                          <span className="text-3xl">🔗</span>
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{currentItem.title}</h3>
                        <p className="text-sm opacity-75 mb-4 max-w-xs mx-auto">
                          This content cannot be played directly. Click below to open in a new tab.
                        </p>
                        <button
                          onClick={() => window.open(currentItem.url, '_blank')}
                          className="px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg text-white font-medium transition-colors"
                        >
                          Open in New Tab
                        </button>
                      </div>
                    </div>
                  )}

                  {playbackState.isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                      <div className="text-white text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                        <p>Loading...</p>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className={cn(
                  "text-center p-8 flex items-center justify-center h-full",
                  isModernTheme ? "text-white" : "text-white"
                )}>
                  <div>
                    <div className={cn(
                      "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                      isModernTheme
                        ? "bg-white/10 text-white/50"
                        : "bg-gray-700 text-gray-400"
                    )}>
                      <Play className="w-8 h-8" />
                    </div>
                    <p className={cn(
                      "text-lg font-medium",
                      isModernTheme ? "text-white/80" : "text-gray-200"
                    )}>No media selected</p>
                    <p className={cn(
                      "text-sm mt-2",
                      isModernTheme ? "text-white/60" : "text-gray-400"
                    )}>Choose a media item from the playlist to start playback</p>
                  </div>
                </div>
              )}
            </div>

            {/* Media Controls */}
            <div className="import-section">
              <h3 className="section-title">🎛️ Playback Controls</h3>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className={cn(
                  "flex justify-between text-sm mb-2",
                  isModernTheme ? "text-gray-300" : "text-gray-600"
                )}>
                  <span>{formatTime(playbackState.currentTime)}</span>
                  <span>{formatTime(playbackState.duration)}</span>
                </div>
                <div className={cn(
                  "w-full rounded-full h-2 cursor-pointer border",
                  isModernTheme
                    ? "bg-white/10 border-white/20"
                    : "bg-gray-200 border-gray-300"
                )}
                  onClick={(e) => {
                    const rect = e.currentTarget.getBoundingClientRect()
                    const percent = (e.clientX - rect.left) / rect.width
                    seekTo(percent * playbackState.duration)
                  }}>
                  <div
                    className={cn(
                      "h-2 rounded-full transition-all",
                      isModernTheme ? "bg-blue-400" : "bg-blue-500"
                    )}
                    style={{
                      width: `${playbackState.duration ? (playbackState.currentTime / playbackState.duration) * 100 : 0}%`
                    }}
                  />
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center justify-center gap-3 mb-4">
                <button
                  onClick={playPrevious}
                  disabled={currentItemIndex === 0}
                  className={cn(
                    "close-btn",
                    "disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                  aria-label="Previous track"
                  title="Previous track"
                >
                  <SkipBack className="w-5 h-5" />
                </button>

                <button
                  onClick={togglePlayPause}
                  className={cn(
                    "format-option flex items-center justify-center",
                    "w-12 h-12 rounded-full border-2 transition-all",
                    isModernTheme
                      ? "bg-blue-500/20 border-blue-400/50 text-blue-400 hover:bg-blue-500/30 hover:border-blue-400"
                      : "bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300"
                  )}
                  aria-label={playbackState.isPlaying ? "Pause" : "Play"}
                  title={playbackState.isPlaying ? "Pause" : "Play"}
                >
                  {playbackState.isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6" />
                  )}
                </button>

                <button
                  onClick={playNext}
                  disabled={currentItemIndex === playlist.items.length - 1}
                  className={cn(
                    "close-btn",
                    "disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                  aria-label="Next track"
                  title="Next track"
                >
                  <SkipForward className="w-5 h-5" />
                </button>
              </div>

              {/* Volume and Settings */}
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-3 flex-1">
                  <button
                    onClick={toggleMute}
                    className="close-btn"
                    aria-label={playbackState.isMuted ? "Unmute" : "Mute"}
                    title={playbackState.isMuted ? "Unmute" : "Mute"}
                  >
                    {playbackState.isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </button>

                  <div className="flex-1 max-w-24">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={playbackState.isMuted ? 0 : playbackState.volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className={cn(
                        "w-full h-2 rounded-lg appearance-none cursor-pointer border",
                        isModernTheme
                          ? "bg-white/10 border-white/20"
                          : "bg-gray-200 border-gray-300"
                      )}
                    />
                  </div>

                  <span className={cn(
                    "text-sm min-w-[3rem] text-center",
                    isModernTheme ? "text-gray-300" : "text-gray-600"
                  )}>
                    {playbackState.isMuted ? 0 : playbackState.volume}%
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <span className={cn(
                    "text-sm",
                    isModernTheme ? "text-gray-300" : "text-gray-600"
                  )}>Speed:</span>
                  <select
                    value={playbackState.playbackRate}
                    onChange={(e) => setPlaybackRate(Number(e.target.value))}
                    className={cn(
                      "text-sm border rounded px-2 py-1 min-w-[4rem]",
                      isModernTheme
                        ? "bg-black/20 border-white/20 text-white"
                        : "bg-white border-gray-300 text-gray-900"
                    )}
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1}>1x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                    <option value={2}>2x</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Playlist Section */}
          {showPlaylist && (
            <div className="import-section">
              <h3 className="section-title">🎵 Playlist</h3>
              <p className="section-description">
                {playlist.name} • {playlist.items.length} items
              </p>

              <div className="scrollable-content">
                {playlist.items.map((item, index) => (
                  <button
                    key={item.id}
                    onClick={() => setCurrentItemIndex(index)}
                    className={cn(
                      "format-option w-full text-left mb-2 transition-all",
                      index === currentItemIndex && "active"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-8 h-8 rounded flex items-center justify-center text-xs font-medium",
                        index === currentItemIndex
                          ? isModernTheme
                            ? "bg-blue-500 text-white"
                            : "bg-blue-500 text-white"
                          : isModernTheme
                            ? "bg-white/10 text-gray-300"
                            : "bg-gray-200 text-gray-600"
                      )}>
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block truncate">
                          {item.title}
                        </span>
                        <small className={cn(
                          "block mt-1",
                          isModernTheme ? "text-gray-400" : "text-gray-500"
                        )}>
                          {item.type} • {item.duration ? formatTime(item.duration) : 'Unknown duration'}
                        </small>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
