{"timestamp": "2025-07-17T13:56:03.276Z", "summary": {"totalSessions": 0, "totalEmotionalStates": 0, "satisfactionPoints": 0, "frustrationPoints": 0, "delightMoments": 0, "satisfactionRatio": null, "delightFrequency": null}, "emotionalFlow": {"states": {}, "transitions": {}, "patterns": []}, "satisfactionAnalysis": {"totalPoints": 0, "categories": {}, "triggers": {}, "averageIntensity": 0}, "frustrationAnalysis": {"totalPoints": 0, "categories": {}, "causes": {}, "averageIntensity": 0, "resolutionRate": 0}, "delightAnalysis": {"totalMoments": 0, "categories": {}, "triggers": {}, "averageIntensity": 0, "frequency": 0}, "recommendations": [{"priority": "high", "category": "frustration", "issue": "Low frustration resolution rate", "recommendation": "Improve error recovery and user guidance systems"}]}