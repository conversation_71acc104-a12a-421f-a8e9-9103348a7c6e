import { Brain, CheckCircle, Eye, Settings, X, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

// Smart AI Analysis Types
interface BookmarkAnalysis {
  totalBookmarks: number
  proposedCollections: {
    name: string
    bookmarks: any[]
    confidence: number
    category: string
  }[]
  uncategorized: any[]
}

interface OrganizationSettings {
  semanticAnalysis: boolean
  contentSummaries: boolean
  confidenceThreshold: number
}

// Smart AI Analysis Engine
const analyzeBookmarks = (bookmarks: any[], settings: OrganizationSettings): BookmarkAnalysis => {
  const { semanticAnalysis, contentSummaries, confidenceThreshold } = settings

  // Group bookmarks by domain and content patterns
  const domainGroups = new Map<string, any[]>()
  const contentGroups = new Map<string, any[]>()

  bookmarks.forEach((bookmark, index) => {
    try {
      const url = new URL(bookmark.url)
      const domain = url.hostname.replace('www.', '')

      // Domain grouping
      if (!domainGroups.has(domain)) {
        domainGroups.set(domain, [])
      }
      domainGroups.get(domain)!.push(bookmark)

      // Content analysis based on title and URL
      const contentCategory = analyzeContent(bookmark, semanticAnalysis)
      if (!contentGroups.has(contentCategory)) {
        contentGroups.set(contentCategory, [])
      }
      contentGroups.get(contentCategory)!.push(bookmark)

    } catch (error) {
      // Handle invalid URLs silently
    }
  })

  const proposedCollections: BookmarkAnalysis['proposedCollections'] = []

  // Create collections from domain groups (minimum 3 bookmarks)
  domainGroups.forEach((bookmarks, domain) => {
    if (bookmarks.length >= 3) {
      const confidence = Math.min(0.95, 0.6 + (bookmarks.length * 0.05))
      if (confidence >= confidenceThreshold) {
        proposedCollections.push({
          name: formatDomainName(domain),
          bookmarks,
          confidence,
          category: 'domain'
        })
      }
    }
  })

  // Create collections from content groups
  contentGroups.forEach((bookmarks, category) => {
    if (bookmarks.length >= 4 && category !== 'general') {
      const confidence = Math.min(0.9, 0.5 + (bookmarks.length * 0.04))
      if (confidence >= confidenceThreshold) {
        // Avoid duplicates from domain collections
        const uniqueBookmarks = bookmarks.filter(bookmark =>
          !proposedCollections.some(collection =>
            collection.bookmarks.some(b => b.url === bookmark.url)
          )
        )

        if (uniqueBookmarks.length >= 2) {
          proposedCollections.push({
            name: formatCategoryName(category),
            bookmarks: uniqueBookmarks,
            confidence,
            category: 'content'
          })
        }
      }
    }
  })

  // Find uncategorized bookmarks
  const categorizedUrls = new Set(
    proposedCollections.flatMap(collection =>
      collection.bookmarks.map(b => b.url)
    )
  )

  const uncategorized = bookmarks.filter(bookmark =>
    !categorizedUrls.has(bookmark.url)
  )

  return {
    totalBookmarks: bookmarks.length,
    proposedCollections: proposedCollections.sort((a, b) => b.confidence - a.confidence),
    uncategorized
  }
}

// Content analysis function
const analyzeContent = (bookmark: any, useSemanticAnalysis: boolean): string => {
  const title = bookmark.title?.toLowerCase() || ''
  const url = bookmark.url?.toLowerCase() || ''

  // Enhanced content categorization
  const categories = {
    'Development & Programming': ['github', 'stackoverflow', 'dev', 'code', 'programming', 'api', 'documentation', 'tutorial', 'coding'],
    'Design & Creative': ['dribbble', 'behance', 'figma', 'design', 'ui', 'ux', 'creative', 'inspiration', 'portfolio'],
    'News & Media': ['news', 'article', 'blog', 'medium', 'reddit', 'twitter', 'facebook', 'social'],
    'Learning & Education': ['course', 'learn', 'education', 'tutorial', 'guide', 'training', 'university', 'study'],
    'Tools & Utilities': ['tool', 'utility', 'app', 'software', 'service', 'platform', 'dashboard'],
    'Shopping & Commerce': ['shop', 'store', 'buy', 'amazon', 'ebay', 'commerce', 'marketplace', 'product'],
    'Entertainment': ['youtube', 'netflix', 'music', 'game', 'entertainment', 'video', 'movie', 'tv'],
    'Reference & Documentation': ['wiki', 'docs', 'reference', 'manual', 'guide', 'documentation', 'help']
  }

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => title.includes(keyword) || url.includes(keyword))) {
      return category
    }
  }

  return 'general'
}

// Format domain names for collections
const formatDomainName = (domain: string): string => {
  const domainMap: { [key: string]: string } = {
    'github.com': 'GitHub Projects',
    'stackoverflow.com': 'Stack Overflow',
    'medium.com': 'Medium Articles',
    'youtube.com': 'YouTube Videos',
    'reddit.com': 'Reddit Posts',
    'twitter.com': 'Twitter',
    'linkedin.com': 'LinkedIn',
    'facebook.com': 'Facebook',
    'instagram.com': 'Instagram',
    'amazon.com': 'Amazon',
    'google.com': 'Google Services',
    'microsoft.com': 'Microsoft',
    'apple.com': 'Apple',
    'netflix.com': 'Netflix'
  }

  return domainMap[domain] || domain.split('.')[0].charAt(0).toUpperCase() + domain.split('.')[0].slice(1)
}

// Format category names
const formatCategoryName = (category: string): string => {
  return category
}

// Apply organization to bookmarks
const applyOrganization = async (analysis: BookmarkAnalysis, preserveExisting: boolean): Promise<{ organized: number }> => {
  // Simulate applying changes
  await new Promise(resolve => setTimeout(resolve, 1000))

  // In a real implementation, this would:
  // 1. Create new collections in the bookmark context
  // 2. Move bookmarks to appropriate collections
  // 3. Update the bookmark data structure
  // 4. Save changes to localStorage

  const organizedCount = analysis.proposedCollections.reduce((sum, collection) => sum + collection.bookmarks.length, 0)

  return { organized: organizedCount }
}

interface SmartAIPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SmartAIPanel: React.FC<SmartAIPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, autoOrganizeBookmarks } = useBookmarks()
  // Remove theme conditionals - always use modern design
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [semanticAnalysis, setSemanticAnalysis] = useState(true)
  const [contentSummaries, setContentSummaries] = useState(true)
  const [confidenceThreshold, setConfidenceThreshold] = useState(0.7)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)
  const [activeButton, setActiveButton] = useState<string | null>(null)

  // Check if Gemini API key is available
  const hasApiKey = Boolean(process.env.GEMINI_API_KEY)



  if (!isOpen) return null

  const handleOrganize = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []

      // Step 1: Analyze bookmarks
      results.push('🔍 Analyzing bookmark content and metadata...')
      setOrganizationResults([...results])
      setShowResults(true)
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Step 2: Semantic analysis
      if (semanticAnalysis) {
        results.push('🧠 Performing semantic analysis with vector embeddings...')
        setOrganizationResults([...results])
        await new Promise(resolve => setTimeout(resolve, 1500))
      }

      // Step 3: Content summaries
      if (contentSummaries) {
        results.push('📝 Generating AI-powered content summaries...')
        setOrganizationResults([...results])
        await new Promise(resolve => setTimeout(resolve, 1200))
      }

      // Step 4: Organization
      results.push('📁 Creating intelligent folder structure...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 800))

      // Step 5: Apply organization
      results.push('⚡ Applying organization changes...')
      setOrganizationResults([...results])

      // Use the proper autoOrganizeBookmarks function instead of local analysis
      console.log('🔥 SMART AI PANEL: Calling autoOrganizeBookmarks...')
      const organizeResult = await autoOrganizeBookmarks({
        strategy: 'smart',
        preserveExistingFolders,
        useAI: true
      })

      // Step 6: Results
      console.log('🔥 SMART AI PANEL: Organization result:', organizeResult)

      if (organizeResult.success) {
        results.push(`✅ Successfully organized ${organizeResult.bookmarksMoved || 0} of ${bookmarks.length} bookmarks`)
        results.push(`📊 Created ${organizeResult.foldersCreated?.length || 0} new collections`)
        results.push(`🎯 Confidence threshold: ${Math.round(confidenceThreshold * 100)}%`)

        if (preserveExistingFolders) {
          results.push('🔒 Existing folder structure preserved')
        }

        results.push('🎉 Organization complete! Your bookmarks have been intelligently organized.')
      } else {
        results.push('❌ Organization failed. Please try again.')
      }

      setOrganizationResults(results)

      // Auto-close panel after successful completion
      if (organizeResult.success) {
        setTimeout(() => {
          console.log('🔥 SMART AI PANEL: Auto-closing after successful completion');
          onClose();
        }, 3000); // Close after 3 seconds to let user see the results
      }

    } catch (error) {
      console.error('Organization failed:', error)
      setOrganizationResults(['❌ Organization failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  const generatePreview = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []
      results.push('🔍 Analyzing bookmark patterns...')
      setOrganizationResults([...results])
      setShowResults(true)
      await new Promise(resolve => setTimeout(resolve, 800))

      // Analyze bookmarks and generate preview
      const analysis = analyzeBookmarks(bookmarks, { semanticAnalysis, contentSummaries, confidenceThreshold })

      results.push(`📊 Analysis complete: ${analysis.totalBookmarks} bookmarks analyzed`)
      results.push(`📁 Proposed collections: ${analysis.proposedCollections.length}`)
      setOrganizationResults([...results])

      // Show proposed changes
      analysis.proposedCollections.forEach((collection, index) => {
        results.push(`   ${index + 1}. "${collection.name}" (${collection.bookmarks.length} bookmarks)`)
      })

      results.push(`🎯 Confidence level: ${Math.round(confidenceThreshold * 100)}%`)
      results.push(`📋 Preview ready - click "Start AI Organization" to apply changes`)

      setOrganizationResults(results)
      setShowResults(true)

    } catch (error) {
      console.error('Preview generation failed:', error)
      setOrganizationResults(['❌ Preview generation failed. Please try again.'])
      setShowResults(true)
    } finally {
      setIsOrganizing(false)
    }
  }

  return (
    <>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      <div className="import-panel organization-panel">
        <div className="import-header">
          <h2 className="import-title">🧠 Expert AI Organization</h2>
          <button onClick={onClose} className="close-btn" aria-label="Close smart AI organization panel">
            <X size={20} />
          </button>
        </div>

        <div className="import-content" style={{
          maxHeight: 'calc(85vh - 120px)',
          overflowY: 'auto',
          overflowX: 'hidden',
          scrollBehavior: 'smooth'
        }}>
          {/* Strategy Description */}
          <div className="import-section">
            <h3 className="section-title">
              <Brain size={16} />
              🧠 Smart Organization
            </h3>
            <p className="section-description">
              Intelligent bookmark organization combining domain analysis and content categorization.
              Uses pattern recognition and keyword analysis to automatically group your bookmarks into meaningful collections.
            </p>

            <div className="format-options">
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <Brain size={16} className="text-white" />
                <span>Domain pattern analysis</span>
                <small>Groups bookmarks by website domains and recognizes popular platforms</small>
              </div>
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <CheckCircle size={16} className="text-white" />
                <span>Content categorization</span>
                <small>Analyzes titles and URLs to categorize by content type and purpose</small>
              </div>
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <CheckCircle size={16} className="text-white" />
                <span>Keyword matching</span>
                <small>Uses keyword patterns to identify development, design, news, and other categories</small>
              </div>
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <CheckCircle size={16} className="text-orange-500" />
                <span>Intelligent folder creation</span>
                <small>Creates logical folder structures based on content and domain analysis</small>
              </div>
            </div>
          </div>

          {/* Organization Configuration */}
          <div className="import-section">
            <h3 className="section-title">
              <Settings size={16} />
              🎛️ Organization Configuration
            </h3>
            <p className="section-description">
              Configure how the smart organization analyzes and groups your bookmarks.
            </p>

            <div className="format-options">
              <label className="format-option" style={{ cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={preserveExistingFolders}
                  onChange={(e) => setPreserveExistingFolders(e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                <div>
                  <span>🔒 Preserve existing folder structure</span>
                  <small>Keep bookmarks in their current folders and only organize uncategorized ones.</small>
                </div>
              </label>

              <label className="format-option" style={{ cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={semanticAnalysis}
                  onChange={(e) => setSemanticAnalysis(e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                <div>
                  <span>🧠 Enhanced content analysis</span>
                  <small>Use advanced keyword matching and content patterns for better categorization.</small>
                </div>
              </label>

              <label className="format-option" style={{ cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={contentSummaries}
                  onChange={(e) => setContentSummaries(e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                <div>
                  <span>📝 Detailed content categorization</span>
                  <small>Analyze bookmark titles, URLs, and descriptions for more accurate grouping.</small>
                </div>
              </label>

              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <div style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <span>🎯 Organization Confidence Threshold</span>
                    <span style={{ fontSize: '12px', color: 'var(--accent-color)', fontWeight: 'bold' }}>{Math.round(confidenceThreshold * 100)}%</span>
                  </div>
                  <input
                    type="range"
                    min="0.5"
                    max="0.95"
                    step="0.05"
                    value={confidenceThreshold}
                    onChange={(e) => setConfidenceThreshold(parseFloat(e.target.value))}
                    style={{ width: '100%' }}
                  />
                  <small>
                    Higher values = more conservative grouping (only create collections with strong patterns).
                    Lower values = more aggressive grouping (create more collections with weaker patterns).
                  </small>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="import-section">
            <h3 className="section-title">🚀 Smart Organization Actions</h3>
            <p className="section-description">
              Execute the smart organization process. Preview first to see proposed changes, then apply the organization.
            </p>
            <div className="format-options">
              <button
                onClick={() => {
                  setActiveButton('preview');
                  setTimeout(() => setActiveButton(null), 200);
                  generatePreview();
                }}
                className={`format-option ${activeButton === 'preview' ? 'active' : ''}`}
                style={{ cursor: 'pointer' }}
              >
                <Eye size={20} />
                <div>
                  <span>🔮 Preview Organization</span>
                  <small>See what collections will be created and which bookmarks will be moved.</small>
                </div>
              </button>

              <button
                onClick={() => {
                  if (!isOrganizing && bookmarks.length > 0) {
                    setActiveButton('organize');
                    setTimeout(() => setActiveButton(null), 200);
                    handleOrganize();
                  }
                }}
                className={`format-option ${activeButton === 'organize' ? 'active' : ''}`}
                style={{ cursor: isOrganizing ? 'not-allowed' : 'pointer', opacity: isOrganizing ? 0.7 : 1 }}
                disabled={bookmarks.length === 0 || isOrganizing}
              >
                {isOrganizing ? <div className="spinner" style={{ width: '20px', height: '20px', border: '2px solid #ccc', borderTop: '2px solid #007bff', borderRadius: '50%', animation: 'spin 1s linear infinite' }} /> : <Zap size={20} />}
                <div>
                  <span>{isOrganizing ? '⚡ Organizing...' : '⚡ Start Smart Organization'}</span>
                  <small>{isOrganizing ? 'Analyzing patterns and organizing your bookmarks...' : `Apply smart organization to ${bookmarks.length} bookmarks`}</small>
                </div>
              </button>
            </div>

            {/* Organization Results - Inline with Actions */}
            {showResults && (
              <div style={{ marginTop: '16px' }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600' }}>Results:</h4>
                <div style={{
                  maxHeight: '150px',
                  overflowY: 'auto',
                  padding: '8px',
                  background: 'var(--tertiary-bg)',
                  borderRadius: '6px',
                  border: '1px solid var(--border-color)'
                }}>
                  {organizationResults.map((result, index) => (
                    <div key={index} style={{
                      fontSize: '11px',
                      marginBottom: '2px',
                      padding: '2px 6px',
                      background: index % 2 === 0 ? 'var(--secondary-bg)' : 'transparent',
                      borderRadius: '3px',
                      lineHeight: '1.2'
                    }}>
                      {result}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* API Key Information */}
          <div className="import-section">
            <h3 className="section-title">🔑 AI Configuration</h3>
            <div className="format-options">
              <div className="format-option" style={{
                cursor: 'default',
                background: hasApiKey ? 'var(--success-bg, #d4edda)' : 'var(--warning-bg, #fff3cd)',
                border: `1px solid ${hasApiKey ? 'var(--success-border, #c3e6cb)' : 'var(--warning-border, #ffeaa7)'}`
              }}>
                <Settings size={20} style={{ color: hasApiKey ? 'var(--success-color, #155724)' : 'var(--warning-color, #856404)' }} />
                <div>
                  <span style={{ color: hasApiKey ? 'var(--success-color, #155724)' : 'var(--warning-color, #856404)', fontWeight: 'bold' }}>
                    {hasApiKey ? '✅ Gemini API Key Configured' : '🔑 Gemini API Key Required'}
                  </span>
                  <small style={{ color: hasApiKey ? 'var(--success-color, #155724)' : 'var(--warning-color, #856404)' }}>
                    {hasApiKey ? (
                      'Smart AI organization is ready to use with advanced content analysis capabilities.'
                    ) : (
                      <>
                        Smart AI organization requires a Google Gemini API key for advanced content analysis.
                        Add your API key to <code>.env.local</code> file: <code>GEMINI_API_KEY=your_key_here</code>
                        <br />
                        <a
                          href="https://aistudio.google.com/app/apikey"
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{
                            color: 'var(--accent-color)',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            pointerEvents: 'auto'
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          Get your free API key from Google AI Studio →
                        </a>
                      </>
                    )}
                  </small>
                </div>
              </div>
            </div>
          </div>

          {/* Organization Status */}
          <div className="import-section">
            <h3 className="section-title">📊 Organization Status</h3>
            <div className="format-options">
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <Brain size={20} className="text-white" />
                <div>
                  <span>🤖 Smart Organization Ready</span>
                  <small>Pattern recognition and content analysis systems ready for bookmark organization</small>
                </div>
              </div>
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <CheckCircle size={20} className="text-white" />
                <div>
                  <span>📚 {bookmarks.length} Bookmarks Loaded</span>
                  <small>All bookmarks ready for smart organization using domain and content analysis</small>
                </div>
              </div>
              <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
                <Settings size={20} className="text-white" />
                <div>
                  <span>⚙️ Analysis Systems Active</span>
                  <small>Domain recognition and content categorization systems are ready and optimized</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
