{"timestamp": "2025-07-17T06:40:25.219Z", "summary": {"totalTests": 70, "passedTests": 0, "failedTests": 70, "passRate": "0.0"}, "categories": {"emotionalResponse": 0, "flowState": 10, "cognitiveLoad": 0, "performance": 0}, "detailedMetrics": {"responseTime": [{"test": "Star interaction provides immediate confident feedback", "duration": 180.91609999999991, "threshold": 200, "passed": true, "quality": "acceptable"}], "emotionalFeedback": [{"test": "Star interaction provides immediate confident feedback", "overallQuality": "excellent"}], "flowDisruption": [], "cognitiveLoad": [], "bulkOperation": [], "suggestionQuality": []}, "recommendations": [], "vibeScore": {"score": 20, "grade": "F", "interpretation": "Unacceptable user experience - complete UX overhaul required"}}