import {
  AlertCircle,
  Check,
  Chrome,
  Database,
  Download,
  FileJson,
  FileSpreadsheet,
  FileText,
  Globe,
  Info,
  X
} from 'lucide-react'
import React, { useState } from 'react'
import type { ExportFormat } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'

interface ExportPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const ExportPanel: React.FC<ExportPanelProps> = ({ isOpen, onClose }) => {
  const { exportBookmarks, filteredBookmarks, selectedBookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [exportFormat, setExportFormat] = useState<ExportFormat>('json')
  const [filename, setFilename] = useState('bookmarks_export')
  const [exportStatus, setExportStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [exportSource, setExportSource] = useState<'filtered' | 'selected'>('filtered')
  const [overwriteWarning, setOverwriteWarning] = useState(false)

  const handleExport = () => {
    try {
      // Get bookmarks to export
      const bookmarksToExport = exportSource === 'selected' && selectedBookmarks.length > 0
        ? selectedBookmarks
        : filteredBookmarks

      // Add file extension if not present
      let finalFilename = filename
      if (!finalFilename.endsWith(`.${exportFormat}`)) {
        finalFilename = `${finalFilename}.${exportFormat}`
      }

      // Export bookmarks
      exportBookmarks(exportFormat, finalFilename, bookmarksToExport)

      // Set success status
      setExportStatus('success')
      setTimeout(() => {
        setExportStatus('idle')
      }, 2000)
    } catch (error) {
      console.error('Export error:', error)
      setExportStatus('error')
      setTimeout(() => {
        setExportStatus('idle')
      }, 3000)
    }
  }

  const getFormatExtension = (format: ExportFormat) => {
    switch (format) {
      case 'html': return '.html'
      case 'json': return '.json'
      case 'csv': return '.csv'
    }
  }

  const handleFilenameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilename(e.target.value)

    // Check if file with this name might already exist
    // This is just a simple check that could be enhanced
    const fileWithExtension = e.target.value + getFormatExtension(exportFormat)
    const commonNames = ['bookmarks', 'bookmarks_export', 'favorites', 'export']
    setOverwriteWarning(commonNames.includes(e.target.value))
  }

  if (!isOpen) return null

  return (
    <div className="import-panel export-panel"> {/* Using unified panel design system */}
      <div className="import-header">
        <h2 className="import-title">{t('export.title')}</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close export panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content">
        {/* Format Selection */}
        <div className="import-section">
          <h3 className="section-title">Select Format</h3>
          <div className="format-options">
            <button
              onClick={() => setExportFormat('json')}
              className={`format-option ${exportFormat === 'json' ? 'active' : ''}`}
            >
              <FileJson size={20} />
              <span>JSON</span>
              <small>Standard format with all data</small>
            </button>
            <button
              onClick={() => setExportFormat('html')}
              className={`format-option ${exportFormat === 'html' ? 'active' : ''}`}
            >
              <FileText size={20} />
              <span>HTML</span>
              <small>Browser-compatible format</small>
            </button>
            <button
              onClick={() => setExportFormat('csv')}
              className={`format-option ${exportFormat === 'csv' ? 'active' : ''}`}
            >
              <FileSpreadsheet size={20} />
              <span>CSV</span>
              <small>Spreadsheet format</small>
            </button>
          </div>
        </div>

        {/* Export Options */}
        <div className="import-section">
          <h3 className="section-title">Export Options</h3>
          <p className="section-description">
            Configure your export settings and choose which bookmarks to export.
          </p>

          <div className="filename-input">
            <label htmlFor="filename" className="upload-text">Filename</label>
            <div className="filename-container">
              <input
                id="filename"
                type="text"
                value={filename}
                onChange={handleFilenameChange}
                className="filename-field"
                placeholder="Enter filename"
              />
              <span className="file-extension">
                {getFormatExtension(exportFormat)}
              </span>
            </div>

            {overwriteWarning && (
              <div className="upload-hint warning">
                <Info size={14} />
                <span>Files with this name may already exist and will be overwritten.</span>
              </div>
            )}
          </div>

          <div className="export-source">
            <label className="upload-text">Export Source</label>
            <div className="format-options">
              <button
                onClick={() => setExportSource('filtered')}
                className={`format-option ${exportSource === 'filtered' ? 'active' : ''}`}
              >
                <Database size={20} />
                <span>Filtered Bookmarks</span>
                <small>{filteredBookmarks.length} bookmarks</small>
              </button>

              <button
                onClick={() => setExportSource('selected')}
                disabled={selectedBookmarks.length === 0}
                className={`format-option ${exportSource === 'selected' ? 'active' : ''} ${selectedBookmarks.length === 0 ? 'disabled' : ''}`}
              >
                <Check size={20} />
                <span>Selected Bookmarks</span>
                <small>{selectedBookmarks.length} bookmarks</small>
              </button>
            </div>
          </div>
        </div>

        {/* Export Action */}
        <div className="import-section">
          <h3 className="section-title">{t('export.title')}</h3>
          <div className="export-area">
            {exportStatus === 'idle' && (
              <>
                <Download size={32} className="upload-icon" />
                <p className="upload-text">Ready to export your bookmarks</p>
                <p className="upload-hint">
                  {exportSource === 'selected' && selectedBookmarks.length > 0
                    ? `${selectedBookmarks.length} selected bookmark${selectedBookmarks.length !== 1 ? 's' : ''} will be exported`
                    : `${filteredBookmarks.length} filtered bookmark${filteredBookmarks.length !== 1 ? 's' : ''} will be exported`}
                </p>
                <button
                  onClick={handleExport}
                  className="import-another-btn"
                  disabled={filteredBookmarks.length === 0 && selectedBookmarks.length === 0}
                >
                  <Download size={16} />
                  Export Bookmarks
                </button>
              </>
            )}

            {exportStatus === 'success' && (
              <>
                <Check size={32} className="success-icon" />
                <p className="upload-text">Export completed successfully!</p>
                <p className="upload-hint">
                  Your bookmarks have been downloaded to your device.
                </p>
                <button
                  onClick={() => setExportStatus('idle')}
                  className="import-another-btn"
                >
                  📁 Export Another File
                </button>
              </>
            )}

            {exportStatus === 'error' && (
              <>
                <AlertCircle size={32} className="error-icon" />
                <p className="upload-text">Export failed. Please try again.</p>
              </>
            )}
          </div>
        </div>

        {/* Format Information */}
        <div className="import-section">
          <h3 className="section-title">Format Details</h3>
          <p className="section-description">
            Learn about the different export formats and their use cases.
          </p>
          <div className="browser-instructions">
            {exportFormat === 'json' && (
              <div className="instruction-item">
                <FileJson size={16} />
                <span>JSON format includes full metadata, tags, and collections. Best for backup and data transfer.</span>
              </div>
            )}

            {exportFormat === 'html' && (
              <div className="instruction-item">
                <FileText size={16} />
                <span>HTML format is browser-compatible and can be imported into Chrome, Firefox, and Safari.</span>
              </div>
            )}

            {exportFormat === 'csv' && (
              <div className="instruction-item">
                <FileSpreadsheet size={16} />
                <span>CSV format is ideal for spreadsheet applications and data analysis tools.</span>
              </div>
            )}
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="import-section">
          <h3 className="section-title">Import to Browser</h3>
          <div className="browser-instructions">
            <div className="instruction-item">
              <Chrome size={16} />
              <span>Chrome: Settings → Bookmarks → Bookmark manager → Import bookmarks</span>
            </div>
            <div className="instruction-item">
              <Globe size={16} />
              <span>Firefox: Library → Bookmarks → Show All Bookmarks → Import</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}