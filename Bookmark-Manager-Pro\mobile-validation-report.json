{"timestamp": "2025-07-18T10:31:33.973Z", "overall": {"passed": 50, "failed": 3, "score": 9}, "categories": {"responsive": {"tests": [{"testName": "File exists: src/styles/responsive-foundation.css", "passed": true, "details": "File found", "timestamp": "2025-07-18T10:31:34.011Z"}, {"testName": "File exists: src/components/ResponsiveHeader.tsx", "passed": true, "details": "File found", "timestamp": "2025-07-18T10:31:34.012Z"}, {"testName": "File exists: src/components/ResponsiveHeader.css", "passed": true, "details": "File found", "timestamp": "2025-07-18T10:31:34.012Z"}, {"testName": "File exists: src/components/EnhancedSearchInput.tsx", "passed": true, "details": "File found", "timestamp": "2025-07-18T10:31:34.012Z"}, {"testName": "File exists: src/components/EnhancedSearchInput.css", "passed": true, "details": "File found", "timestamp": "2025-07-18T10:31:34.012Z"}, {"testName": "Breakpoint defined: --breakpoint-xs: 320px", "passed": true, "details": "Breakpoint found", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Breakpoint defined: --breakpoint-sm: 480px", "passed": true, "details": "Breakpoint found", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Breakpoint defined: --breakpoint-md: 640px", "passed": true, "details": "Breakpoint found", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Breakpoint defined: --breakpoint-lg: 768px", "passed": true, "details": "Breakpoint found", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Breakpoint defined: --breakpoint-xl: 1024px", "passed": true, "details": "Breakpoint found", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Mobile-first query: @media (min-width: 480px)", "passed": true, "details": "Mobile-first approach confirmed", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Mobile-first query: @media (min-width: 640px)", "passed": true, "details": "Mobile-first approach confirmed", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Mobile-first query: @media (min-width: 768px)", "passed": true, "details": "Mobile-first approach confirmed", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Header component: interface ResponsiveHeaderProps", "passed": true, "details": "Component feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Header component: useState", "passed": true, "details": "Component feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Header component: useEffect", "passed": true, "details": "Component feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Header component: onSearch", "passed": true, "details": "Component feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Header component: searchQuery", "passed": true, "details": "Component feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Header component: isSearching", "passed": false, "details": "Missing required component feature", "timestamp": "2025-07-18T10:31:34.015Z"}], "passed": 18, "failed": 1}, "touchTargets": {"tests": [{"testName": "Touch target variable: --touch-target-min: 44px", "passed": true, "details": "Variable defined", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Touch target variable: --touch-target-comfortable: 48px", "passed": true, "details": "Variable defined", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Touch target variable: --touch-target-large: 56px", "passed": true, "details": "Variable defined", "timestamp": "2025-07-18T10:31:34.014Z"}, {"testName": "Search component: interface EnhancedSearchInputProps", "passed": true, "details": "Search feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Search component: recentSearches", "passed": true, "details": "Search feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Search component: suggestions", "passed": true, "details": "Search feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Search component: onSubmit", "passed": true, "details": "Search feature found", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Search component: isLoading", "passed": false, "details": "Missing search component feature", "timestamp": "2025-07-18T10:31:34.015Z"}, {"testName": "Search component: size?:", "passed": true, "details": "Search feature found", "timestamp": "2025-07-18T10:31:34.015Z"}], "passed": 8, "failed": 1}, "performance": {"tests": [{"testName": "Performance optimization: will-change", "passed": true, "details": "Performance optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "Performance optimization: transform: translateZ(0)", "passed": true, "details": "Performance optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "Performance optimization: backface-visibility: hidden", "passed": true, "details": "Performance optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "Performance optimization: @media print", "passed": true, "details": "Performance optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "React optimization: useCallback", "passed": true, "details": "React optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "React optimization: useMemo", "passed": true, "details": "React optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "React optimization: debounce", "passed": true, "details": "React optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}, {"testName": "React optimization: useEffect", "passed": true, "details": "React optimization found", "timestamp": "2025-07-18T10:31:34.017Z"}], "passed": 8, "failed": 0}, "accessibility": {"tests": [{"testName": "A11y feature: @media (prefers-reduced-motion: reduce)", "passed": true, "details": "Accessibility feature implemented", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "A11y feature: @media (prefers-contrast: high)", "passed": true, "details": "Accessibility feature implemented", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "A11y feature: outline: 2px solid", "passed": true, "details": "Accessibility feature implemented", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "A11y feature: outline-offset", "passed": true, "details": "Accessibility feature implemented", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "A11y feature: :focus", "passed": true, "details": "Accessibility feature implemented", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "ARIA feature: aria-label", "passed": true, "details": "ARIA attribute found", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "ARIA feature: aria-expanded", "passed": false, "details": "Missing ARIA attribute", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "ARIA feature: role=", "passed": true, "details": "ARIA attribute found", "timestamp": "2025-07-18T10:31:34.016Z"}, {"testName": "ARIA feature: tabIndex", "passed": true, "details": "ARIA attribute found", "timestamp": "2025-07-18T10:31:34.016Z"}], "passed": 8, "failed": 1}, "usability": {"tests": [{"testName": "Mobile interaction: touch-action", "passed": true, "details": "Mobile interaction optimized", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Mobile interaction: user-select", "passed": true, "details": "Mobile interaction optimized", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Mobile interaction: -webkit-tap-highlight-color", "passed": true, "details": "Mobile interaction optimized", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Mobile interaction: cursor: pointer", "passed": true, "details": "Mobile interaction optimized", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Responsive typography: font-size: var(--text-", "passed": true, "details": "Responsive typography implemented", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Responsive typography: line-height", "passed": true, "details": "Responsive typography implemented", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Responsive typography: letter-spacing", "passed": true, "details": "Responsive typography implemented", "timestamp": "2025-07-18T10:31:34.018Z"}, {"testName": "Responsive typography: @media (min-width:", "passed": true, "details": "Responsive typography implemented", "timestamp": "2025-07-18T10:31:34.018Z"}], "passed": 8, "failed": 0}}}