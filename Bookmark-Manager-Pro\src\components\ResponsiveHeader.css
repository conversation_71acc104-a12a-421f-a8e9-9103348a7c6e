/* ==========================================================================
   RESPONSIVE HEADER - Mobile-First Design
   Enhanced for 9/10 Mobile Friendliness Score
   ========================================================================== */

/* Import responsive foundation */
@import '../styles/responsive-foundation.css';

/* ==========================================================================
   HEADER BASE STYLES (320px+)
   ========================================================================== */

.responsive-header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-areas: "menu brand actions";
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--header-bg, #ffffff);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  min-height: 60px;
  transition: all var(--transition-fast) ease;
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  will-change: transform, opacity;
}

/* ==========================================================================
   MENU BUTTON (Mobile)
   ========================================================================== */

.responsive-header__menu-btn {
  grid-area: menu;
  background: none;
  border: none;
  color: var(--text-color, #374151);
  padding: var(--spacing-xs);
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: var(--touch-target-minimum);
  min-height: var(--touch-target-minimum);
}

.responsive-header__menu-btn:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--primary-color, #3b82f6);
}

.responsive-header__menu-btn:active {
  background: var(--active-bg, #e5e7eb);
  transform: scale(0.95);
}

.responsive-header__menu-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* ==========================================================================
   BRAND/LOGO SECTION
   ========================================================================== */

.responsive-header__brand {
  grid-area: brand;
  display: flex;
  align-items: center;
  min-width: 0; /* Allow shrinking */
}

.responsive-header__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--primary-color, #3b82f6);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive title display */
.responsive-header__title-full {
  display: none;
}

.responsive-header__title-short {
  display: none;
}

.responsive-header__title-icon {
  display: inline;
  font-size: var(--text-xl);
}

/* ==========================================================================
   SEARCH SECTION - MOBILE FIRST
   ========================================================================== */

.responsive-header__search-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--header-bg, #ffffff);
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.responsive-header__search-section--expanded {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.responsive-header__search-section--focused {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Search Icon (Mobile Trigger) */
.responsive-header__search-icon {
  grid-area: actions;
  background: none;
  border: none;
  color: var(--text-color, #374151);
  padding: var(--spacing-xs);
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsive-header__search-icon:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--primary-color, #3b82f6);
}

.responsive-header__search-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* Search Form */
.responsive-header__search-form {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  max-width: 100%;
}

.responsive-header__search-input-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.responsive-header__search-input {
  width: 100%;
  height: var(--touch-target-comfortable);
  padding: 0 var(--spacing-md);
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  font-size: var(--text-base);
  background: var(--input-bg, #ffffff);
  color: var(--text-color, #374151);
  transition: all 0.2s ease;
  outline: none;
}

.responsive-header__search-input:focus {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.responsive-header__search-input::placeholder {
  color: var(--placeholder-color, #9ca3af);
}

/* Clear Button */
.responsive-header__clear-btn {
  position: absolute;
  right: var(--spacing-xs);
  background: none;
  border: none;
  color: var(--text-secondary, #6b7280);
  padding: var(--spacing-xs);
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsive-header__clear-btn:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--text-color, #374151);
}

.responsive-header__clear-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* Search Submit Button */
.responsive-header__search-submit {
  background: var(--primary-color, #3b82f6);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0 var(--spacing-md);
  height: var(--touch-target-comfortable);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
}

.responsive-header__search-submit:hover {
  background: var(--primary-hover, #2563eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.responsive-header__search-submit:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.responsive-header__search-submit-text {
  display: none;
}

.responsive-header__search-submit-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

/* Advanced Search Button */
.responsive-header__advanced-search {
  background: var(--secondary-bg, #f8fafc);
  color: var(--text-color, #374151);
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  padding: 0 var(--spacing-sm);
  height: var(--touch-target-comfortable);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
}

.responsive-header__advanced-search:hover {
  background: var(--hover-bg, #f3f4f6);
  border-color: var(--primary-color, #3b82f6);
  color: var(--primary-color, #3b82f6);
}

.responsive-header__advanced-search-text {
  display: none;
}

.responsive-header__advanced-search-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

/* ==========================================================================
   ACTION BUTTONS
   ========================================================================== */

.responsive-header__actions {
  grid-area: actions;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.responsive-header__action-btn,
.responsive-header__profile-btn {
  background: none;
  border: none;
  color: var(--text-color, #374151);
  padding: var(--spacing-xs);
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsive-header__action-btn:hover,
.responsive-header__profile-btn:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--primary-color, #3b82f6);
}

.responsive-header__action-btn svg,
.responsive-header__profile-btn svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.responsive-header__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--avatar-bg, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Search Overlay */
.responsive-header__search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* ==========================================================================
   ENHANCED MOBILE (480px+)
   ========================================================================== */

@media (min-width: 480px) {
  .responsive-header {
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-md);
    min-height: 64px;
  }

  /* Show short title */
  .responsive-header__title-icon {
    display: none;
  }

  .responsive-header__title-short {
    display: inline;
  }

  /* Enhanced search form */
  .responsive-header__search-submit-text {
    display: inline;
  }

  .responsive-header__advanced-search-text {
    display: inline;
  }

  /* Larger touch targets */
  .responsive-header__search-input {
    height: var(--touch-target-large);
  }

  .responsive-header__search-submit,
  .responsive-header__advanced-search {
    height: var(--touch-target-large);
  }
}

/* ==========================================================================
   SMALL TABLET (640px+)
   ========================================================================== */

@media (min-width: 640px) {
  .responsive-header {
    grid-template-columns: auto 1fr auto;
    grid-template-areas: "menu brand search";
    padding: var(--spacing-lg) var(--spacing-xl);
    gap: var(--spacing-lg);
  }

  /* Show full title */
  .responsive-header__title-short {
    display: none;
  }

  .responsive-header__title-full {
    display: inline;
  }

  /* Inline search section */
  .responsive-header__search-section {
    position: static;
    grid-area: search;
    background: none;
    transform: none;
    opacity: 1;
    visibility: visible;
    padding: 0;
    max-width: 400px;
    justify-self: end;
  }

  .responsive-header__search-section--expanded {
    transform: none;
  }

  /* Hide mobile search icon */
  .responsive-header__search-icon {
    display: none;
  }

  /* Enhanced search form layout */
  .responsive-header__search-form {
    max-width: none;
  }

  .responsive-header__search-input {
    min-width: 200px;
  }

  /* Move actions to separate area */
  .responsive-header__actions {
    grid-area: actions;
    justify-self: end;
  }
}

/* ==========================================================================
   TABLET (768px+)
   ========================================================================== */

@media (min-width: 768px) {
  .responsive-header {
    grid-template-columns: auto 1fr auto auto;
    grid-template-areas: "menu brand search actions";
    padding: var(--spacing-lg) var(--spacing-2xl);
    gap: var(--spacing-xl);
    min-height: 72px;
  }

  /* Hide mobile menu on larger screens */
  .responsive-header__menu-btn {
    display: none;
  }

  .responsive-header {
    grid-template-areas: "brand search actions";
    grid-template-columns: auto 1fr auto;
  }

  /* Enhanced search section */
  .responsive-header__search-section {
    max-width: 500px;
  }

  .responsive-header__search-input {
    min-width: 250px;
  }

  /* Show all action buttons */
  .responsive-header__actions {
    gap: var(--spacing-sm);
  }

  /* Enhanced typography */
  .responsive-header__title {
    font-size: var(--text-xl);
  }
}

/* ==========================================================================
   DESKTOP (1024px+)
   ========================================================================== */

@media (min-width: 1024px) {
  .responsive-header {
    padding: var(--spacing-xl) var(--spacing-3xl);
    gap: var(--spacing-2xl);
    min-height: 80px;
  }

  /* Enhanced search section */
  .responsive-header__search-section {
    max-width: 600px;
  }

  .responsive-header__search-input {
    min-width: 300px;
    font-size: var(--text-lg);
  }

  /* Enhanced button sizes */
  .responsive-header__search-submit,
  .responsive-header__advanced-search {
    padding: 0 var(--spacing-lg);
    font-size: var(--text-base);
  }

  /* Enhanced typography */
  .responsive-header__title {
    font-size: var(--text-2xl);
  }

  /* Enhanced action buttons */
  .responsive-header__action-btn svg,
  .responsive-header__profile-btn svg {
    width: 24px;
    height: 24px;
  }

  .responsive-header__avatar {
    width: 36px;
    height: 36px;
  }
}

/* ==========================================================================
   LARGE DESKTOP (1280px+)
   ========================================================================== */

@media (min-width: 1280px) {
  .responsive-header {
    padding: var(--spacing-xl) var(--spacing-4xl);
    gap: var(--spacing-3xl);
  }

  /* Enhanced search section */
  .responsive-header__search-section {
    max-width: 700px;
  }

  .responsive-header__search-input {
    min-width: 350px;
  }
}

/* ==========================================================================
   DARK MODE SUPPORT
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  .responsive-header {
    --header-bg: #1f2937;
    --border-color: #374151;
    --text-color: #f9fafb;
    --text-secondary: #9ca3af;
    --hover-bg: #374151;
    --active-bg: #4b5563;
    --input-bg: #374151;
    --placeholder-color: #6b7280;
    --secondary-bg: #374151;
    --avatar-bg: #4b5563;
  }
}

/* ==========================================================================
   HIGH CONTRAST MODE
   ========================================================================== */

@media (prefers-contrast: high) {
  .responsive-header__search-input {
    border-width: 3px;
  }

  .responsive-header__search-submit,
  .responsive-header__advanced-search {
    border: 2px solid currentColor;
  }

  .responsive-header__action-btn,
  .responsive-header__profile-btn,
  .responsive-header__menu-btn {
    border: 1px solid currentColor;
  }
}

/* ==========================================================================
   REDUCED MOTION
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .responsive-header,
  .responsive-header__search-section,
  .responsive-header__search-input,
  .responsive-header__search-submit,
  .responsive-header__advanced-search,
  .responsive-header__action-btn,
  .responsive-header__profile-btn,
  .responsive-header__menu-btn,
  .responsive-header__clear-btn {
    transition: none;
  }

  .responsive-header__search-submit:hover,
  .responsive-header__action-btn:active,
  .responsive-header__menu-btn:active {
    transform: none;
  }

  .responsive-header__search-overlay {
    animation: none;
  }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
  .responsive-header {
    position: static;
    box-shadow: none;
    border-bottom: 2px solid #000;
    background: #fff;
    color: #000;
  }

  .responsive-header__search-section,
  .responsive-header__actions,
  .responsive-header__menu-btn {
    display: none;
  }

  .responsive-header {
    grid-template-areas: "brand";
    grid-template-columns: 1fr;
    justify-items: center;
  }
}