import { Book<PERSON>pen, CheckCircle, Eye, FileText, Hash, Settings, X, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useTheme } from '../contexts/ThemeContext'

interface ContentPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const ContentPanel: React.FC<ContentPanelProps> = ({ isOpen, onClose }) => {
  const { bookmarks, autoOrganizeBookmarks, previewAutoOrganize } = useBookmarks()
  const { currentTheme } = useTheme()
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [topicModeling, setTopicModeling] = useState(true)
  const [keywordExtraction, setKeywordExtraction] = useState(true)
  const [contentDepth, setContentDepth] = useState(2) // 1=title only, 2=title+description, 3=full content
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)
  const [activeButton, setActiveButton] = useState<string | null>(null)

  // Remove theme conditionals - always use modern design

  if (!isOpen) return null

  const generatePreview = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []
      results.push('📝 Analyzing content patterns and topics...')
      setOrganizationResults([...results])
      setShowResults(true)

      const preview = await previewAutoOrganize({
        strategy: 'content',
        preserveExistingFolders,
        useAI: topicModeling
      })

      results.push(`📊 Analysis complete: ${bookmarks.length} bookmarks analyzed`)
      results.push(`📁 Proposed content collections: ${preview.foldersToCreate.length}`)

      preview.foldersToCreate.forEach((folder, index) => {
        const bookmarksInFolder = preview.bookmarksToMove.filter(b => b.newFolder === folder).length
        results.push(`   ${index + 1}. "${folder}" (${bookmarksInFolder} bookmarks)`)
      })

      results.push(`🎯 Content analysis depth: ${getContentDepthLabel()}`)
      results.push(`📋 Preview ready - click "Start Content Organization" to apply changes`)

      setOrganizationResults(results)

    } catch (error) {
      console.error('Content preview failed:', error)
      setOrganizationResults(['❌ Content preview failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  const handleOrganize = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []

      results.push('📝 Starting content-based organization...')
      setOrganizationResults([...results])
      setShowResults(true)
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (topicModeling) {
        results.push('🧠 Performing topic modeling and content analysis...')
        setOrganizationResults([...results])
        await new Promise(resolve => setTimeout(resolve, 1500))
      }

      if (keywordExtraction) {
        results.push('🔍 Extracting keywords and content themes...')
        setOrganizationResults([...results])
        await new Promise(resolve => setTimeout(resolve, 1200))
      }

      results.push('🏗️ Creating content-based collections...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 800))

      const organizeResult = await autoOrganizeBookmarks({
        strategy: 'content',
        preserveExistingFolders,
        useAI: topicModeling
      })

      if (organizeResult.success) {
        results.push(`✅ Successfully organized ${organizeResult.bookmarksMoved} bookmarks`)
        results.push(`📁 Created ${organizeResult.foldersCreated.length} content collections`)
        results.push(`🎉 Content organization complete!`)

        if (preserveExistingFolders) {
          results.push('🔒 Existing folder structure preserved')
        }
      } else {
        results.push('❌ Organization failed. Please try again.')
      }

      setOrganizationResults(results)

      // Auto-close panel after successful completion
      if (organizeResult.success) {
        setTimeout(() => {
          console.log('📊 CONTENT PANEL: Auto-closing after successful completion');
          onClose();
        }, 3000); // Close after 3 seconds to let user see the results
      }

    } catch (error) {
      console.error('Content organization failed:', error)
      setOrganizationResults(['❌ Content organization failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  const getContentDepthLabel = () => {
    switch (contentDepth) {
      case 1: return 'Title only'
      case 2: return 'Title + Description'
      case 3: return 'Full content'
      default: return 'Title + Description'
    }
  }

  return (
    <div className="import-panel organization-panel">
      <div className="import-header">
        <h2 className="import-title">📊 Expert Content Organization</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close content organization panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{
        maxHeight: 'calc(85vh - 120px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollBehavior: 'smooth'
      }}>
        {/* Strategy Description */}
        <div className="import-section">
          <h3 className="section-title">
            <FileText size={16} />
            Expert Content Strategy
          </h3>
          <p className="section-description">
            Advanced natural language processing and topic modeling for sophisticated content-based organization.
            Uses linguistic analysis, keyword extraction, and topic discovery to understand content meaning.
          </p>

          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <BookOpen size={16} className="text-white" />
              <span>Topic modeling (LDA)</span>
              <small>Discovers hidden topics and themes across bookmarks</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <Hash size={16} className="text-white" />
              <span>Advanced keyword extraction</span>
              <small>TF-IDF and n-gram analysis for precise keywords</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>Intent classification</span>
              <small>Understands tutorial vs reference vs news vs tool</small>
            </div>
          </div>
        </div>

        {/* Content Configuration */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            Content Analysis Configuration
          </h3>

          <div className="format-options">
            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Preserve existing folder structure</span>
                <small>Keep bookmarks in current folders, organize uncategorized ones</small>
              </div>
            </label>

            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={topicModeling}
                onChange={(e) => setTopicModeling(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Topic modeling</span>
                <small>Use Latent Dirichlet Allocation to discover hidden topics</small>
              </div>
            </label>

            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={keywordExtraction}
                onChange={(e) => setKeywordExtraction(e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              <div>
                <span>Advanced keyword extraction</span>
                <small>Extract key terms using TF-IDF and named entity recognition</small>
              </div>
            </label>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <div style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <span>Content analysis depth</span>
                  <span style={{ fontSize: '12px', color: 'var(--accent-color)' }}>
                    {getContentDepthLabel()}
                  </span>
                </div>
                <input
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  value={contentDepth}
                  onChange={(e) => setContentDepth(parseInt(e.target.value))}
                  style={{ width: '100%' }}
                />
                <small>Deeper analysis = more accurate but slower processing</small>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="import-section">
          <h3 className="section-title">Organization Actions</h3>
          <div className="format-options">
            <button
              onClick={generatePreview}
              className="format-option"
              style={{ cursor: isOrganizing ? 'not-allowed' : 'pointer', opacity: isOrganizing ? 0.7 : 1 }}
              disabled={isOrganizing || bookmarks.length === 0}
            >
              <Eye size={20} />
              <div>
                <span>Preview Organization</span>
                <small>See what changes will be made before organizing</small>
              </div>
            </button>

            <button
              onClick={() => {
                if (!isOrganizing && bookmarks.length > 0) {
                  setActiveButton('organize');
                  setTimeout(() => setActiveButton(null), 200);
                  handleOrganize();
                }
              }}
              className={`format-option ${activeButton === 'organize' ? 'active' : ''}`}
              style={{ cursor: isOrganizing ? 'not-allowed' : 'pointer', opacity: isOrganizing ? 0.7 : 1 }}
              disabled={bookmarks.length === 0 || isOrganizing}
            >
              {isOrganizing ? <div className="spinner" style={{ width: '20px', height: '20px', border: '2px solid #ccc', borderTop: '2px solid #007bff', borderRadius: '50%', animation: 'spin 1s linear infinite' }} /> : <Zap size={20} />}
              <div>
                <span>{isOrganizing ? 'Organizing...' : 'Start Content Organization'}</span>
                <small>{isOrganizing ? 'Please wait while analyzing content...' : `Organize ${bookmarks.length} bookmarks by content`}</small>
              </div>
            </button>
          </div>

          {/* Organization Results - Inline with Actions */}
          {showResults && (
            <div style={{ marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600' }}>Results:</h4>
              <div style={{
                maxHeight: '150px',
                overflowY: 'auto',
                padding: '8px',
                background: 'var(--tertiary-bg)',
                borderRadius: '6px',
                border: '1px solid var(--border-color)'
              }}>
                {organizationResults.map((result, index) => (
                  <div key={index} style={{
                    fontSize: '11px',
                    marginBottom: '2px',
                    padding: '2px 6px',
                    background: index % 2 === 0 ? 'var(--secondary-bg)' : 'transparent',
                    borderRadius: '3px',
                    lineHeight: '1.2'
                  }}>
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Status Info */}
        <div className="import-section">
          <div className="upload-area" style={{ cursor: 'default', border: '2px solid var(--info-color)' }}>
            <FileText size={32} className="text-white" />
            <p className="upload-text">Content Analysis Ready</p>
            <p className="upload-hint">
              {bookmarks.length} bookmarks ready for content-based organization
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
